import { Request, Response } from 'express';
export default {
  'GET /api/friendTask': (req: Request, res: Response) => {
    res.send({
      jobName: "Annual Report Analysis",
      departmentName: "Data Analytics",
      creator: "<PERSON>",
      distributionType: 1,
      departmentId: "dept12345",
      creatorName: "<PERSON>",
      updateTime: "2024-07-12T08:00:00Z",
      executeTimes: 5,
      retryTimes: 2,
      jobId: "job98765",
      executeType: 0,
      firmId: 123,
      jobUuid: "uuid-98765",
      createTime: "2024-06-30T08:00:00Z",
      processDetails: [
        {
          processOrder: 1,
          processName: "Data Collection",
          processUuid: "proc-uuid-001",
          processVersion: "v1.0",
          processDetailUuid: "proc-det-uuid-001",
          processChannel: "Channel A",
        },
        {
          processOrder: 2,
          processName: "Data Cleaning",
          processUuid: "proc-uuid-002",
          processVersion: "v1.1",
          processDetailUuid: "proc-det-uuid-002",
          processChannel: "Channel B",
        },
        {
          processOrder: 3,
          processName: "Data Analysis",
          processUuid: "proc-uuid-003",
          processVersion: "v2.0",
          processDetailUuid: "proc-det-uuid-003",
          processChannel: "Channel C",
        },
      ],
      jobDescription: "This task involves analyzing the annual report data for insights.",
      firmName: "Tech Solutions Inc.",
      isGroup: true,
      status: 1,
      isSchedule: true,
    });
  },
  'POST/api/runRecordList': (req: Request, res: Response) => {
    res.send({
        total: '100',
        pages: '10',
        size: '10',
        current: '1',
        records: [{
            accountId: 'acc123',
            accountName: 'John Doe',
            createTime: '2024-07-12T08:00:00Z',
            friendBusStatus: 1,
            hasClearLog: 'true',
            hasClearVideo: 'false',
            hasLog: 'true',
            hasRecord: 'true',
            isReference: 'false',
            machineIp: '***********',
            machineName: 'Server01',
            runTime: '3600',
            status: 0,
            template: 'template01',
            workId: 'work123',
            workUuid: 'uuid-123',
          }],
      })
},
}

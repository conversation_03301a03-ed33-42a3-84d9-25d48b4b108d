import { defineConfig } from 'umi';
import routes from './routes';
import proxy from './proxy';

const { API_ENV, PORT } = process.env;

// 主应用
const entryMainURL = () => {
  switch (API_ENV) {
    case 'local':
      return '//localhost:3002/remote.js';
    default:
      return '/main-fe/remote.js';
  }
};

// 提取变量是为了和 MFSU 配合使用保持配置一致
const shared = {
  // umi: {
  //   singleton: true,
  //   eager: true,
  // },
  react: {
    singleton: true,
    eager: true,
  },
  'react-dom': {
    singleton: true,
    eager: true,
  },
};

// 提取变量是为了和 MFSU 配合使用保持配置一致
/**  publicPath 需要根据环境配置地址 publicPath: 'http://localhost:3001/', dev: http://**********:3001/ **** */
const name = 'deopCmd';
export default defineConfig({
  // devtool: 'cheap-module-source-map',
  hash: true,
  antd: {},
  model: {},
  // publicPath: 'http://localhost:3001/',
  // publicPath: 'http://**********:3001/',
  // runtimePublicPath: true,
  initialState: {},
  alias: {
    '@deopCmd': '/src',
  },
  theme: {
    'root-entry-name': 'variable',
    /** 注：主题色, 三级文本，成功色等因为涉及fade编译的错误，不在此露出 */
    // // 主题色
    // 'primary-color': 'var(--ant-primary-color)',
    // // 三级文本
    // 'text-color-secondary': 'var(--third-font-color)',
    // // 成功色
    // 'success-color': 'var(--success-color)',
    // // 警告色
    // 'warning-color': 'var(--info-color)',
    // // 错误色
    // 'error-color': 'var(--error-color)',
    // 链接色
    'link-color': 'var(--ant-link-color)',
    // 边框色
    'border-color-base': 'var(--ant-border-color-base)',
    // 组合搜索框背景色
    '@input-addon-bg': 'var(--piece-bg-color)',
    // 下拉选择默认的颜色
    '@select-item-selected-bg': 'var(--piece-bg-color)',
    // menu选中背景
    '@menu-item-active-bg': 'var(--piece-bg-color)',
    // 组合搜索框下拉hover背景色
    '@select-item-active-bg': 'var(--piece-bg-color)',
    // 树
    '@tree-node-selected-bg': 'var(--piece-bg-color)',
    // form表单label
    'heading-color': 'var(--ant-heading-color)',
    // form表单正文
    'text-color': 'var(--first-font-color)',
    // // radio框颜色
    // '@radio-dot-color': '#2f9aff',
    // // checked框颜色
    // '@checkbox-color': '#2f9aff',
    // 组件/浮层圆角
    'border-radius-base': '4px',
    // 输入框等高度
    '@height-base': '36px',
    // 主字号
    'font-size-base': '14px',
    // Menu选中border宽度
    '@menu-item-active-border-width': '0px',
  },
  request: {
    // @ts-ignore
    // dataField: 'data',
  },
  targets: {
    ie: 11,
    chrome: 67,
  },
  jsMinifier: 'terser',
  define: {
    'process.env': {
      API_ENV: API_ENV,
      SYSTEM_BIZNAME: name,
    },
  },
  fastRefresh: true,
  dva: {
    // 不再支持 hmr 这个参数
  },
  // @ts-ignore
  proxy: proxy[API_ENV || 'dev'],
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  extraBabelPlugins: [],
  // API_ENV === 'prod' ? ['babel-plugin-dynamic-import-node'] : [],
  mf: {
    name,
    remotes: [
      // 主应用
      {
        name: 'main',
        entry: entryMainURL(),
      },
    ],
    shared,
  },
  mfsu: false,
  layout: {
    title: '子项目',
  },
  routes,
  //favicons: ['/assets/imgages/favicon.ico'],
  npmClient: 'yarn',
  headScripts: [''],
  // @ts-ignore
  chainWebpack: (memo: any) => {
    memo.output.set('chunkLoadingGlobal', 'courtChunk');
    memo.externals(
      API_ENV === 'local' && PORT === '3003'
        ? {}
        : {
            umi: 'umi',
            'react-dom': 'ReactDOM',
            react: 'React',
          },
    );
  },
  extraPostCSSPlugins: ['tailwindcss', 'autoprefixer'],
});

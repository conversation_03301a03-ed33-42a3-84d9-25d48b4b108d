/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  local: {
    '/frameWorkPortal/': {
      ws: true,
      // target: 'http://*************:8080/',
      // target: 'http://deop.dev.ii-ai.tech/frameWorkPortal',
      target: 'http://deop.qa.ii-ai.tech/frameWorkPortal',
      changeOrigin: true,
      pathRewrite: { '^/frameWorkPortal': '' },
    },
    '/ws': {
      ws: true,
      // target: 'http://deop.dev.ii-ai.tech/ws',
      target: 'http://deop.qa.ii-ai.tech/ws',
      changeOrigin: true,
      pathRewrite: { '^/ws': '' },
    },
    '/autoRefresh/deopCmd/': {
      ws: true,
      target: 'http://localhost:9001',
      // target: 'http://dep-fe.dev.ii-ai.tech',
      changeOrigin: true,
      pathRewrite: { '^/autoRefresh/deopCmd': '' },
    },
    '/websocket': {
      ws: true,
      // target: 'http://deop.dev.ii-ai.tech/ws',
      // target: 'http://**********:8080/websocket',
      target: 'http://deop.qa.ii-ai.tech/websocket',
      changeOrigin: true,
      pathRewrite: { '^/websocket': '' },
    },
  },
  dev: {
    '/frameWorkPortal/': {
      ws: true,
      // target: 'http://dep-gateway:8080',
      target: 'http://dep-fe.dev.ii-ai.tech',
      changeOrigin: true,
      pathRewrite: { '^/frameWorkPortal': '' },
    },
  },
  qa: {
    '/frameWorkPortal/': {
      ws: true,
      target: 'http://dep-fe.dev.ii-ai.tech/frameWorkPortal',
      // target: 'http://dep-fe.dev.ii-ai.tech',
      changeOrigin: true,
      pathRewrite: { '^/frameWorkPortal': '' },
    },
  },
};

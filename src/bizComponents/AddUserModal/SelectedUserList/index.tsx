import { Button, Space } from 'antd';
import styles from './index.less';
import { SelectedUserInfoType } from '../interface';
import { IconFont } from 'main/MyIcon';
import defaultAvatarUrl from '@/assets/images/user/default_avatar.png';
interface SelectedUserListProps {
  // realcheckedUsers: SelectedUserInfoType[];
  realcheckedUsers: any[];
  deleteAll: () => void;
  deleteItem: (item: SelectedUserInfoType) => void;
}

const SelectedUserList: React.FC<SelectedUserListProps> = ({
  realcheckedUsers,
  deleteAll,
  deleteItem,
}) => {
  return (
    <div className={styles['right-selected']}>
      <div className={styles['selected-header']}>
        <span className={styles['selected-num']}>
          已选成员:{realcheckedUsers.length}人
        </span>
        {realcheckedUsers.length > 0 && (
          <Button onClick={deleteAll} className={styles.clearout}>
            清空
          </Button>
        )}
      </div>
      <div className={styles.divider}></div>
      <ul className={styles['selected-list']}>
        {realcheckedUsers.map((item: SelectedUserInfoType) => {
          return (
            <li className={styles['selected-item']} key={'select' + item.key}>
              <span>
                <Space className={styles['item-info']}>
                  <img
                    src={item?.avatar || defaultAvatarUrl}
                    className={styles['item-info-icon']}
                  />
                  <div className={styles['item-info-name']}>{item.name}</div>
                </Space>
              </span>
              <span className={styles.delete} onClick={() => deleteItem(item)}>
                <IconFont
                  type={'iconguanbi'}
                  className={styles['icon-delete']}
                />
              </span>
            </li>
          );
        })}
      </ul>
    </div>
  );
};
export default SelectedUserList;

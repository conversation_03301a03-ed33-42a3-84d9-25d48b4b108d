@import '~@/assets/styles/variable.less';

.right-selected {
  padding: 10px 18px;
  width: calc((100% - 16px) / 2);
  // width: 50%;
  background-color: @page-bg-color;

  .selected-header {
    display: flex;
    justify-content: space-between;

    .selected-num {
      height: 24px;
      line-height: 24px;
      // font-size: 13px;
    }

    .clearout {
      height: 24px;
      line-height: 20px;
      border-radius: 10px;
      font-size: 12px;
      padding: 0 10px;
    }
  }

  .divider {
    width: calc(100% + (10px + 10px));
    margin: 6px 0;
    margin-left: calc(-1 * 10px);
    height: 1px;
    // background: @divider-border-color;
    background: transparent;
  }

  ul.selected-list {
    // -自适应
    height: 340px;
    overflow-y: auto;
    margin: 0;
    padding: 0;

    li.selected-item {
      display: flex;
      justify-content: space-between;
      height: 36px;

      .item-info {
        &-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-top: -2px;
        }

        &-name {
          width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }

      .icon-delete {
        width: 18px;
        height: 18px;
        background: transparent;
        padding: 0 2px;
        line-height: 18px;
        color: #9faab8;

        &:hover {
          background-color: rgb(76 91 110 / 10%);
        }
        // .icon-close{

        // }
      }
    }
  }
}

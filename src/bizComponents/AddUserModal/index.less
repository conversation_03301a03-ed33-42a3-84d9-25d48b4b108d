@import '~@/assets/styles/variable.less';

@left-content-padding: 20px;
@left-content-item-padding: 0 8px;

.adModal {
  &-content {
    min-width: 700px !important;
  }

  .content {
    display: flex;
    justify-content: space-between;
    height: 400px;
    overflow: hidden;

    &-col {
      width: calc((100% - 16px) / 2);
      background-color: @page-bg-color;
      border: 1px solid @page-bg-color;

      &.error {
        border-color: @error-color;
      }

      .header {
        padding: 20px 20px 0;
      }

      .list {
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 20px 20px;
      }

      .item-info {
        &-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-top: -2px;
        }

        &-name {
          width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }
    }

    &-source {
      .breadcrumb {
        margin: 6px 0;
        line-height: 20px;
        padding: 0 8px;
        color: @text-color-secondary;

        &-item:last-of-type {
          color: @text-color;
        }

        &-title {
          cursor: pointer;
        }
      }

      .checked-all {
        margin-top: 16px;

        :global {
          .ant-checkbox-wrapper {
            width: 100%;
            height: 32px;
            line-height: 32px;
            padding: 0 8px;

            &:hover,
            &.ant-checkbox-wrapper-checked {
              background-color: #eef1f4;
              border-radius: 3px;
            }
          }
        }
      }

      .source-list {
        margin-top: 8px;
        padding-bottom: 18px;
        width: 100%;
        // -自适应
        height: 180px;
        overflow-y: auto;

        &-empty {
          padding-top: 40px;
        }

        dl {
          dt {
            display: flex;
            justify-content: space-between;
            height: 32px;
            line-height: 32px;
            color: @text-color;
            margin-bottom: 6px;
            padding: 0 8px;

            &:hover {
              background-color: #eef1f4;
            }
          }
        }

        &-department-icon {
          font-size: 14px;
          color: #c8d3e1;
        }

        &-department-name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: keep-all;
          width: 145px;
          display: block;
          height: 32px;
        }

        &-next {
          color: @text-color;
          font-weight: 400;

          &:hover {
            cursor: pointer;
            color: @link-color;
          }
        }

        :global {
          .ant-checkbox-group {
            width: 100%;

            .ant-checkbox-group-item {
              width: 100%;
              height: 32px;
              line-height: 32px;
              padding: 0 8px;
              margin-bottom: 8px;

              &:hover,
              &.ant-checkbox-wrapper-checked {
                background: rgb(76 124 255 / 10%);
                border-radius: 3px;
              }
            }
          }
        }
      }
    }

    &-target {
      .target-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 8px 16px;

        .clear-all {
          height: 20px;
          border-radius: 10px;
          font-size: 12px;
          padding: 0 10px;
        }
      }

      .target-list {
        height: calc(100% - 36px);

        &-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: 32px;
          padding: 0 8px;
          margin-bottom: 8px;

          &:hover,
          &.ant-checkbox-wrapper-checked {
            background-color: #eef1f4;
            border-radius: 3px;
          }

          .delete {
            width: 18px;
            height: 18px;
            background: transparent;
            padding: 0 2px;
            line-height: 18px;

            &:hover {
              background-color: rgb(92 111 136 / 10%);
            }

            .icon {
              font-size: 13px;
              color: #9faab8;
            }
          }
        }
      }
    }
  }

  .tip-single-error {
    color: @error-color;
    height: 0;
  }
}

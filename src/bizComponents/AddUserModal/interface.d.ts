/**
 * deptCheckedList: keys[]
 * realCheckedList: SelectedUserInfoType[]
 * userList: DepartmentMemberBackType[]
 *
 * userId(group)和Id(dept)定义等于key，使用key
 *
 */
/** 用户组项信息 */
export interface UserGroupInfoType {
  id: string;
  groupName: string;
}

/** 用户组成员信息 */
export interface UserInfoType {
  userId: string;
  account?: string;
  name?: string;
  department?: string;
  phone?: string;
  role?: string;
}
/** 查询用户组成员列表参数类型 */
export interface QueryUserGroupMemberParamsType {
  /** 用户组id */
  userGroupId?: string | null;
  /** 用户名 */
  account?: string;
  /** 名字 */
  name?: string;
}
/** 查询部门成员列表参数类型 */
export interface QueryDepartmentMemberParamsType {
  /** 部门id */
  deptId?: string | null;
  /** 用户名  */
  account?: string;
  /** 名字 */
  name?: string;
}

/** 新增用户组成员请求参数 */
export interface QueryAddUserGroupMemberParamsType {
  userGroupId: string;
  userIds: string[];
}

/** 获取部门成员返回的类型数据 */
export interface DepartmentMemberBackType {
  /** id */
  key: string;
  /** id */
  id?: string;
  /** 名字 --后端返回的是value-- */
  name?: string;
  account?: string;
  /** 区分部门1还是成员2 */
  type: number;
  /** 头像icon */
  icon?: string;
  avatar?: string;
}
/** 获取用户组成员返回的类型数据 */
export interface GroupMemberBackType {
  /** id */
  key: string;
  /** 同id */
  userId?: string;
  /** 名字 */
  name?: string;
  /** 用户名 */
  account?: string;
  /** 头像 */
  icon?: string;
  avatar?: string;
}

/** 选中成员信息类型*/
export interface SelectedUserInfoType {
  /** 成员id */
  key: string;
  /** 同key */
  userId?: string;
  /** 名字 */
  name?: string;
  /** 用户名 */
  account?: string;
  /** 头像 */
  avatar?: string | null;
  /** 头像 */
  icon?: string | null;
}

/** 自定义面包屑类型 */
export interface BreadCrumbItemType {
  key: string | null;
  /** 名称 */
  value: string;
}

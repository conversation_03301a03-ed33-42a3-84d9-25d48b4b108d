import { SzSearchForm, SzEmpty } from 'main/SzComponents';
import { IconFont } from 'main/MyIcon';
import type { CheckboxOptionType } from 'antd';
import { Checkbox, Form, Modal, Space, Spin, Tabs } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { CheckboxValueType } from 'antd/es/checkbox/Group';
import SelectedUserList from './SelectedUserList';
import { CheckboxChangeEvent } from 'antd/es/checkbox/Checkbox';
import defaultAvatarUrl from '@/assets/images/user/default_avatar.png';
import {
  getUserGroupList,
  getDepartmentMemberList,
  getGroupMemberList,
} from './services';
import { UserGroupInfoType, UserInfoType } from './interface';
import BreadCrumb from './BreadCrumb';
import styles from './index.less';
import {
  BreadCrumbItemType,
  DepartmentMemberBackType,
  GroupMemberBackType,
  QueryDepartmentMemberParamsType,
  QueryUserGroupMemberParamsType,
  SelectedUserInfoType,
} from './interface';

/**
 *
 */
const CheckboxGroup = Checkbox.Group;
interface AddModalProps {
  /** 弹框是否显示 */
  isModalOpen?: boolean;
  /** 默认组中的用户列表(默认已选中) */
  defaultCheckedList: UserInfoType[];
  /** 点击弹框提交按钮触发事件,并返回选中的用户ids */
  handleOk: (realCheckedList: string[]) => void;
  /** 点击弹框取消按钮触发事件 */
  handleCancel: () => void;
}

/**
 * @deprecated 未来将弃用，使用新的  UserSelect
 */
const AddUserModal: React.FC<AddModalProps> = ({
  isModalOpen,
  handleOk,
  handleCancel,
  defaultCheckedList,
}) => {
  /** 搜索框 */
  const [modalSearchForm] = Form.useForm();
  const [searchName, setSearchName] = useState('account');
  /** 可展开的部门树 */
  const [deptList, setDeptList] = useState<[]>([]);
  /** 可展开的用户组 */
  const [groupList, setGroupList] = useState<[]>([]);
  /** 可选中的部门成员列表 */
  const [deptUserList, setDeptUserList] = useState<DepartmentMemberBackType[]>(
    [],
  );
  /** 可选中的用户组成员列表 */
  const [groupUserList, setGroupUserList] = useState<GroupMemberBackType[]>([]);
  /** 选中的部门成员 */
  const [deptCheckedList, setDeptCheckedList] = useState<string[]>([]);
  /** 选中的用户组成员 */
  const [groupCheckedList, setGroupCheckedList] = useState<string[]>([]);

  /** 所有选中成员，展示在右侧 */
  const [realCheckedList, setRealCheckedList] = useState<
    SelectedUserInfoType[]
  >(defaultCheckedList as SelectedUserInfoType[]);
  /** 当前选择的部门id */
  const [curDeptId, setCurDeptId] = useState<string | null>(null);
  /** 当前选择的用户组id */
  const [curGroupId, setCurGroupId] = useState<string | null>(null);
  /** 当前tab所在位置 */
  const [curTab, setCurTab] = useState('department');
  /** 当前展示的面包屑列表 */
  const [deptBreadcrumbList, setDeptBreadcrumbList] = useState<
    BreadCrumbItemType[]
  >([
    {
      key: null, // 顶级部门树传null
      value: '实在智能',
    },
  ]);
  const [groupBreadcrumbList, setGroupBreadcrumbList] = useState<
    BreadCrumbItemType[]
  >([
    {
      key: null, // 顶级用户组树传null
      value: '实在智能',
    },
  ]);

  const [loading, setLoading] = useState(false);
  /** 根据curDeptId获取部门成员列表 */
  const queryDepartmentMemberList = async (
    params?: QueryDepartmentMemberParamsType,
  ) => {
    setLoading(true);
    await getDepartmentMemberList({ ...params }).then(({ bizData }) => {
      if (bizData && bizData.userAndDept) {
        const data = bizData.userAndDept || [];
        /** 更新部门列表 */
        setDeptList(
          data
            .filter((item: DepartmentMemberBackType) => item.type === 1)
            .map((item: any) => {
              return {
                key: item.key,
                name: item.value,
              };
            }),
        );
        /** 更新部门用户列表 */
        const newUserData = data
          .filter((item: DepartmentMemberBackType) => item.type === 2)
          .map((item: any) => {
            return {
              ...item,
              id: item.key,
              name: item.value,
            };
          });
        setDeptUserList(newUserData);
        /** 更新当前用户列表下，默认选中的部门用户keys */
        const defaultCheckedKeys = realCheckedList
          .filter((item) =>
            newUserData.some(
              (newItem: DepartmentMemberBackType) => newItem.key === item.key,
            ),
          )
          .map((item) => item.key);

        setDeptCheckedList(defaultCheckedKeys);
      }
    });
    setLoading(false);
  };

  /** 根据curGroupId获取部门成员列表 */
  const queryGroupMemberList = async (
    params?: QueryUserGroupMemberParamsType,
  ) => {
    setLoading(true);
    if (curGroupId) {
      setGroupList([]);
      await getGroupMemberList({ ...params }).then(({ bizData }) => {
        setGroupUserList(
          bizData.map((item: GroupMemberBackType) => {
            return {
              key: item.userId,
              userId: item.userId,
              name: item.name || item.account,
              icon: item.icon || item.avatar || null,
            };
          }),
        );
        /**  初始化时将部门已存在的用户加入realcheckList显示 */
        const defaultCheckedKeys = realCheckedList
          .filter((item: SelectedUserInfoType) =>
            bizData.some(
              (newItem: GroupMemberBackType) => newItem.userId === item.key,
            ),
          )
          .map((item) => item.key);

        setGroupCheckedList(defaultCheckedKeys);
      });
    } else {
      setGroupUserList([]);
      await getUserGroupList().then(({ bizData }) => {
        setGroupList(
          bizData.map((item: UserGroupInfoType) => {
            return {
              key: item.id,
              name: item.groupName,
            };
          }),
        );
      });
    }
    setLoading(false);
  };
  /** 设置搜索表单 */
  const searchForm = {
    formData: [
      {
        name: searchName,
        component: {
          name: 'MapSearch',
          style: { width: 260 },
          // style: { minWidth: 150, maxWidth: 220 },
          MapSelect: { account: '账号', name: '姓名' },
          setSelectKey: setSearchName,
          searchName,
          placeholder: '请输入' + { searchName },
          onSearch(value: string) {
            if (curTab === 'department') {
              queryDepartmentMemberList({ [searchName]: value });
            } else if (curTab === 'userGroup') {
              queryGroupMemberList({
                userGroupId: curGroupId,
                [searchName]: value,
              });
            }
          },
        },
      },
    ],
    form: modalSearchForm,
  };
  /** 用户头像名称信息 */
  const TenantInfoElem = useCallback(
    (item: DepartmentMemberBackType | GroupMemberBackType) => {
      return (
        <Space className={styles['item-info']}>
          <img
            src={item?.icon || item?.avatar || defaultAvatarUrl}
            className={styles['item-info-icon']}
          />
          <div className={styles['item-info-name']}>
            {item.name || item.account}
          </div>
        </Space>
      );
    },
    [],
  );

  // 把成员列表数据格式化为checkbox的options数据
  const formatDataToCheckboxOptionType = useCallback(
    (list: DepartmentMemberBackType[] | GroupMemberBackType[]) => {
      const checkboxOptionTypeArr: CheckboxOptionType[] = [];
      list.map((item: any) => {
        checkboxOptionTypeArr.push({
          label: TenantInfoElem(item),
          value: item.key,
        });
        return item;
      });
      return checkboxOptionTypeArr;
    },
    [],
  );

  // 左侧成员选项发生变化
  const onCheckDeptChange = useCallback(
    (list: CheckboxValueType[]) => {
      const data = deptUserList;
      const isCheckedAll = list.length === data.length;
      if (isCheckedAll) {
        // 在realCheckedList中增加，选中但未加入到realCheckedLis的数据
        const newRealChecedList: SelectedUserInfoType[] = [...realCheckedList];
        data.forEach((item: DepartmentMemberBackType) => {
          const findInRealChecedItemArr = realCheckedList.filter(
            (targetItem: SelectedUserInfoType) => targetItem.key === item.key,
          );
          if (findInRealChecedItemArr?.length === 0) {
            newRealChecedList.push({
              key: item.key,
              account: item.account,
              name: item.name,
              userId: item.key,
              avatar: item?.avatar,
            });
          }
        });
        setRealCheckedList(newRealChecedList);
      } else {
        // 在realCheckedList中增加选中的数据，在realCheckedList中删除该左侧列表中未选中的数据
        const newRealChecedList1: SelectedUserInfoType[] = [...realCheckedList];
        data.forEach((item: DepartmentMemberBackType) => {
          const idInRealCheckedListIdx = realCheckedList.findIndex(
            (realListItem: SelectedUserInfoType) =>
              realListItem.key === item.key,
          );
          if (list.includes(item.key)) {
            if (idInRealCheckedListIdx < 0) {
              // 增加该item
              newRealChecedList1.push(item as SelectedUserInfoType);
            }
          } else if (idInRealCheckedListIdx >= 0) {
            newRealChecedList1.splice(idInRealCheckedListIdx, 1);
          }
        });
        setRealCheckedList(newRealChecedList1);
      }
      setDeptCheckedList(list as string[]);
    },
    [realCheckedList, deptUserList, deptCheckedList],
  );

  // 左侧成员选项发生变化
  const onCheckGroupChange = useCallback(
    (list: CheckboxValueType[]) => {
      //
      const data = groupUserList;
      const isCheckedAll = list.length === data.length;
      if (isCheckedAll) {
        // 在realCheckedList中增加，选中但未加入到realCheckedLis的数据
        const newRealChecedList: SelectedUserInfoType[] = [...realCheckedList];
        data.map((item: GroupMemberBackType) => {
          const findInRealChecedItemArr = realCheckedList.filter(
            (targetItem: SelectedUserInfoType) => targetItem.key === item.key,
          );
          if (findInRealChecedItemArr?.length === 0) {
            newRealChecedList.push({
              key: item.key,
              account: item.account,
              name: item.name,
              userId: item.userId,
              avatar: item?.avatar,
            });
          }
          return item;
        });
        setRealCheckedList(newRealChecedList);
      } else {
        // 在realCheckedList中增加选中的数据，在realCheckedList中删除该左侧列表中未选中的数据
        const newRealChecedList1: SelectedUserInfoType[] = [...realCheckedList];
        data.forEach((item: GroupMemberBackType) => {
          const idInRealCheckedListIdx = realCheckedList.findIndex(
            (realListItem: SelectedUserInfoType) =>
              realListItem.key === item.key,
          );
          if (list.includes(item.key)) {
            if (idInRealCheckedListIdx < 0) {
              // 增加该item
              newRealChecedList1.push({
                key: item.key,
                account: item.account,
                name: item.name,
                userId: item.userId,
                avatar: item?.avatar,
              });
            }
          } else if (idInRealCheckedListIdx >= 0) {
            /** 删除该item */
            newRealChecedList1.splice(idInRealCheckedListIdx, 1);
          }
        });
        setRealCheckedList(newRealChecedList1);
      }
      setGroupCheckedList(list as string[]);
    },
    [realCheckedList, groupUserList, groupCheckedList],
  );

  // 设置Tabs标签页
  const TabItems = [
    {
      label: `部门`,
      key: 'department',
      children: (
        <Spin spinning={loading}>
          <BreadCrumb
            breadcrumbList={deptBreadcrumbList}
            handleClick={(item: BreadCrumbItemType) => {
              setCurDeptId(item.key);

              setDeptBreadcrumbList((preState) => {
                const idx = preState.findIndex(
                  (state) => state.key === item.key,
                );
                return preState.slice(0, idx + 1);
              });
            }}
          />
          {deptUserList.length > 0 && (
            <div className={styles['checked-all']}>
              <Checkbox
                onChange={(e: CheckboxChangeEvent) => {
                  /** 选中 */
                  if (e.target.checked) {
                    setDeptCheckedList(
                      deptUserList.map(
                        (item: DepartmentMemberBackType) => item.key,
                      ),
                    );
                    /** 合并 */
                    const needIncludeUsers = deptUserList.filter(
                      (item: DepartmentMemberBackType) =>
                        !realCheckedList.some(
                          (realItem: SelectedUserInfoType) =>
                            realItem.key === item.key,
                        ),
                    ) as SelectedUserInfoType[];

                    setRealCheckedList((preState) => {
                      return [...preState, ...needIncludeUsers];
                    });
                  } else {
                    setDeptCheckedList([]);
                    /** 删除 */
                    setRealCheckedList((preState) => {
                      return preState.filter(
                        (state) =>
                          !deptUserList.some(
                            (item: DepartmentMemberBackType) =>
                              item.key === state.key,
                          ),
                      );
                    });
                  }
                }}
                indeterminate={
                  deptCheckedList.length > 0 &&
                  deptCheckedList.length < deptUserList.length
                }
                checked={
                  deptCheckedList.length === deptUserList.length &&
                  deptUserList.length > 0
                }
              >
                全选
              </Checkbox>
            </div>
          )}
          <div
            className={`${styles['list']} ${styles['source-list']}`}
            style={{ height: 'calc(100vh-130px' }}
          >
            {!deptList?.length && !deptUserList?.length && (
              <div className={styles['source-list-empty']}>
                <SzEmpty />
              </div>
            )}
            {!!deptList?.length && (
              <dl>
                {deptList.map((item: DepartmentMemberBackType) => {
                  return (
                    <dt key={'dpt' + item.key}>
                      <Space>
                        <IconFont
                          type="icona-wenjianjia2x"
                          className={styles['source-list-department-icon']}
                        />
                        <span className={styles['source-list-department-name']}>
                          {item.name}
                        </span>
                      </Space>
                      <span
                        className={styles['source-list-next']}
                        onClick={() => {
                          setCurDeptId(item.key);
                          // setDeptCheckedList([]);
                          setDeptBreadcrumbList((preState) => {
                            return [
                              ...preState,
                              {
                                key: item.key,
                                value: item.name! || item.account!,
                              },
                            ];
                          });
                        }}
                      >
                        <IconFont type="iconxiaji" />
                        下级
                      </span>
                    </dt>
                  );
                })}
              </dl>
            )}
            {!!deptUserList?.length && (
              <CheckboxGroup
                className={styles['source-list-group']}
                options={formatDataToCheckboxOptionType(deptUserList)}
                value={realCheckedList.map((item) => item.key)}
                onChange={onCheckDeptChange}
              />
            )}
          </div>
        </Spin>
      ),
    },
    {
      label: `用户组`,
      key: 'userGroup',
      children: (
        <Spin spinning={loading}>
          {/* 面包屑导航 */}
          <BreadCrumb
            breadcrumbList={groupBreadcrumbList}
            handleClick={(item: BreadCrumbItemType) => {
              setCurGroupId(item.key);

              setGroupBreadcrumbList((preState) => {
                const idx = preState.findIndex(
                  (state) => state.key === item.key,
                );
                return preState.slice(0, idx + 1);
              });
            }}
          />
          {/* 全选框 */}
          {groupUserList.length > 0 && (
            <div className={styles['checked-all']}>
              <Checkbox
                onChange={(e: CheckboxChangeEvent) => {
                  if (e.target.checked) {
                    setGroupCheckedList(
                      groupUserList.map((item: GroupMemberBackType) => {
                        return item.key;
                      }),
                    );
                    /** 合并 */
                    const needIncludeUsers = groupUserList
                      .filter(
                        (item: GroupMemberBackType) =>
                          !realCheckedList.some(
                            (realItem: SelectedUserInfoType) =>
                              realItem.key === item.key,
                          ),
                      )
                      .map((item: GroupMemberBackType) => ({
                        key: item.key,
                        userId: item.userId,
                        account: item.account,
                        name: item.name,
                        avatar: item?.avatar,
                      }));

                    setRealCheckedList((preState) => {
                      return [...preState, ...needIncludeUsers];
                    });
                  } else {
                    setGroupCheckedList([]);
                    /** 删除 */
                    setRealCheckedList((preState) => {
                      return preState.filter(
                        (state) =>
                          !groupUserList.some(
                            (item: GroupMemberBackType) =>
                              item.key === state.key,
                          ),
                      );
                    });
                  }
                }}
                indeterminate={
                  groupCheckedList.length > 0 &&
                  groupCheckedList.length < groupUserList.length
                }
                checked={
                  groupCheckedList.length === groupUserList.length &&
                  groupUserList.length > 0
                }
              >
                全选
              </Checkbox>
            </div>
          )}
          <div
            className={`${styles['list']} ${styles['source-list']}`}
            style={{ height: 'calc(100vh-130px' }}
          >
            {/* 用户组为空时 */}
            {!groupList?.length && !groupUserList?.length && (
              <div className={styles['source-list-empty']}>
                <SzEmpty />
              </div>
            )}
            {/* 用户组列表 */}
            {!!groupList?.length && (
              <dl>
                {groupList.map((item: DepartmentMemberBackType) => {
                  return (
                    <dt key={'group' + item.key}>
                      <Space>
                        <IconFont
                          type="icona-wenjianjia2x"
                          className={styles['source-list-department-icon']}
                        />
                        <span className={styles['source-list-department-name']}>
                          {item.name || item.account}
                        </span>
                      </Space>
                      <span
                        className={styles['source-list-next']}
                        onClick={() => {
                          setCurGroupId(item.key);
                          // setGroupCheckedList([]);
                          setGroupBreadcrumbList((preState) => {
                            return [
                              ...preState,
                              {
                                key: item.key,
                                value: item.name! || item.account!,
                              },
                            ];
                          });
                        }}
                      >
                        <IconFont type="iconxiaji" />
                        下级
                      </span>
                    </dt>
                  );
                })}
              </dl>
            )}
            {/* 用户组下的成员列表 */}
            {!!groupUserList?.length && (
              <CheckboxGroup
                className={styles['source-list-group']}
                options={formatDataToCheckboxOptionType(groupUserList)}
                value={realCheckedList.map((item) => item.key)}
                onChange={onCheckGroupChange}
              />
            )}
          </div>
        </Spin>
      ),
    },
  ];

  useEffect(() => {
    /** 获得部门树数据 */
    queryDepartmentMemberList({ deptId: curDeptId });
  }, [curDeptId]);

  useEffect(() => {
    /** 获得用户组树数据 */
    queryGroupMemberList({ userGroupId: curGroupId });
  }, [curGroupId]);

  useEffect(() => {
    /** Tab转变时，更新默认选中 */
    const defaultDeptCheckedKeys = realCheckedList
      .filter((item) => deptCheckedList.some((newItem) => newItem === item.key))
      .map((item) => item.key);
    setDeptCheckedList(defaultDeptCheckedKeys);
    const defaultGroupCheckedKeys = realCheckedList
      .filter((item) =>
        groupCheckedList.some((newItem) => newItem === item.key),
      )
      .map((item) => item.key);
    setGroupCheckedList(defaultGroupCheckedKeys);
  }, [curTab]);
  return (
    <Modal
      className={styles.adModal}
      open={isModalOpen}
      forceRender={true}
      onOk={() => handleOk(realCheckedList.map((item) => item.key))}
      onCancel={handleCancel}
      closable={true}
      title={'添加用户'}
      width={710}
    >
      <Spin spinning={false}>
        <section className={styles['content']}>
          <div
            className={`${styles['content-col']} ${styles['content-source']}`}
          >
            <div className={`${styles['header']} ${styles['source-header']}`}>
              <SzSearchForm {...searchForm} />
              <Tabs
                className={styles['tabs']}
                items={TabItems}
                onChange={(key: string) => {
                  setCurTab(key);
                }}
              ></Tabs>
            </div>
          </div>
          <SelectedUserList
            realcheckedUsers={realCheckedList}
            deleteAll={() => {
              setRealCheckedList([]);
              setDeptCheckedList([]);
              setGroupCheckedList([]);
            }}
            deleteItem={(item: any) => {
              setRealCheckedList((preState) => {
                return preState.filter((state) => state.key !== item.key);
              });
              setDeptCheckedList((preState) => {
                return preState.filter((state) => state !== item.key);
              });
              setGroupCheckedList((preState) => {
                return preState.filter((state) => state !== item.key);
              });
            }}
          />
        </section>
      </Spin>
    </Modal>
  );
};
export default AddUserModal;

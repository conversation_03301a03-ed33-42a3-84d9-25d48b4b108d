import { BreadCrumbItemType } from '../interface';
import styles from './index.less';
import { IconFont } from 'main/MyIcon';

interface BreadCrumbProps {
  breadcrumbList: BreadCrumbItemType[];
  handleClick: (item: any) => void;
}

const BreadCrumb: React.FC<BreadCrumbProps> = ({
  breadcrumbList,
  handleClick,
}) => {
  return (
    <>
      {breadcrumbList.map((item: BreadCrumbItemType, index: number) => {
        return (
          <label
            key={`breadcrumb${item.key}`}
            className={styles['breadcrumb-item']}
            onClick={() => {
              handleClick(item);
            }}
          >
            {index !== 0 && <IconFont type="iconxiangyou-xian" />}
            <span className={styles['breadcrumb-title']}>{item.value}</span>
          </label>
        );
      })}
    </>
  );
};

export default BreadCrumb;

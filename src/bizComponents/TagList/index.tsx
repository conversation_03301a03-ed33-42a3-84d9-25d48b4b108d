import React from 'react';
import { Tag, Popover } from 'antd';
import styles from './index.less';

interface Props {
  list: any[];
  maxCount: number;
  emptyText?: React.ReactNode;
}
const tagItem = (list: any) => {
  return (
    <>
      {list.map((item: any, index: number) => {
        return (
          <Tag color={item.color} key={item.id} className="w-max">
            {item.tagName}
          </Tag>
        );
      })}
    </>
  );
};
export default function TagList(props: Props) {
  const { list, maxCount, emptyText } = props;

  return (
    <div className="flex flex-wrap gap-y-[8px]">
      {list && list.length > 0 ? (
        list.length <= maxCount ? (
          tagItem(list)
        ) : (
          <>
            {tagItem(list.slice(0, maxCount))}
            <Popover
              content={
                <div className="flex flex-wrap gap-y-[8px] max-w-[300px] max-h-[300px] overflow-y-auto">
                  {tagItem(list)}
                </div>
              }
              title="更多标签"
              placement="right"
              trigger="click"
              // getPopupContainer={(triggerNode: any) =>
              //   triggerNode.parentElement
              // }
            >
              <div className={styles.moreTag}>
                <Tag className="cursor-pointer">{`更多`}</Tag>
              </div>
            </Popover>
          </>
        )
      ) : (
        emptyText
      )}
    </div>
  );
}

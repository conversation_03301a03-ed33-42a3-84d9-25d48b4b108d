// 手机机器人、pc机器人切换

import { ProcessChannelEnum } from '@deopCmd/constants/biz/process';
import { useControllableValue } from 'ahooks';
import { ComponentProps, FC } from 'react';

export const RunSystemEnum = {
  WINDOWS: 'windows',
  ITAI: 'itai',
  MOBILE: 'mobile',
} as const;

export type RunSystemEnum = (typeof RunSystemEnum)[keyof typeof RunSystemEnum];

export const RunSystemTextMap: Record<RunSystemEnum, string> = {
  [RunSystemEnum.WINDOWS]: 'PC',
  [RunSystemEnum.ITAI]: '信创',
  [RunSystemEnum.MOBILE]: '手机',
};

const options = [
  {
    label: RunSystemTextMap[RunSystemEnum.WINDOWS],
    value: RunSystemEnum.WINDOWS,
  },
  {
    label: RunSystemTextMap[RunSystemEnum.MOBILE],
    value: RunSystemEnum.MOBILE,
  },
];

const TabSwitch = <T extends string | number>({
  options,
  ...rest
}: {
  options: {
    label: string;
    value: T;
  }[];
  value?: T;
  onChange?: (val: T) => void;
  defaultValue?: T;
}) => {
  const [value, setValue] = useControllableValue(rest, {});
  return (
    <div className="flex items-center gap-[4px] px-1 py-1 border-[#DCDCDC] border-[1px] border-solid rounded-[6px] bg-white">
      {options.map((item) => (
        <div
          key={item.value}
          className={`cursor-pointer rounded-[4px] px-[16px] py-[2px] text-[rgba(29,33,41,0.6)] hover:text-[#185FF0] ${
            item.value === value && 'bg-[rgba(24,95,240,0.12)] font-medium !text-[rgba(24,95,240,1)]'
          }`}
          onClick={() => {
            // onChange && onChange(item.value);
            setValue(item.value);
          }}>
          {item.label}
        </div>
      ))}
    </div>
  );
};

export const BotTypeSwitch: FC<Omit<ComponentProps<typeof TabSwitch<RunSystemEnum>>, 'options'>> = (props) => {
  return <TabSwitch options={options} {...props} />;
};

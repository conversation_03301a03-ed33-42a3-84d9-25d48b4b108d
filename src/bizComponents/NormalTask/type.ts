/** 任务状态：1-运行中，2-待运行，3-已完成，4-已停止，5-停止中 */
export enum NoramlTaskStatusEnum {
  RUNNING = 1,
  RUNNABLE = 2,
  FINISHED = 3,
  STOPPED = 4,
  STOPPING = 5,
}
export const NomralTaskStatusMap = {
  [NoramlTaskStatusEnum.RUNNING]: '运行中',
  [NoramlTaskStatusEnum.RUNNABLE]: '待运行',
  [NoramlTaskStatusEnum.FINISHED]: '已完成',
  [NoramlTaskStatusEnum.STOPPED]: '已停止',
  [NoramlTaskStatusEnum.STOPPING]: '停止中',
};
export const NoramlTaskStatusArr = [
  {
    key: NoramlTaskStatusEnum.RUNNING,
    name: NomralTaskStatusMap[NoramlTaskStatusEnum.RUNNING],
  },
  {
    key: NoramlTaskStatusEnum.RUNNABLE,
    name: NomralTaskStatusMap[NoramlTaskStatusEnum.RUNNABLE],
  },
  {
    key: NoramlTaskStatusEnum.FINISHED,
    name: NomralTaskStatusMap[NoramlTaskStatusEnum.FINISHED],
  },
  {
    key: NoramlTaskStatusEnum.STOPPED,
    name: NomralTaskStatusMap[NoramlTaskStatusEnum.STOPPED],
  },
  {
    key: NoramlTaskStatusEnum.STOPPING,
    name: NomralTaskStatusMap[NoramlTaskStatusEnum.STOPPING],
  },
];

export const NoramlTaskStatusTypeMapInfo = {
  [NoramlTaskStatusEnum.FINISHED]: {
    label: NomralTaskStatusMap[NoramlTaskStatusEnum.FINISHED],
    color: 'rgb(0 174 87 / 50%)',
  },
  [NoramlTaskStatusEnum.RUNNABLE]: {
    label: NomralTaskStatusMap[NoramlTaskStatusEnum.RUNNABLE],
    color: '#2F9AFF',
  },
  [NoramlTaskStatusEnum.RUNNING]: {
    label: NomralTaskStatusMap[NoramlTaskStatusEnum.RUNNING],
    color: '#00AE57',
  },
  [NoramlTaskStatusEnum.STOPPED]: {
    label: NomralTaskStatusMap[NoramlTaskStatusEnum.STOPPED],
    color: '#5C6F88',
  },
  [NoramlTaskStatusEnum.STOPPING]: {
    label: NomralTaskStatusMap[NoramlTaskStatusEnum.STOPPING],
    color: '#F98956',
  },
};

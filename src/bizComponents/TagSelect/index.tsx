import { Tag, Select } from 'antd';
import React, { useState, useEffect } from 'react';
import { getAllTagList } from '@deopCmd/services/global';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';

const { Option } = Select;

interface Props {
  value?: string[];
  onChange?: (value: string[]) => void;
}

const TagSelect: React.FC<Props> = ({ value = [], onChange }) => {
  const [tagValue, setTagValue] = useState<string[]>(value);
  const [tagList, setTagList] = useState<any[]>([]);

  useEffect(() => {
    getAllTagList().then((res) => {
      setTagList((res?.bizData || []).filter((item: any) => item.status === 1));
    });
  }, []);

  const handleChange = (value: string[]) => {
    setTagValue(value);
    onChange?.(value);
  };

  const tagRender = (props: CustomTagProps) => {
    const { value, closable, onClose } = props;
    const selectTag = tagList.find((item: any) => item.id === value);
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };
    return (
      <Tag
        color={selectTag?.color}
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{ marginRight: 3 }}>
        {selectTag?.tagName}
      </Tag>
    );
  };

  return (
    <Select
      mode="multiple"
      value={value || tagValue}
      showArrow
      allowClear
      tagRender={tagRender}
      onChange={handleChange}
      placeholder="请选择标签，可多选"
      maxTagCount="responsive"
      filterOption={(inputValue, option) => {
        return option?.key.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
      }}>
      {tagList.map((item: any) => (
        <Option value={item.id} key={item.id}>
          <Tag key={item.id} color={item.color}>
            {item.tagName}
          </Tag>
        </Option>
      ))}
    </Select>
  );
};

export default TagSelect;

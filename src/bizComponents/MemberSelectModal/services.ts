import { request } from 'umi';

/** 获取部门成员列表请求参数 */
interface DepartmentDepartmentMemberParamsType {
  /** 姓名 */
  name?: string;
  /** 部门id */
  deptId?: string | null;
  /** 账号 */
  account?: string;
}

/** 用户与部门-数据类型 */
enum DepartmentMemberTypeEnum {
  DEPARTMENT = 1,
  USER = 2,
}

/** 获取部门成员返回的数据类型 */
interface DepartmentMemberBackType {
  icon?: string;
  /** icon */
  avatar?: string;
  /** id */
  key: string;
  /** 名字 */
  value: string;
  /** 类型 */
  type: DepartmentMemberTypeEnum;
}

// 获取部门成员列表
const getDepartmentMemberList = async (
  params?: DepartmentDepartmentMemberParamsType,
) => {
  return await request(`/uc/user/1.0.0/user-dept-list`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

export {
  DepartmentDepartmentMemberParamsType,
  DepartmentMemberTypeEnum,
  DepartmentMemberBackType,
  getDepartmentMemberList,
};

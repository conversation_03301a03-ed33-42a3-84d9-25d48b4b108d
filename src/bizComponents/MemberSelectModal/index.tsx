// import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
// import { Modal, Form, Space, Button, Checkbox, Spin } from 'antd';
// import { SzSearchForm, SzEmpty } from 'main/SzComponents';
// import { IconFont } from 'main/MyIcon';
// import type { CheckboxChangeEvent } from 'antd/es/checkbox';
// import type {
//   CheckboxValueType,
//   CheckboxOptionType,
// } from 'antd/es/checkbox/Group';
// import { SelectModalConfigType, SelectedMemberInfoType } from './interface';
// import {
//   DepartmentDepartmentMemberParamsType,
//   DepartmentMemberTypeEnum,
//   DepartmentMemberBackType,
//   getDepartmentMemberList,
// } from './services';
// /** 展示的头像信息 */
// export const getAvatarUrl = (url?: string | null) => {
//   if (!url || url === '-1') return '';
//
//   return url;
// };
// import styles from './index.less';
//
// const CheckboxGroup = Checkbox.Group;
// interface OneBreadcrumbType {
//   key: string | null;
//   value: string;
// }
// interface Props extends SelectModalConfigType {
//   visible: boolean;
//   /** 默认选中的值 */
//   defaultCheckedList: SelectedMemberInfoType[];
//   /** 取消/关闭回调 */
//   onClose: () => void;
//   /** 点击确定时候的回调 */
//   onOk: (items: SelectedMemberInfoType[]) => void;
// }
//
// /**
//  * 门户选择成员弹窗
//  * @deprecated 不要使用，没有调通接口。使用MemberSelectModalCommander
//  */
// const MemberSelectModal = (props: Props) => {
//   const {
//     visible = false,
//     defaultCheckedList = [],
//     onClose,
//     onOk,
//     modalTitle = '选择成员',
//     modalSelectedTip = '已选择：',
//     isSingleSelected = true,
//   } = props;
//   const SourceHeaderRef = useRef<HTMLDivElement>(null);
//   const [sourceListHeight, setSourceListHeight] = useState<string>();
//
//   // 每次弹窗出现都是取顶级数据
//   const { tenantName = '' } = { tenantName: 'piak' };
//
//   // 搜索框
//   const [modalSearchForm] = Form.useForm();
//   const [searchName, setSearchName] = useState('account');
//   const [searchValue, setSearchValue] = useState<string | null>(null);
//
//   // 当前选则的部门id
//   const [curDeptId, setCurDeptId] = useState<string | null>(null);
//   // 左侧面包屑，没有搜索词时展示
//   const [breadcrumbList, setBreadcrumbList] = useState<OneBreadcrumbType[]>([
//     {
//       key: null, // 顶级部门树传null
//       value: tenantName,
//     },
//   ]);
//
//   // 可点击的部门列表
//   const [treeList, setTreeList] = useState<any[]>([]);
//   // 可选择的成员列表
//   const [dataList, setDataList] = useState<any[]>([]);
//   const [datalistLoading, setDataListLoading] = useState<boolean>(false);
//   const [indeterminate, setIndeterminate] = useState<boolean>(false);
//   const [checkAll, setCheckAll] = useState<boolean>(false);
//   const [checkedList, setCheckedList] = useState<CheckboxValueType[]>([]);
//   const [realCheckedList, setRealCheckedList] =
//     useState<SelectedMemberInfoType[]>(defaultCheckedList);
//
//   const resetPageData = useCallback(() => {
//     modalSearchForm.resetFields();
//     setDataList([]);
//     setDataListLoading(false);
//     setIndeterminate(false);
//     setCheckAll(false);
//     setCheckedList([]);
//   }, []);
//
//   // 点击取消的操作
//   const handleCancel = useCallback(() => {
//     resetPageData();
//     setRealCheckedList([]);
//     onClose();
//   }, [onClose]);
//
//   /** 获取部门用户列表 */
//   const queryDepartmentList = useCallback(
//     async (params?: DepartmentDepartmentMemberParamsType) => {
//       setDataListLoading(true);
//       await getDepartmentMemberList({ ...params, deptId: curDeptId })
//         .then(({ bizData }) => {
//           if (bizData && bizData?.userAndDept) {
//             const data = bizData.userAndDept || [];
//             const departmentListResult = data.filter(
//               (item: DepartmentMemberBackType) =>
//                 item.type === DepartmentMemberTypeEnum.DEPARTMENT,
//             );
//             setTreeList(departmentListResult);
//             const memberListResult = data.filter(
//               (item: DepartmentMemberBackType) =>
//                 item.type === DepartmentMemberTypeEnum.USER,
//             );
//             setDataList(memberListResult);
//           }
//         })
//         .finally(() => {
//           setDataListLoading(false);
//         });
//     },
//     [curDeptId],
//   );
//
//   useEffect(() => {
//     if (!visible) return;
//     queryDepartmentList();
//   }, [curDeptId, visible]);
//
//   // 搜索信息
//   const searchForm = {
//     formData: [
//       {
//         name: searchName,
//         component: {
//           name: 'MapSearch',
//           style: { width: 260 },
//           MapSelect: { account: '账号', name: '姓名' },
//           setSelectKey: setSearchName,
//           searchName,
//           onSearch(value: string) {
//             setSearchValue(value);
//             if (!value) {
//               setCurDeptId(breadcrumbList[breadcrumbList.length - 1].key);
//             }
//             queryDepartmentList({ [searchName]: value });
//           },
//         },
//       },
//     ],
//     form: modalSearchForm,
//     initialValues: {},
//     onFormChange: () => {},
//   };
//
//   // 更新源数据侧选中状态（注：不能加到useCallback中，否则会进入死循环）
//   const updateSourceChecked = (hadCheckedlist?: SelectedMemberInfoType[]) => {
//     const curRealCheckedList: SelectedMemberInfoType[] =
//       hadCheckedlist || realCheckedList || [];
//     // 把realCheckedList中的数据渲染到左侧树
//     const list: CheckboxValueType[] = [];
//     dataList.map((item: DepartmentMemberBackType) => {
//       const findInRealCheckedItems = curRealCheckedList.filter(
//         (realItem: SelectedMemberInfoType) => item.key === realItem.key,
//       );
//       if (findInRealCheckedItems.length > 0) {
//         list.push(item.key);
//       }
//       return item;
//     });
//
//     onCheckGroupChange(list);
//   };
//
//   // 左侧树源数据发生变化时，把右侧选中数据反写到左侧
//   useEffect(() => {
//     updateSourceChecked();
//   }, [dataList]);
//
//   // 源数据
//   const DataListLen = useMemo(() => {
//     return (dataList || []).length;
//   }, [dataList]);
//
//   // 源数据列表高度：100%高度-顶部高度
//   useEffect(() => {
//     const height = `calc(100% - ${SourceHeaderRef.current?.offsetHeight}px - 20px)`;
//     setSourceListHeight(height);
//   }, [SourceHeaderRef.current?.offsetHeight]);
//
//   /**
//    * 点击"确定"的操作
//    */
//   const handleOk = useCallback(() => {
//     onOk(realCheckedList);
//   }, [realCheckedList, onOk]);
//
//   // 左侧成员选项发生变化
//   const onCheckGroupChange = useCallback(
//     (list: CheckboxValueType[]) => {
//       const isCheckedAll = list.length === DataListLen;
//       if (isCheckedAll) {
//         // 在realCheckedList中增加，选中但未加入到realCheckedLis的数据
//         const newRealChecedList: SelectedMemberInfoType[] = [
//           ...realCheckedList,
//         ];
//         dataList.map((item: DepartmentMemberBackType) => {
//           const findInRealChecedItemArr = realCheckedList.filter(
//             (targetItem: SelectedMemberInfoType) => targetItem.key === item.key,
//           );
//           if (findInRealChecedItemArr?.length === 0) {
//             newRealChecedList.push({
//               key: item.key,
//               label: item.value,
//               value: item.key,
//               avatar: item?.avatar,
//             });
//           }
//           return item;
//         });
//         setRealCheckedList(newRealChecedList);
//       } else {
//         // 在realCheckedList中增加选中的数据，在realCheckedList中删除该左侧列表中未选中的数据
//         const newRealChecedList1: SelectedMemberInfoType[] = [
//           ...realCheckedList,
//         ];
//         dataList.map((item: DepartmentMemberBackType) => {
//           const idInRealCheckedListIdx = realCheckedList.findIndex(
//             (realListItem: SelectedMemberInfoType) =>
//               realListItem.key === item.key,
//           );
//           if (list.includes(item.key)) {
//             if (idInRealCheckedListIdx < 0) {
//               // 增加该item
//               newRealChecedList1.push({
//                 key: item.key,
//                 label: item.value,
//                 value: item.key,
//                 avatar: item?.avatar,
//               });
//             }
//           } else if (idInRealCheckedListIdx >= 0) {
//             newRealChecedList1.splice(idInRealCheckedListIdx, 1);
//           }
//           return item;
//         });
//         setRealCheckedList(newRealChecedList1);
//       }
//       setCheckedList(list);
//       setIndeterminate(!!list.length && list.length < DataListLen);
//       setCheckAll(isCheckedAll);
//     },
//     [DataListLen, realCheckedList, dataList, isSingleSelected],
//   );
//
//   // 全选按钮发生变更
//   const onCheckAllChange = useCallback(
//     (e: CheckboxChangeEvent) => {
//       if (e.target.checked) {
//         setCheckedList(formatDataToCheckboxValueType(dataList));
//         // 在realCheckedList中增加未加入的数据
//         const newRealChecedList: SelectedMemberInfoType[] = [
//           ...realCheckedList,
//         ];
//         dataList.map((item: DepartmentMemberBackType) => {
//           const findInRealCheckedItemArr = realCheckedList.filter(
//             (targetItem: SelectedMemberInfoType) => targetItem.key === item.key,
//           );
//           if (findInRealCheckedItemArr?.length === 0) {
//             newRealChecedList.push({
//               key: item.key,
//               label: item.value,
//               value: item.key,
//               avatar: item?.avatar,
//             });
//           }
//           return item;
//         });
//         setRealCheckedList(newRealChecedList);
//       } else {
//         setCheckedList([]);
//         // 在realCheckedList中删除左侧列表中未选中的数据
//         const newRealChecedList: SelectedMemberInfoType[] = [];
//         realCheckedList.map((item: SelectedMemberInfoType) => {
//           const findInTendantListItemArr = dataList.filter(
//             (sourceItem: DepartmentMemberBackType) =>
//               sourceItem.key === item.key,
//           );
//           if (findInTendantListItemArr?.length === 0) {
//             newRealChecedList.push({
//               key: item.key,
//               label: item.value,
//               value: item.key,
//               avatar: item?.avatar,
//             });
//           }
//           return item;
//         });
//         setRealCheckedList(newRealChecedList);
//       }
//       setIndeterminate(false);
//       setCheckAll(e.target.checked);
//     },
//     [dataList, realCheckedList],
//   );
//
//   // 用户信息元素
//   const TenantInfoElem = useCallback(
//     (
//       item: DepartmentMemberBackType | SelectedMemberInfoType,
//       from: 'source' | 'target',
//     ) => {
//       return (
//         <Space className={styles['item-info']}>
//           <img
//             src={getAvatarUrl(item?.icon || item?.avatar)}
//             className={styles['item-info-icon']}
//           />
//           <div className={styles['item-info-name']}>
//             {from === 'target'
//               ? (item as SelectedMemberInfoType).label
//               : item.value}
//           </div>
//         </Space>
//       );
//     },
//     [],
//   );
//
//   // 把成员列表数据格式化为checkbox的options数据
//   const formatDataToCheckboxOptionType = useCallback(
//     (list: DepartmentMemberBackType[]) => {
//       const checkboxOptionTypeArr: CheckboxOptionType[] = [];
//       list.map((item: DepartmentMemberBackType) => {
//         checkboxOptionTypeArr.push({
//           label: TenantInfoElem(item, 'source'),
//           value: item.key,
//         });
//         return item;
//       });
//       return checkboxOptionTypeArr;
//     },
//     [],
//   );
//
//   // 把租选中的租户列表数据格式化为checkbox的options数据
//   const formatDataToCheckboxValueType = useCallback(
//     (list: DepartmentMemberBackType[]) => {
//       const checkboxValueTypeArr: CheckboxValueType[] = [];
//       list.map((item: DepartmentMemberBackType) => {
//         checkboxValueTypeArr.push(item.key);
//         return item;
//       });
//
//       return checkboxValueTypeArr;
//     },
//     [],
//   );
//   return (
//     <Modal
//       title={modalTitle}
//       open={visible}
//       onCancel={handleCancel}
//       maskClosable={false}
//       footer={
//         <Space>
//           <Button onClick={handleCancel}>取消</Button>
//           <Button
//             type="primary"
//             onClick={handleOk}
//             disabled={
//               realCheckedList.length === 0 ||
//               (isSingleSelected && realCheckedList.length !== 1)
//             }
//           >
//             确定
//           </Button>
//         </Space>
//       }
//       destroyOnClose={true}
//       width={660}
//       className={`${styles['member-select-modal-content']}`}
//       wrapClassName={`${styles['member-select-modal']}`}
//     >
//       <Spin spinning={datalistLoading}>
//         <section className={styles['content']}>
//           <div
//             className={`${styles['content-col']} ${styles['content-source']} ${
//               isSingleSelected && realCheckedList.length > 1
//                 ? styles['error']
//                 : ''
//             }`}
//           >
//             <div
//               className={`${styles['header']} ${styles['source-header']}`}
//               ref={SourceHeaderRef}
//             >
//               <SzSearchForm {...searchForm} />
//               {!searchValue && (
//                 <div className={styles['breadcrumb']}>
//                   {breadcrumbList.map(
//                     (item: OneBreadcrumbType, index: number) => {
//                       return (
//                         <label
//                           key={`breadcrumb${item.key}`}
//                           className={styles['breadcrumb-item']}
//                           onClick={() => {
//                             setCurDeptId(item.key);
//                             setBreadcrumbList((preState) => {
//                               const idx = preState.findIndex(
//                                 (state) => state.key === item.key,
//                               );
//                               return preState.slice(0, idx + 1);
//                             });
//                           }}
//                         >
//                           {index !== 0 && <IconFont type="iconxiangyou-xian" />}
//                           <span className={styles['breadcrumb-title']}>
//                             {item.value}
//                           </span>
//                         </label>
//                       );
//                     },
//                   )}
//                 </div>
//               )}
//               {DataListLen > 0 && (
//                 <div className={styles['checked-all']}>
//                   <Checkbox
//                     indeterminate={indeterminate}
//                     onChange={onCheckAllChange}
//                     checked={checkAll}
//                   >
//                     全选
//                   </Checkbox>
//                 </div>
//               )}
//             </div>
//             <div
//               className={`${styles['list']} ${styles['source-list']}`}
//               style={{ height: sourceListHeight }}
//             >
//               {!treeList?.length && !dataList?.length && (
//                 <div className={styles['source-list-empty']}>
//                   <SzEmpty />
//                 </div>
//               )}
//               {!!treeList?.length && (
//                 <dl>
//                   {treeList.map((item: DepartmentMemberBackType) => {
//                     return (
//                       <dt key={item.key}>
//                         <Space>
//                           <IconFont
//                             type="icona-wenjianjia2x"
//                             className={styles['source-list-departmenticon']}
//                           />
//                           <span
//                             className={styles['source-list-departmentname']}
//                           >
//                             {item.value}
//                           </span>
//                         </Space>
//                         <span
//                           className={styles['source-list-next']}
//                           onClick={() => {
//                             setCurDeptId(item.key);
//                             setBreadcrumbList((preState) => {
//                               return [
//                                 ...preState,
//                                 {
//                                   key: item.key,
//                                   value: item.value,
//                                 },
//                               ];
//                             });
//                           }}
//                         >
//                           <IconFont type="iconxiaji" /> 下级
//                         </span>
//                       </dt>
//                     );
//                   })}
//                 </dl>
//               )}
//               {!!dataList?.length && (
//                 <CheckboxGroup
//                   className={styles['source-list-group']}
//                   options={formatDataToCheckboxOptionType(dataList)}
//                   value={checkedList}
//                   onChange={onCheckGroupChange}
//                 />
//               )}
//             </div>
//           </div>
//           <div
//             className={`${styles['content-col']} ${styles['content-target']}`}
//           >
//             <div className={`${styles['header']} ${styles['target-header']}`}>
//               <span>
//                 {modalSelectedTip}
//                 {realCheckedList.length}人
//               </span>
//               {realCheckedList.length > 0 && (
//                 <Button
//                   onClick={() => {
//                     onCheckGroupChange([]);
//                     setRealCheckedList([]);
//                   }}
//                   className={styles['clear-all']}
//                 >
//                   清空
//                 </Button>
//               )}
//             </div>
//             {realCheckedList.length > 0 && (
//               <div className={`${styles['list']} ${styles['target-list']}`}>
//                 {realCheckedList.map(
//                   (item: SelectedMemberInfoType, index: number) => {
//                     return (
//                       <div
//                         key={item.key}
//                         className={styles['target-list-item']}
//                       >
//                         {TenantInfoElem(item, 'target')}
//                         <span
//                           className={styles['delete']}
//                           onClick={() => {
//                             // 从realCheckedList中删除该条数据
//                             const newRealCheckedList: SelectedMemberInfoType[] =
//                               [...realCheckedList];
//                             newRealCheckedList.splice(index, 1);
//                             updateSourceChecked(newRealCheckedList);
//                             setRealCheckedList(newRealCheckedList);
//                           }}
//                         >
//                           <IconFont
//                             type={'iconguanbi'}
//                             className={styles['icon']}
//                           />
//                         </span>
//                       </div>
//                     );
//                   },
//                 )}
//               </div>
//             )}
//           </div>
//         </section>
//         {isSingleSelected && realCheckedList.length > 1 && (
//           <div className={styles['tip-single-error']}>最多只能选择一个</div>
//         )}
//       </Spin>
//     </Modal>
//   );
// };
//
// export default MemberSelectModal;

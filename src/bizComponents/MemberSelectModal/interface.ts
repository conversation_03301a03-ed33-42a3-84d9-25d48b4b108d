import type { DefaultOptionType } from 'antd/lib/select/index.d';

// 真实选中数据存储信息
export interface SelectedMemberInfoType extends DefaultOptionType {
  /** 代码补充，与value值一致 */
  key?: string;
  /** 负责人icon */
  avatar?: string;
}

// 弹窗需要的配置信息
export interface SelectModalConfigType {
  // 弹窗标题， 默认‘选择成员’
  modalTitle?: string;
  // 右侧已选择提示语， 默认‘已选择：’
  modalSelectedTip?: string;
  // 值是否是单选, 默认为false
  isSingleSelected?: boolean;
}

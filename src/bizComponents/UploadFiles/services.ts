// 调用流程见： https://ai-indeed.feishu.cn/docx/OxmydtcMJoVZStxBDpwcN3bKn6b
import { request } from 'umi';

// 上传接口超时时间， 1小时超时时间
const UploadTimeout = 60 * 60 * 60;

/** 获取uploadToken的请求参数 */
interface GetUploadTokenParamsType {
  /** 文件md5：按照文件内容 */
  fileMd5: string;
  /** 文件名称 */
  fileName: string;
}

/** 获取uploadToken的返回信息 */
interface GetUploadTokenBackType {
  /** 文件是否存在 */
  fileExist: boolean;
  /** 存在文件的id */
  fileId?: number;
  /** 文件服务器类型 */
  type?: 'alioss' | 'minio';
  /** 文件在文件服务器上的路径 */
  filePath?: string;
  /** 阿里oss参数 */
  policy?: string;
  /** 阿里oss参数 */
  signature?: string;
  /** 阿里oss参数 */
  host?: string;
  /** 阿里oss参数 */
  expire?: number;
  /** 阿里oss参数 */
  ossaccessKeyId?: string;
  /** minio上传url */
  url?: string;
}

/** 获取uploadToken */
const getUploadToken = async (params: GetUploadTokenParamsType) => {
  return await request('/file/direct/upload/token', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** oss上传请求地址及参数信息 */
interface UploadFileToAliossParamsType {
  /** 上传请求地址 */
  host: string;
  /** 阿里oss上传key */
  filePath: string;
  /** 阿里oss参数 */
  ossaccessKeyId: string;
  /** 阿里oss参数 */
  policy: string;
  /** 阿里oss参数 */
  signature: string;
  /** 需要上传的文件，且必须在formdata的最后一个位置 */
  file: any;
}

/** 直传文件到alioss文件服务器 */
const uploadFileToAlioss = async (params: UploadFileToAliossParamsType) => {
  const formData = new FormData();
  formData.append('key', params.filePath);
  formData.append('OSSAccessKeyId', params.ossaccessKeyId);
  formData.append('policy', params.policy);
  formData.append('signature', params.signature);
  formData.append('success_action_status', '200');
  formData.append('file', params.file);

  return await request(`https://${params.host}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: formData,
    timeout: UploadTimeout,
  });
};

/** minio上传请求地址及参数信息 */
interface UploadFileToMinioParamsType {
  /** 上传请求地址 */
  url: string;
  /** 需要上传的文件，且必须在formdata的最后一个位置 */
  file: any;
}

/** 直传文件到minio文件服务器， 使用binary方式上传，非form-data */
const uploadFileToMinio = async (params: UploadFileToMinioParamsType) => {
  return await request(params.url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: params.file,
    timeout: UploadTimeout,
  });
};

/**  向服务端保存上传结果请求参数 */
interface ConfirmUploadParamsType {
  /** 文件md5：按照文件内容 */
  fileMd5: string;
  /** 文件名称 */
  fileName: string;
  /** 文件地址 */
  filePath: string;
}

/** 向服务端保存上传结果 */
const saveUploadResult = async (params: ConfirmUploadParamsType) => {
  return await request('/file/direct/upload/confirm', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

export {
  GetUploadTokenParamsType,
  GetUploadTokenBackType,
  getUploadToken,
  UploadFileToAliossParamsType,
  uploadFileToAlioss,
  UploadFileToMinioParamsType,
  uploadFileToMinio,
  ConfirmUploadParamsType,
  saveUploadResult,
};

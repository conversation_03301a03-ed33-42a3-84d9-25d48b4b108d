// import { useState } from 'react';
// import { message, Upload, Modal, Button } from 'antd';
// import type { UploadProps } from 'antd';
// import type { RcFile } from 'antd/lib/upload';
// import type { UploadRequestOption } from 'rc-upload/lib/interface';
// import type { UploadFile } from 'antd/es/upload/interface';
// import { UploadFileTypeEnum, UploadFileTypeTextInfo } from '../types';
// import { generateFileMd5ByFileContent } from '@/utils/crypto/fileMd5';
// import {
//   GetUploadTokenParamsType,
//   getUploadToken,
//   GetUploadTokenBackType,
//   uploadFileToAlioss,
//   uploadFileToMinio,
//   ConfirmUploadParamsType,
//   saveUploadResult,
// } from '../services';
// import type { BaseButtonProps } from 'antd/lib/button/button.d.ts';
//
// import styles from './index.less';
//
// interface Props extends UploadProps {
//   /** 按钮样式 */
//   btnInfo?: {
//     type?: BaseButtonProps['type'];
//     text?: string;
//     overClassName?: string;
//     icon?: BaseButtonProps['icon'];
//   };
//   /** 显示上传流程的盒子高度 */
//   boxHeight?: number;
//   /** 上传类型，不传则默认为所有文件类型均支持 */
//   uploadType?: UploadFileTypeEnum;
//   /** 上传文件大小，不传则默认为不限制，单位字节 */
//   maxSize?: number;
//   value?: UploadFile[];
//   onUploadSuccess: (props: any, options: UploadRequestOption) => Promise<any>;
//   onUploadFailed: () => void;
//   btnLoding?: boolean;
//   updateBtnLoading?: (state: boolean) => void;
// }
//
// const ButtonUpload = (props: Props) => {
//   const {
//     btnInfo,
//     boxHeight,
//     uploadType,
//     maxSize,
//     onUploadSuccess,
//     onUploadFailed,
//     btnLoding = false,
//     updateBtnLoading,
//     ...restProps
//   } = props;
//   // 记录处理上传文件的传输状态
//   /** 是否上传过程中出现错误，如果有错误不展示校验相关结果 */
//   const [fileList, setFileList] = useState<UploadFile[]>([]);
//
//   // 文件上传前基本校验: 1. 文件个数判断 2. 文件大小判断 3. 文件类型判断;
//   const checkBasicFile = (file: RcFile) => {
//     // 文件个数判断（maxCount为1则直接放过，处理为直接覆盖）
//     if (!!restProps?.maxCount && restProps.maxCount !== 1) {
//       const isOverLimitCount = (fileList || []).length >= restProps.maxCount;
//
//       if (!isOverLimitCount) {
//         Modal.warning({
//           title: '提示',
//           content: `上传文件个数已经达到上限: ${restProps.maxCount}！`,
//           centered: true,
//         });
//         return false;
//       }
//     }
//
//     // 文件大小校验
//     if (!!maxSize) {
//       const isCorrectSize = file.size <= maxSize;
//
//       if (!isCorrectSize) {
//         Modal.warning({
//           title: '提示',
//           content: '文件大小不符合要求，请重新上传！',
//           centered: true,
//         });
//         return false;
//       }
//     }
//
//     // 文件类型校验
//     if (!!uploadType) {
//       const typeCheckRegExp =
//         UploadFileTypeTextInfo[uploadType].typeCheckRegExp;
//
//       if (!typeCheckRegExp.test(file.type)) {
//         Modal.warning({
//           title: '提示',
//           content: UploadFileTypeTextInfo[uploadType].errorTypeTip,
//           centered: true,
//         });
//         return false;
//       }
//     }
//
//     return true;
//   };
//
//   /** 上传之前的处理 */
//   const beforeUpload = async (file: RcFile) => {
//     const basicCheckResult = checkBasicFile(file);
//
//     if (!basicCheckResult) {
//       return Upload.LIST_IGNORE;
//     }
//
//     return basicCheckResult;
//   };
//
//   /** 获取uploadToken */
//   const handleGetUploadToken = async (
//     params: GetUploadTokenParamsType,
//     options: UploadRequestOption,
//   ) => {
//     return await getUploadToken(params)
//       .then(({ bizData }: { bizData: GetUploadTokenBackType }) => {
//         return bizData;
//       })
//       .catch((e) => {
//         // console.log('获取uploadToken异常', e);
//
//         Modal.warning({
//           title: '提示',
//           content: `上传失败，请稍候再试～`,
//           centered: true,
//           onOk: () => {
//             updateBtnLoading?.(false);
//           },
//         });
//
//         options?.onError?.(e);
//         onUploadFailed();
//
//         return null;
//       });
//   };
//
//   /** 上传文件到alioss文件服务, false-上传失败，true-上传成功 */
//   const handleUploadFileToService = async (
//     params: {
//       uploadInfo: GetUploadTokenBackType;
//       file: RcFile;
//     },
//     options: UploadRequestOption,
//   ) => {
//     const { uploadInfo, file } = params;
//     const serverType = uploadInfo?.type;
//     if (!serverType) {
//       Modal.warning({
//         title: '提示',
//         content: `未找到存储服务，请稍候再试～`,
//         centered: true,
//         onOk: () => {
//           updateBtnLoading?.(false);
//         },
//       });
//
//       options?.onError?.(new Error());
//       onUploadFailed();
//
//       return false;
//     }
//     if (serverType === 'alioss') {
//       return await uploadFileToAlioss({
//         host: uploadInfo.host!,
//         filePath: uploadInfo.filePath!,
//         ossaccessKeyId: uploadInfo.ossaccessKeyId!,
//         policy: uploadInfo.policy!,
//         signature: uploadInfo.signature!,
//         file,
//       })
//         .then(() => {
//           return true;
//         })
//         .catch((e) => {
//           // console.log('上传到alioss失败', e);
//           Modal.warning({
//             title: '提示',
//             content: `上传失败，请稍候再试～`,
//             centered: true,
//             onOk: () => {
//               updateBtnLoading?.(false);
//             },
//           });
//
//           options?.onError?.(e);
//           onUploadFailed();
//
//           return false;
//         });
//     } else if (serverType === 'minio' && !!uploadInfo?.url) {
//       return await uploadFileToMinio({ url: uploadInfo.url, file })
//         .then(() => {
//           return true;
//         })
//         .catch((e) => {
//           // console.log('上传到minio失败', e);
//           Modal.warning({
//             title: '提示',
//             content: `上传失败，请稍候再试～`,
//             centered: true,
//             onOk: () => {
//               updateBtnLoading?.(false);
//             },
//           });
//
//           options?.onError?.(e);
//           onUploadFailed();
//
//           return false;
//         });
//     }
//
//     return false;
//   };
//
//   /** 向服务器保存文件 */
//   const handleSaveUploadResult = async (
//     params: ConfirmUploadParamsType,
//     options: UploadRequestOption,
//   ) => {
//     return await saveUploadResult(params)
//       .then(({ bizData }: { bizData: string }) => {
//         return bizData;
//       })
//       .catch((e) => {
//         console.log('保存到服务器失败', e);
//         Modal.warning({
//           title: '提示',
//           content: `上传失败，请稍候再试～`,
//           centered: true,
//           onOk: () => {
//             updateBtnLoading?.(false);
//           },
//         });
//
//         options?.onError?.(e);
//         onUploadFailed();
//
//         return null;
//       });
//   };
//
//   /** 自定义文件上传过程 */
//   const customRequest = async (options: UploadRequestOption) => {
//     updateBtnLoading?.(true);
//     const file = options.file as RcFile;
//     // 生成md5值
//     const fileMd5 = await generateFileMd5ByFileContent(file);
//     // 获取uploadToken
//     const uploadTokenInfo: GetUploadTokenBackType | null =
//       await handleGetUploadToken(
//         {
//           fileName: file.name,
//           fileMd5,
//         },
//         options,
//       );
//
//     if (!!uploadTokenInfo) {
//       const {
//         fileExist,
//         fileId,
//         type,
//         filePath = '',
//       } = uploadTokenInfo as GetUploadTokenBackType;
//
//       // 显示上传的文件信息和状态
//       if (!!restProps?.maxCount && restProps.maxCount === 1) {
//         // 长度为1时直接替换
//         setFileList([
//           {
//             uid: file.uid,
//             name: file.name,
//           },
//         ]);
//       } else {
//         setFileList((prelist) => {
//           prelist.push({
//             uid: file.uid,
//             name: file.name,
//           });
//           return [...prelist];
//         });
//       }
//       if (fileExist) {
//         // 文件已经在服务器，通知服务端做业务处理
//         await onUploadSuccess(
//           {
//             filePath: fileId,
//             fileName: file.name,
//             fileMd5,
//             fileSize: file.size,
//           },
//           options,
//         );
//       } else {
//         // 新增上传
//         const isUploadSuccessFileService = await handleUploadFileToService(
//           {
//             uploadInfo: uploadTokenInfo,
//             file,
//           },
//           options,
//         );
//
//         if (isUploadSuccessFileService) {
//           // 向服务端保存上传结果
//           const saveFileResult: string | null = await handleSaveUploadResult(
//             {
//               fileName: file.name,
//               fileMd5,
//               filePath,
//             },
//             options,
//           );
//
//           if (!!saveFileResult) {
//             // 通知服务端做业务处理
//             await onUploadSuccess(
//               {
//                 filePath: saveFileResult,
//                 fileName: file.name,
//                 fileMd5,
//                 fileSize: file.size,
//               },
//               options,
//             );
//           } else {
//             Modal.warning({
//               title: '提示',
//               content: '保存到服务器错误',
//               centered: true,
//             });
//           }
//         } else {
//           Modal.warning({
//             title: '提示',
//             content: `上传到${type}文件服务异常`,
//             centered: true,
//           });
//         }
//       }
//     }
//   };
//
//   return (
//     <div className={styles['sz-file-upload']}>
//       <Upload
//         accept={
//           !!uploadType ? UploadFileTypeTextInfo[uploadType].accept : undefined
//         }
//         style={!!boxHeight ? { height: boxHeight } : {}}
//         beforeUpload={beforeUpload}
//         customRequest={customRequest}
//         onChange={(info: any) => {
//           if (info.file.status === 'done') {
//             message.success(`${info.file.name} 文件上传成功`);
//           }
//         }}
//         {...restProps}
//       >
//         <Button
//           loading={btnLoding}
//           className={`${styles['btn-upload']} ${btnInfo?.overClassName}`}
//           type={btnInfo?.type || 'default'}
//           icon={btnInfo?.icon}
//         >
//           {btnInfo?.text || '点击上传'}
//         </Button>
//       </Upload>
//     </div>
//   );
// };
//
// export default ButtonUpload;

/** 上传文件类型 */
export enum UploadFileTypeEnum {
  ZIP = 'zip', // zip格式
  IMAGE = 'image', // 图片
  EXCEL = 'excel', // excel
  XLS = 'xls', // xls或xlsx
  TXT = 'txt', // txt
}

/** 上传文件格式相关信息 */
export const UploadFileTypeTextInfo = {
  [UploadFileTypeEnum.ZIP]: {
    accept: 'application/zip,application/x-zip,application/x-zip-compressed',
    typeCheckRegExp:
      /^(application\/zip|application\/x-zip|application\/x-zip-compressed)$/,
    types: ['zip'],
    supportTip: '仅支持扩展名：zip',
    errorTypeTip: '请选择zip类型文件上传',
  },
  [UploadFileTypeEnum.IMAGE]: {
    accept: 'image/*',
    typeCheckRegExp: /^(image\/)/,
    types: null,
    supportTip: '文件类型支持：jpg、png、bmp、jpeg等图片类型',
    errorTypeTip: '请选择图片类型文件上传',
  },
  [UploadFileTypeEnum.EXCEL]: {
    accept: '.xlsx,application/vnd.ms-excel,text/csv',
    typeCheckRegExp:
      /^(application\/vnd.openxmlformats-officedocument.spreadsheetml.sheet|application\/vnd.ms-excel|text\/csv)$/,
    types: ['excel', 'xls', 'xlsx', 'csv'],
    supportTip: '文件类型支持：excel、xls、xlsx、csv',
    errorTypeTip: '请选择excel/xls/xlsx/csv格式文件上传',
  },
  [UploadFileTypeEnum.XLS]: {
    accept: '.xlsx,application/vnd.ms-excel',
    typeCheckRegExp:
      /^(application\/vnd.openxmlformats-officedocument.spreadsheetml.sheet|application\/vnd.ms-excel)$/,
    types: ['xls', 'xlsx'],
    supportTip: '支持 xls、xlsx格式文件',
    errorTypeTip: '请选择xls/xlsx格式文件上传',
  },
  [UploadFileTypeEnum.TXT]: {
    accept: 'text/plain',
    typeCheckRegExp: /^text\/plain$/,
    types: ['txt'],
    supportTip: 'txt',
    errorTypeTip: '请选择txt类型文件上传',
  },
};

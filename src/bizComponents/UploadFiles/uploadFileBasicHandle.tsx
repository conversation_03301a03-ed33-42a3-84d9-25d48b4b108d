// import type { RcFile } from 'antd/lib/upload';
// import { Modal } from 'antd';

// zyj-todo 抽取上传文件基本操作方法
export const FileHandler = {
  // 文件上传前基本校验: 1. 文件类型判断; 2. 大小判断
  // checkBasicFile: (
  //   file: RcFile,
  //   fileLimitConfig?: {
  //     maxSize?: number;
  //     minSize?: number;
  //     textInfo?: Record<string, string>
  //   },
  // ) => {
  //   // 文件类型校验
  //   if (!!uploadType) {
  //     const typeCheckRegExp =
  //       UploadFileTypeTextInfo[uploadType].typeCheckRegExp;
  //     if (!typeCheckRegExp.test(file.type)) {
  //       Modal.warning({
  //         title: '提示',
  //         content: UploadFileTypeTextInfo[uploadType].errorTypeTip,
  //         centered: true,
  //       });
  //       return false;
  //     }
  //   }
  //   // 文件大小校验
  //   if (!!maxSize) {
  //     const isCorrectSize = file.size <= maxSize;
  //     if (!isCorrectSize) {
  //       Modal.warning({
  //         title: '提示',
  //         content: '文件大小不符合要求，请重新上传！',
  //         centered: true,
  //       });
  //       return false;
  //     }
  //   }
  //   return true;
  // },
};

import { InboxOutlined } from '@ant-design/icons';
import Ternary from '@deopCmd/components/Ternary';
import { UploadService } from '@deopCmd/services/uploadService';
import { tryit } from '@deopCmd/utils/tryit';
import { message, Upload } from 'antd';
import type { DraggerProps, UploadFile } from 'antd/lib/upload';
import type { UploadRequestOption } from 'rc-upload/lib/interface';
import React, { useRef, useState } from 'react';

interface Props extends DraggerProps {
  // 传入自定义的customRequest 不会触发 onUploadSuccess 事件
  onUploadSuccess?: (
    fileInfo: {
      md5: string;
      fileId: string;
      filePath?: string;
      fileName: string;
      fileSize: number;
    },
    options: UploadRequestOption,
  ) => void;
  icon?: React.ReactNode;
  text?: string;
  hint?: string;
}

/** 客户端管理使用了 */
const DragUpload: React.FC<Props> = ({
  icon = <InboxOutlined rev="" />,
  text = '点击或将文件拖拽到这里上传',
  hint,
  children,
  customRequest,
  onRemove,
  ...dragProps
}) => {
  const [] = useState();
  const ref = useRef<UploadService>();

  const _customRequest =
    customRequest ||
    (async (options) => {
      options.onProgress?.({ percent: 30 });

      const oss = new UploadService();

      if (ref.current) ref.current = oss;

      const [err, fileInfo] = await tryit(oss.upload)(options.file as File);
      if (err) {
        if (err.name === 'CanceledError') message.info('取消上传');
        // @ts-ignore
        return options.onError?.();
      }

      options.onProgress?.({ percent: 70 });

      if (dragProps.onUploadSuccess) {
        dragProps.onUploadSuccess?.(fileInfo!, options);
      } else {
        options.onSuccess?.(fileInfo);
      }
    });

  const handleRemove = (file: UploadFile) => {
    ref.current?.abortUpload();
    onRemove?.(file);
  };

  return (
    <>
      <Upload.Dragger
        className="bg-[rgba(222,226,230,0.12) h-[140px] w-full items-center justify-center rounded border border-solid"
        customRequest={_customRequest}
        onRemove={handleRemove}
        {...dragProps}>
        <Ternary
          condition={React.isValidElement(children)}
          falseElement={
            <>
              <p className="ant-upload-drag-icon">{icon}</p>
              <p className="ant-upload-text">{text}</p>
              <p className="ant-upload-hint">{hint}</p>
            </>
          }>
          <>{children}</>
        </Ternary>
      </Upload.Dragger>
      {/* <Button
        onClick={() => {
          oss.abortUpload();
        }}>
        取消上传
      </Button> */}
    </>
  );
};

export default DragUpload;

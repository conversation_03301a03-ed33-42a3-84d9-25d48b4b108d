import React, { useMemo } from 'react';

import { loopUpdateTreeData } from '@deopCmd/constants/formatDepartmentTree';
import * as API from '@deopCmd/services/common';
import { useRequest } from 'ahooks';

import { TreeSelect } from 'antd';
import { GetProps } from 'main/types';

/**
 * 部门树下拉组件
 * 使用示例： /deopCmd/monitoringcenter/robotMonitoring
 * TODO: 如果部门书缩进非常多怎么办
 */
const DepartmentSelect: React.FC<GetProps<typeof TreeSelect<string, ReturnType<typeof loopUpdateTreeData>[number]>>> = (
  props,
) => {
  const { data: departmentTreeData } = useRequest(API.getDeptTree);

  const departmentTree = useMemo(() => {
    if (departmentTreeData?.id) {
      const res = loopUpdateTreeData([departmentTreeData]);
      return res;
    }
    return [];
  }, [departmentTreeData]);

  // 所有的部门ID
  // const AllDeptKeyValue = useMemo(
  //   () => getAllDeptKeys(departmentTree || []),
  //   [departmentTree],
  // );

  // const checkModeProps = {
  //   treeNodeFilterProp: 'title',
  //   treeCheckable: true,
  //   showCheckedStrategy: SHOW_ALL,
  //   treeCheckStrictly: true,
  // };

  return (
    <>
      <TreeSelect
        dropdownMatchSelectWidth={false}
        allowClear
        placeholder="请选择所属部门"
        className="min-w-[230px]"
        treeData={departmentTree}
        dropdownRender={(originNode) => {
          return <div className="min-w-[230px]">{originNode}</div>;
        }}
        {...props}
        // {...checkModeProps}
      />
    </>
  );
};

export default DepartmentSelect;

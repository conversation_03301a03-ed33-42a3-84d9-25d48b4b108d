import { PaginationParamsType } from '@/interface.d';

export interface TagItem {
  color: string;
  id: string;
  tagName: string;
  status: number;
}

/** 请求参数 */
export interface QueryListParams extends PaginationParamsType {
  tagName?: string;
  status?: number;
  startModifyTime?: string;
  endModifyTime?: string;
}

export interface QuateSearchParams extends PaginationParamsType {
  /**
   * 引用人
   */
  createByName?: string;
  /**
   * 表单ID
   */
  depLowCodeId?: number;
  /**
   * 引用类型
   */
  quoteType?: string;
  /**
   * 开始引用时间
   */
  startCreateTime?: string;
  /**
   * 结束引用时间
   */
  endCreateTime?: string;
}

export interface DetailParams {
  /**
   * 表单id
   */
  id?: string;
  /**
   * 账号授权范围：1全部账号，0指定
   */
  accountRange: number;
  /**
   * 描述
   */
  description: string;
  /**
   * 表单项
   */
  items: { [key: string]: any }[];
  /**
   * 表单名称
   */
  name: string;
  /**
   * 状态：0关，1开
   */
  status: number;
  /**
   * 用户ids，如果accountRange为1不用传
   */
  userIds?: number[] | string[];
}

/** 创建、编辑表单：1-第一步，2-第二步 */
export enum StepNumberEnum {
  ONE = 0,
  TWO = 1,
}

/** 类型枚举 */
export enum TypeEnum {
  DEFAULT = 0,
  ADD = 1,
}

/** 类型 */
export const TypeInfo = {
  [TypeEnum.DEFAULT]: '默认',
  [TypeEnum.ADD]: '新增',
};

/** 类型 */
export const TypeArr = [
  {
    name: '默认',
    key: [TypeEnum.DEFAULT],
  },
  {
    name: '新增',
    key: [TypeEnum.ADD],
  },
];

/** 状态枚举 */
export enum StatusEnum {
  OPEN = 1,
  CLOSE = 0,
}

/** 状态 */
export const StatusInfo = {
  [StatusEnum.OPEN]: '开启',
  [StatusEnum.CLOSE]: '关闭',
};

/** 类型 */
export const StatusArr = [
  {
    name: '开启',
    key: StatusEnum.OPEN,
  },
  {
    name: '关闭',
    key: StatusEnum.CLOSE,
  },
];

/** 引用类型 */
export enum QuateTypeEnum {
  MACHINE = 'HUMAN_MACHINE_TASK',
  PROGCESS = 'PROCESS_ORCHESTRATION',
  DEMAND = 'BUSINESS_APPROVAL',
  BUSINESS = 'NEED_MANAGER',
  DESIGNER = 'PROCESS_DEVELOP',
}

/** 引用类型列表 */
export const QuateTypeArr = [
  {
    name: '人机任务',
    key: QuateTypeEnum.MACHINE,
  },
  {
    name: '流程编排',
    key: QuateTypeEnum.PROGCESS,
  },
  {
    name: '需求管理',
    key: QuateTypeEnum.BUSINESS,
  },
  {
    name: '业务审批',
    key: QuateTypeEnum.DEMAND,
  },
  {
    name: '设计器流程开发',
    key: QuateTypeEnum.DESIGNER,
  },
];

export interface userInfo {
  relationType: string;
  code: string;
  city: string;
  joinTime: string;
  nickName: string;
  mobile: string;
  tenantCode: string;
  avatar: string;
  type: number;
  countryOrArea: string;
  enable: any;
  name: string;
  tenantId: string;
  depId: string;
  id: string;
  state: number;
  position: string;
  deptAndUserGroup: string;
  email: string;
  account: string;
  userId: string;
}

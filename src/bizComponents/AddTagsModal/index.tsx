import { Modal, Form, Tooltip, Tag, Switch, Space } from 'antd';
import React, { useState, useEffect } from 'react';
import {
  SzSearchForm,
  SzTable,
  GetPopupContainerLayerType,
} from 'main/SzComponents';
import * as Api from './services';
import { PaginationInitData } from '@/constants';
import { setIdxForPaginationData } from '@/utils/utils';
import { TagItem, QueryListParams, StatusEnum, StatusArr } from './types';
import NiceModal, { useModal } from '@ebay/nice-modal-react';

interface Props {
  visible: boolean;
  deafaultSelectedRows?: TagItem[];
  onOk: (selectedRows: TagItem[]) => void;
  onCancel: () => void;
}

const AddTagsModal: React.FC<Props> = (props) => {
  const { visible, deafaultSelectedRows, onOk, onCancel } = props;
  const [tableData, setTableData] = useState<any>({});
  const [modalSearchForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>(
    deafaultSelectedRows?.map((item) => item.id) || [],
  );
  const [selectedRows, setSelectedRows] = useState<TagItem[]>(
    deafaultSelectedRows || [],
  );

  const queryList = async (params?: QueryListParams) => {
    setLoading(true);
    const values = modalSearchForm.getFieldsValue();
    const newParams = params || {};
    newParams.pageSize =
      newParams?.pageSize ||
      Number(tableData.size || 0) ||
      PaginationInitData.pageSize;
    const query = {
      ...values,
      ...newParams,
    };
    Api.queryTableList(query)
      .then(({ bizData }) => {
        setIdxForPaginationData(
          bizData?.records || [],
          newParams?.pageNo,
          newParams?.pageSize,
        );
        setTableData(bizData || {});
        // console.log('bizData', bizData);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const resetPageNumAndQueryList = () => {
    queryList({
      pageNo: 1,
    });
  };

  // 列数据
  const columns = [
    {
      title: '标签',
      dataIndex: 'tagName',
      render: (text: string, record: TagItem) => {
        return (
          <Tooltip
            title={text}
            placement="top"
            getPopupContainer={(triggerNode) => triggerNode}
          >
            <Tag color={record.color}>{text}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (_: any, record: TagItem) => {
        return (
          <Space>
            <Switch
              checked={Boolean(record.status)}
              unCheckedChildren="禁用"
              checkedChildren="启用"
              disabled={true}
            />
          </Space>
        );
      },
    },
  ];

  const searchForm = {
    formData: [
      {
        name: 'tagName',
        component: {
          name: 'Search',
          allowClear: true,
          style: { width: 200 },
          placeholder: '请输入流程名称',
          onSearch(value: string) {
            queryList({ tagName: value });
          },
        },
      },
      {
        name: 'status',
        component: {
          name: 'Select',
          popupContainerLayer: GetPopupContainerLayerType.PROGRIDCONTENT,
          placeholder: '请选择状态',
          allowClear: true,
          style: { width: 200 },
          options: {
            data: StatusArr,
            keyStr: 'key',
            valStr: 'name',
          },
          onChange() {
            resetPageNumAndQueryList();
          },
        },
      },
    ],
    actionData: [],
    form: modalSearchForm,
    initialValues: {},
  };

  useEffect(() => {
    if (visible) {
      resetPageNumAndQueryList();
      setSelectedRowKeys(deafaultSelectedRows?.map((item) => item.id) || []);
      setSelectedRows(deafaultSelectedRows || []);
    }
  }, [visible, deafaultSelectedRows]);

  // rowSelection object indicates the need for row selection
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: string[], selectedRows: TagItem[]) => {
      // console.log(
      //   `selectedRowKeys: ${selectedRowKeys}`,
      //   'selectedRows: ',
      //   selectedRows,
      // );
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    getCheckboxProps: (record: TagItem) => ({
      disabled: record?.status === StatusEnum['CLOSE'], // Column configuration not to be checked
    }),
  };

  return (
    <Modal
      title="添加标签"
      open={visible}
      onOk={() => onOk(selectedRows)}
      onCancel={onCancel}
      width={500}
    >
      <SzSearchForm {...searchForm} />
      <SzTable
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        rowKey={'id'}
        tableData={tableData}
        columns={columns}
        loading={loading}
        onPageChange={(currentPage: number, pageSize: number) => {
          queryList({ pageNo: currentPage, pageSize: pageSize });
        }}
      />
    </Modal>
  );
};

export const AddTags = NiceModal.create((props: Props) => {
  const modal = useModal();

  return (
    <AddTagsModal
      {...props}
      visible={modal.visible}
      onCancel={() => {
        modal.remove();
      }}
      onOk={(val) => {
        modal.resolve(val);
        modal.remove();
      }}
    />
  );
});

export default AddTagsModal;

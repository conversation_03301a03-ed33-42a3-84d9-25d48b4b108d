/* eslint-disable */
import G6 from '@antv/g6';
import DagreLayout from '@antv/g6/lib/layout/dagre';
import './event';
import { EdgeStyleType, FlowBlockType } from '../../types';
import { IEdgeModel } from '../../interface';
import { NodeConfig } from '@antv/g6/lib/types';
const dagre = require('dagre');

// dagrejs Wiki https://github.com/dagrejs/dagre/wiki#configuring-the-layout
// 改写g6 dagre布局
G6.registerLayout(
  'sz-layout',
  {
    // 【自定义】
    // @ts-ignore
    sortEdges(node: NodeConfig, edges: IEdgeModel[]) {
      if (node.shape === FlowBlockType.Judge) {
        // @ts-ignore
        return edges.sort((last, next) => {
          // if (last.styleType === EdgeStyleType.Normal) return -1
          // else if (next.styleType === EdgeStyleType.Normal) return 1

          if (last.styleType === EdgeStyleType.No) return 1;
          else if (next.styleType === EdgeStyleType.No) return -1;
        });
      } else {
        return edges.sort(
          (last, next) => last.sourceAnchor - next.sourceAnchor,
        );
      }
    },
    execute() {
      const self = this;
      // @ts-ignore
      const { nodes, nodeSizeFunc = noop, getEdgesFunc = noop } = self;
      if (!nodes) return;
      // const edges = self.edges.sort((cur, next) => cur.sourceAnchor - next.sourceAnchor) || []
      const g = new dagre.graphlib.Graph();

      // @ts-ignore
      self.ranker = 'longest-path';
      g.setDefaultEdgeLabel(() => ({}));
      g.setGraph(self);

      const edges: any[] = [];
      nodes.forEach((node) => {
        const [width, height] = nodeSizeFunc(node) || [40, 40];
        const outEdges = getEdgesFunc(node);
        // @ts-ignore
        const sortEdges = self.sortEdges(node, outEdges);
        edges.push(...sortEdges);

        g.setNode(node.id, { width, height });
      });

      edges.forEach((edge) => {
        g.setEdge(edge.source, edge.target, { weight: 1 });
      });
      dagre.layout(g);

      let coord;
      g.nodes().forEach((node: any) => {
        coord = g.node(node);
        const i = nodes.findIndex((it) => it.id === node);
        nodes[i].x = coord.x - coord.width / 2;
        nodes[i].y = coord.y - coord.height / 2;
      });
      g.edges().forEach((edge: any) => {
        coord = g.edge(edge);
        const i = edges.findIndex(
          (it) => it.source === edge.v && it.target === edge.w,
        );
        // @ts-ignore
        if (i >= 0 && self.controlPoints && edges[i]?.type !== 'loop') {
          edges[i].controlPoints = coord.points.slice(
            1,
            coord.points.length - 1,
          );
        }
      });
    },
  },
  DagreLayout,
);

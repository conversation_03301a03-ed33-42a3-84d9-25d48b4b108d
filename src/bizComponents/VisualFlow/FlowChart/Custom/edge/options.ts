import { EdgeShapeName } from '../../types';

export const edgeShapeOptions = {
  name: EdgeShapeName.EdgeShape,
  style: {
    stroke: '#6B7799',
    lineWidth: 1,
    lineAppendWidth: 1,
    lineDash: [],
    endArrow: {
      path: 'M 0,0 L 6,4 L 6,-4 Z',
      fill: '#7B85A1',
    },
  },
  no: {
    stroke: '#ff6151',
    endArrow: {
      path: 'M 0,0 L 6,4 L 6,-4 Z',
      fill: '#ff6151',
    },
  },
  yes: {
    stroke: '#2FC37C',
    endArrow: {
      path: 'M 0,0 L 6,4 L 6,-4 Z',
      fill: '#2FC37C',
    },
  },
  error: {
    stroke: '#F96056',
    endArrow: {
      path: 'M 0,0 L 6,4 L 6,-4 Z',
      fill: '#F96056',
    },
  },
  stateStyles: {
    hover: {
      stroke: '#2F9AFF',
      lineWidth: 2,
      endArrow: {
        path: 'M 0,0 L 6,4 L 6,-4 Z',
        fill: '#2F9AFF',
      },
    },
  },
};

export const textShapeOptions = {
  name: EdgeShapeName.TextShape,
  style: {
    fontSize: 12,
    fontWeight: 400,
    fill: '#666',
    textBaseline: 'middle',
    textAlign: 'center',
  },
  no: {
    text: 'no',
    fill: '#ff6151',
  },
  yes: {
    text: 'yes',
    fill: '#2FC37C',
  },
};

export const rectShapeOptions = {
  name: EdgeShapeName.RectShape,
  style: {
    fill: '#E1E4E8',
    radius: [2, 2, 2, 2],
  },
};

export const deleteGroupOptions = {
  [EdgeShapeName.IconShape]: {
    name: EdgeShapeName.IconShape,
    style: {
      text: '\ue709',
      fontFamily: 'iconfont',
      fill: '#FFF',
      fontSize: 16,
      fontWeight: 400,
      textBaseline: 'middle' as
        | 'middle'
        | 'top'
        | 'hanging'
        | 'alphabetic'
        | 'ideographic'
        | 'bottom'
        | undefined,
      cursor: 'pointer',
      shadowColor: 'rgba(0,0,0,0.06)',
      shadowOffsetY: 2,
      shadowBlur: 8,
    },
    stateStyles: {
      hover: {
        fontSize: 17,
      },
    },
    calcCoord(point: any) {
      return {
        x:
          point.x -
          deleteGroupOptions[EdgeShapeName.IconShape].style.fontSize / 2,
        y: point.y,
      };
    },
  },
  [EdgeShapeName.DeleteShape]: {
    name: EdgeShapeName.DeleteShape,
    style: {
      r: 12,
      fill: '#2F9AFF',
      cursor: 'pointer',
    },
    stateStyles: {
      hover: {
        r: 13,
      },
    },
  },
};

/* eslint-disable */
import G6 from '@antv/g6';
import {
  edgeShapeOptions,
  textShapeOptions,
  rectShapeOptions,
} from './options';
import { EdgeType, EdgeGroupName, EdgeStyleType } from '../../types';
import { IEdgeModel } from '../../interface';
import { IGroup } from '@antv/g-base/lib/interfaces.d';
import { IEdge } from '@antv/g6/lib/interface/item';
import { Item, IPoint, IBBox } from '@antv/g6/lib/types';
import { IShape } from '@antv/g-base/lib/interfaces';
import './event';

const uniqBy = (arr: any[], key: any) => {
  const result: any[] = [];
  arr.forEach((i) => {
    if (!result.find((r) => r[key] === i[key])) result.push(i);
  });
  return result;
};

G6.registerEdge(
  EdgeType,
  {
    options: edgeShapeOptions,
    // 去除edge自带的label 使用自带的textShape
    drawLabel(): IShape {
      // @ts-ignore
      return null;
    },
    updateLabel() {}, // 去除edge自带的label 使用自带的textShape
    setState(state, value, item) {
      item?.toFront();
      switch (state) {
        case 'hover': {
          const currentStates = item?.getStates();
          if (currentStates?.indexOf('selected') !== -1) break;

          this.updateShape(state, value, item);
          break;
        }
        default:
          break;
      }
    },

    // 【自定义】更新allShape属性
    updateShape(
      state: string | undefined,
      value: string | boolean | undefined,
      item: Item | undefined,
    ) {
      if (!item || !state) return;
      const { stateStyles, style } = this.options!;

      const { id, styleType } = item.getModel();
      const group = item.getContainer();
      const keyShape = group.getFirst();
      // @ts-ignore
      keyShape.attr(
        value
          ? stateStyles?.[state]
          : // @ts-ignore
            { ...style, ...this.options?.[styleType] },
      );
    },

    // 【覆盖-自定义】
    // @ts-ignore
    draw(cfg: IEdgeModel, group: IGroup) {
      const { styleType = EdgeStyleType.Normal } = cfg;
      const points = this.getPoints(cfg);
      const path = this.getPath(points);
      cfg.centerPoint = this.getCenterPoint(points);

      const keyShape = group.addShape('path', {
        attrs: {
          ...this.options?.style,
          // @ts-ignore
          ...this.options?.[styleType],
          path,
          styleType,
        },
        name: this.options?.name,
      });

      return keyShape;
    },
    // 【覆盖-自定义】
    // @ts-ignore
    afterDraw(cfg: IEdgeModel, group: IGroup) {
      if ((textShapeOptions as any)[cfg.styleType]) {
        this.addTextGroup(cfg, group);
      }
    },

    // 【覆盖-自定义】
    // @ts-ignore
    afterUpdate(cfg: IEdgeModel, item: IEdge) {
      const { id } = cfg;
      const points = this.getPoints(cfg);
      cfg.centerPoint = this.getCenterPoint(points);
      const { x, y } = cfg.centerPoint;

      const group = item.getContainer();

      for (const groupName in EdgeGroupName) {
        // @ts-ignore
        const sGroup = group.findById(`${id}-${EdgeGroupName[groupName]}`);
        if (sGroup) {
          sGroup.resetMatrix();
          sGroup.move(x, y);
        }
      }

      if (!item.getStates().length) {
        this.updateShape('', false, item);
      }
    },

    // 【自定义】添加textGroup
    addTextGroup(cfg: IEdgeModel, group: IGroup) {
      const { id, centerPoint, labelCfg = {}, styleType } = cfg;
      const { x, y } = centerPoint;

      const textGroup = group.addGroup({
        id: `${id}-${EdgeGroupName.TextShape}`,
        name: EdgeGroupName.TextShape,
      });

      textGroup.move(x, y);

      const textShape = textGroup.addShape('text', {
        attrs: {
          ...textShapeOptions.style,
          // @ts-ignore
          ...textShapeOptions[styleType],
          ...labelCfg,
        },
        name: textShapeOptions.name,
      });

      const bbox = textShape.getBBox();

      const rectShape = textGroup.addShape('rect', {
        attrs: {
          ...rectShapeOptions.style,
          x: bbox.x - 4,
          y: bbox.y,
          width: bbox.width + 8,
          height: bbox.height + 4,
        },
        name: rectShapeOptions.name,
      });

      rectShape.toBack();
    },

    // 【自定义】
    getPoints(cfg: IEdgeModel) {
      const startPoint = cfg.startPoint;
      const endPoint = cfg.endPoint;
      // @ts-ignore
      const controlPoints = this.getControlPoints(cfg);
      let points = [startPoint];

      if (controlPoints) {
        points = points.concat(controlPoints);
      }
      points.push(endPoint);

      return points;
    },

    // 【自定义】
    getCenterPoint(points: IPoint[]) {
      const edgeLenght = points?.reduce((pre, curPoint, index) => {
        if (!points[index + 1]) return pre;
        const nextPoint = points[index + 1];
        const distance = Math.abs(
          curPoint.x - nextPoint.x + curPoint.y - nextPoint.y,
        );
        return pre + distance;
      }, 0);
      let halfEdgeLenght = edgeLenght / 2;

      const centerPoint = points.reduce((pre, curPoint, index) => {
        if (halfEdgeLenght < 0 || !points[index + 1]) return pre;
        const nextPoint = points[index + 1];
        const distanceTemp =
          curPoint.x - nextPoint.x + curPoint.y - nextPoint.y;
        const distance = Math.abs(distanceTemp);

        if (halfEdgeLenght > 0 && (halfEdgeLenght -= distance) <= 0) {
          if (curPoint.x === nextPoint.x)
            return {
              x: nextPoint.x,
              y: nextPoint.y + halfEdgeLenght * (distanceTemp > 0 ? -1 : 1),
            };
          else
            return {
              x: nextPoint.x + halfEdgeLenght * (distanceTemp > 0 ? -1 : 1),
              y: nextPoint.y,
            };
        }
        return pre;
      }, {});

      return centerPoint;
    },
    // 【自定义】
    getPath(points: IPoint[]) {
      const path = [];
      for (let i = 0; i < points.length; i++) {
        const point = points[i];
        if (i === 0) {
          path.push(['M', point.x, point.y]);
        } else if (i === points.length - 1) {
          path.push(['L', point.x, point.y]);
        } else {
          const prevPoint = points[i - 1];
          let nextPoint = points[i + 1];
          let cornerLen = 6; // radio
          if (
            Math.abs(point.y - prevPoint.y) > cornerLen ||
            Math.abs(point.x - prevPoint.x) > cornerLen
          ) {
            if (prevPoint.x === point.x) {
              path.push([
                'L',
                point.x,
                point.y > prevPoint.y
                  ? point.y - cornerLen
                  : point.y + cornerLen,
              ]);
            } else if (prevPoint.y === point.y) {
              path.push([
                'L',
                point.x > prevPoint.x
                  ? point.x - cornerLen
                  : point.x + cornerLen,
                point.y,
              ]);
            }
          }
          const yLen = Math.abs(point.y - nextPoint.y);
          const xLen = Math.abs(point.x - nextPoint.x);
          if (yLen > 0 && yLen < cornerLen) {
            cornerLen = yLen;
          } else if (xLen > 0 && xLen < cornerLen) {
            cornerLen = xLen;
          }
          if (prevPoint.x !== nextPoint.x && nextPoint.x === point.x) {
            path.push([
              'Q',
              point.x,
              point.y,
              point.x,
              point.y > nextPoint.y ? point.y - cornerLen : point.y + cornerLen,
            ]);
          } else if (prevPoint.y !== nextPoint.y && nextPoint.y === point.y) {
            path.push([
              'Q',
              point.x,
              point.y,
              point.x > nextPoint.x ? point.x - cornerLen : point.x + cornerLen,
              point.y,
            ]);
          }
        }
      }

      return path;
    },
    // 【覆盖-自定义】
    getControlPoints(cfg: IEdgeModel) {
      if (!cfg.sourceNode) {
        return cfg.controlPoints;
      }
      return this.polylineFinding(
        cfg.sourceNode,
        cfg.targetNode,
        cfg.startPoint,
        cfg.endPoint,
        15,
      );
    },
    // 【自定义】
    getExpandedBBox(bbox: IBBox, offset: number) {
      return 0 === bbox.width && 0 === bbox.height
        ? bbox
        : {
            centerX: bbox.centerX,
            centerY: bbox.centerY,
            minX: bbox.minX - offset,
            minY: bbox.minY - offset,
            maxX: bbox.maxX + offset,
            maxY: bbox.maxY + offset,
            height: bbox.height + 2 * offset,
            width: bbox.width + 2 * offset,
          };
    },
    // 【自定义】
    getExpandedPort(bbox: IBBox, point: IPoint, offset: number) {
      return point.x === bbox.minX || point.x === bbox.maxX
        ? // @ts-ignore
          {
            // @ts-ignore
            x: point.x > bbox.centerX ? bbox.maxX + offset : bbox.minX - offset,
            y: point.y,
          }
        : // @ts-ignore
          {
            x: point.x,
            // @ts-ignore
            y: point.y > bbox.centerY ? bbox.maxY + offset : bbox.minY - offset,
          };
    },
    // 【自定义】
    combineBBoxes(sBBox: IBBox, tBBox: IBBox) {
      const minX = Math.min(sBBox.minX, tBBox.minX),
        minY = Math.min(sBBox.minY, tBBox.minY),
        maxX = Math.max(sBBox.maxX, tBBox.maxX),
        maxY = Math.max(sBBox.maxY, tBBox.maxY);
      return {
        centerX: (minX + maxX) / 2,
        centerY: (minY + maxY) / 2,
        minX: minX,
        minY: minY,
        maxX: maxX,
        maxY: maxY,
        height: maxY - minY,
        width: maxX - minX,
      };
    },
    // 【自定义】
    getBBoxFromVertexes(sPoint: IPoint, tPoint: IPoint) {
      const minX = Math.min(sPoint.x, tPoint.x),
        maxX = Math.max(sPoint.x, tPoint.x),
        minY = Math.min(sPoint.y, tPoint.y),
        maxY = Math.max(sPoint.y, tPoint.y);
      return {
        centerX: (minX + maxX) / 2,
        centerY: (minY + maxY) / 2,
        maxX: maxX,
        maxY: maxY,
        minX: minX,
        minY: minY,
        height: maxY - minY,
        width: maxX - minX,
      };
    },
    // 【自定义】
    vertexOfBBox(bbox: IBBox) {
      return [
        { x: bbox.minX, y: bbox.minY },
        { x: bbox.maxX, y: bbox.minY },
        { x: bbox.maxX, y: bbox.maxY },
        { x: bbox.minX, y: bbox.maxY },
      ];
    },
    // 【自定义】
    crossPointsByLineAndBBox(bbox: IBBox, centerPoint: IPoint) {
      let crossPoints: IPoint[] = [];
      if (!(centerPoint.x < bbox.minX || centerPoint.x > bbox.maxX))
        crossPoints = crossPoints.concat([
          { x: centerPoint.x, y: bbox.minY },
          { x: centerPoint.x, y: bbox.maxY },
        ]);
      if (!(centerPoint.y < bbox.minY || centerPoint.y > bbox.maxY))
        crossPoints = crossPoints.concat([
          { x: bbox.minX, y: centerPoint.y },
          { x: bbox.maxX, y: centerPoint.y },
        ]);
      return crossPoints;
    },
    // 【自定义】
    getConnectablePoints(
      sBBox: IBBox,
      tBBox: IBBox,
      sPoint: IPoint,
      tPoint: IPoint,
    ) {
      const lineBBox = this.getBBoxFromVertexes(sPoint, tPoint);
      const outerBBox = this.combineBBoxes(sBBox, tBBox);
      const sLineBBox = this.combineBBoxes(sBBox, lineBBox);
      const tLineBBox = this.combineBBoxes(tBBox, lineBBox);
      let points: IPoint[] = [];
      points = points.concat(
        this.vertexOfBBox(sLineBBox),
        this.vertexOfBBox(tLineBBox),
        this.vertexOfBBox(outerBBox),
      );
      const centerPoint = { x: outerBBox.centerX, y: outerBBox.centerY };
      [outerBBox, sLineBBox, tLineBBox, lineBBox].forEach((bbox) => {
        points = points.concat(
          this.crossPointsByLineAndBBox(bbox, centerPoint),
        );
      });

      points.push({ x: sPoint.x, y: tPoint.y });
      points.push({ x: tPoint.x, y: sPoint.y });
      return points;
    },
    // 【自定义】
    filterConnectablePoints(points: IPoint[], bbox: IBBox) {
      return points.filter(
        (point) =>
          point.x <= bbox.minX ||
          point.x >= bbox.maxX ||
          point.y <= bbox.minY ||
          point.y >= bbox.maxY,
      );
    },
    // 【自定义】
    AStar(
      points: IPoint[],
      sPoint: IPoint,
      tPoint: IPoint,
      sBBox: IBBox,
      tBBox: IBBox,
    ) {
      const openList: IPoint[] = [sPoint];
      const closeList: IPoint[] = [];
      points = uniqBy(this.fillId(points), 'id');
      points.push(tPoint);
      let endPoint: IPoint | undefined = undefined;
      while (openList.length > 0) {
        let minCostPoint: IPoint | undefined;
        openList.forEach((p, i) => {
          if (!p.parent) p.f = 0;
          if (!minCostPoint) minCostPoint = p;
          if (Number(p.f) < Number(minCostPoint.f)) minCostPoint = p;
        });
        if (minCostPoint?.x === tPoint.x && minCostPoint.y === tPoint.y) {
          endPoint = minCostPoint;
          break;
        }
        openList.splice(
          openList.findIndex(
            (o) => o.x === minCostPoint?.x && o.y === minCostPoint.y,
          ),
          1,
        );
        closeList.push(minCostPoint!);
        const neighbor = points.filter(
          (p) =>
            (p.x === minCostPoint?.x || p.y === minCostPoint?.y) &&
            !(p.x === minCostPoint?.x && p.y === minCostPoint?.y) &&
            !this.crossBBox([sBBox, tBBox], minCostPoint, p),
        );
        neighbor.forEach((p: IPoint) => {
          const inOpen = openList.find((o) => o.x === p.x && o.y === p.y);
          const currentG = this.getCost(p, minCostPoint);
          if (closeList.find((o) => o.x === p.x && o.y === p.y)) {
          } else if (inOpen) {
            if (Number(p.g) > Number(currentG)) {
              // @ts-ignore
              p.parent = minCostPoint;
              p.g = currentG;
              p.f = Number(p.g) + Number(p.h);
            }
          } else {
            // @ts-ignore
            p.parent = minCostPoint;
            p.g = currentG;
            let h = this.getCost(p, tPoint);
            if (this.crossBBox([tBBox], p, tPoint)) {
              h += tBBox.width / 2 + tBBox.height / 2; //如果穿过bbox则增加该点的预估代价为bbox周长的一半
            }
            p.h = h;
            p.f = Number(p.g) + Number(p.h);
            openList.push(p);
          }
        });
      }
      if (endPoint) {
        const result = [];
        result.push({ x: endPoint.x, y: endPoint.y });
        while (endPoint?.parent) {
          // @ts-ignore
          endPoint = endPoint.parent;
          result.push({ x: endPoint?.x, y: endPoint?.y });
        }
        return result.reverse();
      }
      return [];
    },
    // 【自定义】
    crossBBox(bboxes: IBBox[], p1: IPoint, p2: IPoint) {
      for (let i = 0; i < bboxes.length; i++) {
        const bbox = bboxes[i];
        if (p1.x === p2.x && bbox.minX < p1.x && bbox.maxX > p1.x) {
          if (
            (p1.y < bbox.maxY && p2.y >= bbox.maxY) ||
            (p2.y < bbox.maxY && p1.y >= bbox.maxY)
          )
            return true;
        } else if (p1.y === p2.y && bbox.minY < p1.y && bbox.maxY > p1.y) {
          if (
            (p1.x < bbox.maxX && p2.x >= bbox.maxX) ||
            (p2.x < bbox.maxX && p1.x >= bbox.maxX)
          )
            return true;
        }
      }
      return false;
    },
    getCost(p1: IPoint, p2: IPoint) {
      return Math.abs(p1.x - p2.x) + Math.abs(p1.y - p2.y);
    },
    getPointBBox(t: { x: number; y: number; id: string }) {
      return {
        centerX: t.x,
        centerY: t.y,
        minX: t.x,
        minY: t.y,
        maxX: t.x,
        maxY: t.y,
        height: 0,
        width: 0,
      };
    },
    fillId(points: IPoint[]) {
      points.forEach((p) => {
        // @ts-ignore
        p.id = p.x + '-' + p.y;
      });
      return points;
    },
    polylineFinding(
      sNode: Item,
      tNode: Item,
      sPort: IPoint,
      tPort: IPoint,
      offset: number,
    ) {
      const sourceBBox =
        sNode && sNode.getBBox() ? sNode.getBBox() : this.getPointBBox(sPort);
      const targetBBox =
        tNode && tNode.getBBox() ? tNode.getBBox() : this.getPointBBox(tPort);
      const sBBox = this.getExpandedBBox(sourceBBox, offset);
      const tBBox = this.getExpandedBBox(targetBBox, offset);
      const sPoint = this.getExpandedPort(sourceBBox, sPort, offset);
      const tPoint = this.getExpandedPort(targetBBox, tPort, offset);
      let points = this.getConnectablePoints(sBBox, tBBox, sPoint, tPoint);
      points = this.filterConnectablePoints(points, sBBox);
      points = this.filterConnectablePoints(points, tBBox);
      const polylinePoints = this.AStar(points, sPoint, tPoint, sBBox, tBBox);
      return polylinePoints;
    },
  },
  'polyline',
);

// @ts-nocheck
import G6 from '@antv/g6';

G6.registerBehavior('edge-event', {
  getDefaultCfg() {
    return {
      multiple: true,
    };
  },
  getEvents() {
    return {
      'edge:mouseenter': 'onEdgeMouseenter',
      'edge:mouseleave': 'onEdgeMouseleave',
    };
  },

  /** 鼠标hover到线 */
  onEdgeMouseenter(e) {
    this.onEdgeShapeMouseenter(e);
  },

  /** 鼠标离开线 */
  onEdgeMouseleave(e) {
    this.onEdgeShapeMouseleave(e);
  },

  // 鼠标移入线shape || 文字Group
  onEdgeShapeMouseenter(e: any) {
    const { item } = e;
    item.setState('hover', true);
  },

  // 鼠标移出线shape || 文字Group
  onEdgeShapeMouseleave(e: any) {
    const { item } = e;
    item.setState('hover', false);
  },
});

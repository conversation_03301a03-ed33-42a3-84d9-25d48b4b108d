import { TBranchData } from '../interface.d';

// 节点字段的类型
export enum EnumFlowBlockFieldType {
  expression, // 表达式
  variable, // 变量
  number, // 数字
  customModal,
  show,
  branch,
}

export interface IFlowBlockAnchor {
  anchorIndex: number;
  type: string;
  name: string;
}

export interface IFlowBlockField {
  name: string;
  value: any;
  label: string;
  tips?: string;
  type: EnumFlowBlockFieldType;
  category?: string; // 简单处理 划分到类目下
  example?: string; // 示例
  params?: any;
}

export interface IFlowBlock {
  definition: {
    type: string;
    message: string;
    fields: IFlowBlockField[];
    args: IFlowBlockAnchor[];
  };
  /** 目前控制器暂时不生成代码，忽略 */
  codeGenerator?: () => void;
}

const blocks: IFlowBlock[] = [
  {
    definition: {
      type: 'sz-start',
      message: '开始',
      fields: [
        {
          name: 'var_name',
          value: 'globalConfig',
          label: '全局配置保存到',
          tips: '全局配置的数据将会保存到名为globalConfig的变量中。全局配置可以在整个流程中使用。',
          type: EnumFlowBlockFieldType.show,
        },
        {
          name: 'config',
          value: '[]',
          label: '编辑全局配置数据',
          tips: '通过本选项可以对全局配置的数据进行编辑，支持如表格，对话框，文本，附件等多种形式。编辑过的全局配置后，支持从机器人对全局配置进行重设。',
          type: EnumFlowBlockFieldType.customModal,
        },
      ],
      args: [
        {
          anchorIndex: 0, // 节点下标
          type: 'connection', // 节点类型
          name: 'A', // 连接点的名称， 纯连接属性的无特殊含义
        },
      ],
    },
  },
  {
    definition: {
      type: 'sz-end',
      message: '结束',
      fields: [],
      args: [
        {
          anchorIndex: 0,
          type: 'connection',
          name: 'A', // 连接点的名称， 纯连接属性的无特殊含义
        },
      ],
    },
  },
  {
    definition: {
      type: 'sz-task',
      message: '任务',
      fields: [
        {
          name: 'branches',
          value: [] as TBranchData[],
          label: '流程走向判断',
          tips: '流程走向判断，可以设定不同的判断条件来确定流程下一步执行的步骤。可以通过“添加流程分支”来设置判断条件。',
          type: EnumFlowBlockFieldType.branch,
        },
        {
          name: 'reTryCount', // 出错重试次数
          value: '',
          label: '任务执行错误后重试次数',
          type: EnumFlowBlockFieldType.number,
          params: {
            placeholder: '请输入重试次数',
          },
          tips: '如果任务执行出现错误',
          category: '高级选项',
        },
        {
          name: 'errorVar',
          value: '',
          label: '错误结果保存',
          type: EnumFlowBlockFieldType.variable,
          params: {
            placeholder: '请输入或选择变量名',
          },
          tips: '可以将任务执行错误的结果保存到指定变量中。',
          category: '高级选项',
        },
      ],
      args: [
        {
          anchorIndex: 0,
          type: 'connection',
          name: 'A',
        },
        {
          anchorIndex: 1,
          type: 'connection',
          name: 'B',
        },
        {
          anchorIndex: 2,
          type: 'connection',
          name: 'C',
        },
        {
          anchorIndex: 3,
          type: 'connection',
          name: 'D',
        },
        {
          anchorIndex: 4,
          type: 'connection',
          name: 'Error',
        },
      ],
    },
  },
  {
    definition: {
      type: 'sz-judge',
      message: '判断',
      fields: [
        {
          name: 'condition',
          value: '',
          label: '判断条件',
          type: EnumFlowBlockFieldType.expression,
        },
      ],
      args: [
        {
          anchorIndex: 0,
          type: 'connection',
          name: 'A',
        },
        {
          anchorIndex: 1,
          type: 'input_statement',
          name: 'ELSE',
        },
        {
          anchorIndex: 2,
          type: 'input_statement',
          name: 'IF',
        },
      ],
    },
  },
];

export default blocks;

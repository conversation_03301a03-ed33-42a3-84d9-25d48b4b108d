import { useImperativeHandle, useRef } from 'react';
import { User } from './components/user-select';

export interface Methods {
  getData: () => {
    userIds: string[];
    users: User[];
  };
}

export const useUserSelect = <T extends Methods>(userSelectRef?: React.MutableRefObject<T>, methods?: T) => {
  const internalRef = useRef<T>();

  useImperativeHandle(
    userSelectRef || internalRef,
    () =>
      ({
        ...methods,
      } as T),
    [methods],
  );

  return userSelectRef || (internalRef as React.MutableRefObject<T>);
};

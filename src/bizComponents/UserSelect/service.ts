import { IRes } from 'main/types';
import { request } from 'umi';

/** 获取部门成员列表 */
const getDepartmentMemberList = async (params?: { account?: string; name?: string; deptId?: string }) => {
  const res = await request<
    IRes<{
      userAndDept: {
        avatar?: string;
        /** 1 部门 2 用户 */
        type: 1 | 2;
        /** value 实际是名字 */
        value: string;
        key: string;
      }[];
    }>
  >(`/uc/user/1.0.0/user-dept-list`, {
    method: 'POST',
    data: {
      ...params,
    },
  });

  return {
    userAndDept: res.bizData.userAndDept.map((item) => ({
      ...item,
      label: item.value,
    })),
  };
};

/** 获取用户组成员列表 */
const getGroupMemberList = async (params?: any) => {
  return await request(`/opt/uc/1.0.0/group/group-user-list-all`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 获取用户组列表 */
const getUserGroupList = async () => {
  return await request(`/opt/group/1.0.0/user-group-list`, {
    method: 'POST',
    data: {},
  });
};
export { getUserGroupList, getDepartmentMemberList, getGroupMemberList };

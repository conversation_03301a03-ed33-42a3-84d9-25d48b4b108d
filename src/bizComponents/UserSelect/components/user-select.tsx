import { useControllableValue, useRequest } from 'ahooks';
import { <PERSON><PERSON><PERSON><PERSON>b, Button, Checkbox, Divider, List } from 'antd';
import { CheckboxProps } from 'antd/lib';
import { uniqWith } from 'lodash';
import React, { FC, useMemo, useRef, useState } from 'react';
import * as API from '../service';
import { Methods, useUserSelect } from '../use-user-select';
import Item from './item';
import Search from './search';
import { GetProps } from 'main/types';

export type UserOrDept = Awaited<ReturnType<typeof API.getDepartmentMemberList>>['userAndDept'][number];

const defaultBreadPath = [
  {
    title: '部门/用户',
    key: '',
  },
];

interface CommonProps<T extends string[] = string[]> {
  defaultCheckedList?: T;
  hideList?: T;
  value?: T;
  // disabledCheckedList?: T;
  userSelectRef?: React.MutableRefObject<Methods>;
}

// interface UserProps extends CommonProps {
//   deptCheckable?: false;
//   onChange?: (userIds: string[], users: UserOrDept[]) => void;
// }
//
// interface UserOrDeptProps extends CommonProps {
//   deptCheckable: true;
//   onChange?: (userOrDeptIds: string[], userOrDept: UserOrDept[]) => void;
// }
//
// export interface UserSelectType {
//   (props: UserProps): JSX.Element;
//   (props: UserOrDeptProps): JSX.Element;
// }

interface UserProps extends CommonProps {
  deptCheckable: false;
  onUserChange?: (userIds: string[], users: UserOrDept[]) => void;
}

interface UserOrDeptProps extends CommonProps {
  deptCheckable: true;
  onUserOrDeptChange?: (userOrDeptIds: string[], userOrDept: UserOrDept[]) => void;
}

export type Props = UserProps | UserOrDeptProps;

/**
 * TODO: 如果通过props传入的选中用户被系统删除了，这个组件该如何展示？
 */
// function UserSelect(props: UserProps): JSX.Element;
// function UserSelect(props: UserOrDeptProps): JSX.Element;
const UserSelect = (props: Props) => {
  const isFirst = useRef(true);

  const [checkedList, setCheckedList] = useControllableValue<string[]>(props, {
    defaultValue: [],
    defaultValuePropName: 'defaultCheckedList',
    trigger: props.deptCheckable ? 'onUserOrDeptChange' : 'onUserChange',
  });
  const [breadcrumbPath, setBreadcrumbPath] = useState<{ title: string; key?: string }[]>(defaultBreadPath);
  const [realList, setRealList] = useState<UserOrDept[]>([]);

  const {
    data: departsAndUsers,
    loading,
    run: loadData,
  } = useRequest(
    (searchparams?: { account?: string; name?: string }) =>
      API.getDepartmentMemberList({ ...searchparams, deptId: breadcrumbPath.at(-1)?.key || '' }),
    {
      refreshDeps: [breadcrumbPath],
      onSuccess(data) {
        if (isFirst.current) {
          isFirst.current = false;
          if (props.defaultCheckedList) {
            const defaultRealList = data.userAndDept.filter((item) => props?.defaultCheckedList?.includes(item.key));
            setRealList(defaultRealList);
          }
        }
      },
    },
  );

  const { listDataSource, userOrDeptList } = useMemo(() => {
    const data = departsAndUsers?.userAndDept.filter((item) => !props.hideList?.includes(item.key)) || [];
    const departs = data.filter((item) => item.type === 1);
    const users = data.filter((item) => item.type === 2);
    const listDataSource = [...departs, ...users];
    const userOrDeptList = props.deptCheckable ? listDataSource : users;
    return { departs, users, listDataSource, userOrDeptList };
  }, [departsAndUsers, props.deptCheckable, props.hideList]);

  const { checkAll, indeterminate } = useMemo(() => {
    let indeterminate = false;

    const checkAll = userOrDeptList.every((item) => {
      const isChecked = checkedList?.includes(item.key);
      if (isChecked && !indeterminate) indeterminate = true;
      return isChecked;
    });
    return {
      checkAll,
      indeterminate: indeterminate && !checkAll,
    };
  }, [checkedList, userOrDeptList]);

  const onCheckboxChange = (checked: boolean, item: UserOrDept) => {
    if (checked) {
      const newRealList = [...realList, item];
      setCheckedList((checkedList) => {
        return [...checkedList, item.key];
      }, newRealList);
      setRealList(newRealList);
    } else {
      const newRealList = realList.filter((realItem) => realItem.key !== item.key);
      setCheckedList((checkedList) => checkedList.filter((key) => key !== item.key), newRealList);
      setRealList(newRealList);
    }
  };
  const onCheckboxAllChange: CheckboxProps['onChange'] = (e) => {
    const isChecked = e.target.checked;

    if (isChecked) {
      const newRealList = uniqWith(realList.concat(userOrDeptList), (a, b) => a.key === b.key);

      setCheckedList(
        newRealList.map((item) => item.key),
        newRealList,
      );
      setRealList(newRealList);
    } else {
      const retainedData = realList?.reduce((acc, cur) => {
        const shouldOmit = userOrDeptList.find((item) => item.key === cur.key);
        if (!shouldOmit) acc.push(cur);
        return acc;
      }, [] as typeof realList);
      setCheckedList(
        retainedData.map((item) => item.key),
        retainedData,
      );
      setRealList(retainedData);
    }
  };

  const clearAll = () => {
    setCheckedList([], []);
    setRealList([]);
  };

  const renderItem: GetProps<typeof List<UserOrDept>>['renderItem'] = (item, index) => {
    const actions = [];

    const isDepartment = item.type === 1;
    if (isDepartment)
      actions.push(
        <Button
          key={item.key}
          disabled={checkedList.includes(item.key)}
          type="link"
          onClick={() =>
            setBreadcrumbPath((breadcrumbPath) => [...breadcrumbPath, { title: item.label, key: item.key }])
          }
          className="cursor-pointer">
          下级
        </Button>,
      );

    const showCheckbox = item.type === 2 || Boolean(props.deptCheckable);
    // const showCheckbox = true;
    return (
      <Item
        showCheckbox={showCheckbox}
        key={item.key}
        type={item.type}
        disabled={false}
        label={item.label}
        value={item.key}
        onChange={(e) => {
          const checked = e.target.checked;
          onCheckboxChange(checked, item);
        }}
        actions={actions}
      />
    );
  };

  useUserSelect(props.userSelectRef, {
    getData: () => {
      return {
        userIds: checkedList,
        users: realList,
      };
    },
  });

  return (
    <div className="flex h-full w-full">
      <div className="flex flex-1  w-full  flex-col space-y-4">
        <Search onChange={loadData} />
        <Breadcrumb className="px-1">
          {breadcrumbPath.map((item) => {
            return (
              <Breadcrumb.Item
                className="cursor-pointer text-slate-500"
                onClick={() => {
                  const idx = breadcrumbPath.findIndex((_item) => _item.title === item.title);
                  setBreadcrumbPath((breadcrumbPath) => breadcrumbPath.slice(0, idx + 1));
                }}
                key={item.key}>
                {item.title}
              </Breadcrumb.Item>
            );
          })}
        </Breadcrumb>

        <Checkbox className="px-1" indeterminate={indeterminate} checked={checkAll} onChange={onCheckboxAllChange}>
          全选
        </Checkbox>
        <div className="flex-grow  overflow-y-auto">
          {/* <Spin spinning={loading}> */}
          <Checkbox.Group value={checkedList} className="w-full ">
            <List
              className="w-full  [&_.ant-list-item]:!py-0  [&_.ant-list-items]:space-y-2"
              itemLayout="horizontal"
              dataSource={listDataSource}
              split={false}
              loading={loading}
              renderItem={renderItem}
            />
          </Checkbox.Group>
          {/* </Spin> */}
        </div>
      </div>
      <Divider type="vertical" className="h-full" />
      <div className="flex-1 overflow-y-auto px-2">
        <div className="flex items-center justify-between text-slate-500 ">
          <span>已选员工：{checkedList?.length || 0}人</span>
          <Button type="link" onClick={clearAll}>
            清空
          </Button>
        </div>
        <List
          itemLayout="horizontal"
          dataSource={realList}
          split={false}
          locale={{ emptyText: ' ' }}
          className="[&_.ant-list-item]:!py-0 [&_.ant-list-items]:space-y-2"
          renderItem={(item, index) => {
            return (
              <Item
                showCheckbox={false}
                actions={[
                  <span
                    key={item.key}
                    className="relative right-3 inline-block w-5 cursor-pointer rounded-sm bg-white text-slate-500"
                    onClick={() => onCheckboxChange(false, item)}>
                    ×
                  </span>,
                ]}
                key={item.key}
                type={item.type}
                disabled={false}
                label={item.label}
                value={item.key}
                onChange={(e) => {
                  const checked = e.target.checked;
                  onCheckboxChange(checked, item);
                }}
              />
            );
          }}
        />
      </div>
    </div>
  );
};

export default UserSelect;

import { Input, Select, Space } from 'antd'
import React, { useState } from 'react'
const options = [
  {
    value: 'account',
    label: '账户',
    placeholder: '请输入账户',
  },
  {
    value: 'name',
    label: '用户名',
    placeholder: '请输入账户',
  },
]
const Search: React.FC<{ onChange: (val: { account?: string; name?: string }) => void }> = (props) => {
  const [searchMode, setSearchMode] = useState<'name' | 'account'>('name')
  const [searchParams, setSearchParams] = useState<{ account?: string; name?: string }>({})

  return (
    <Space.Compact>
      <Select
        className="min-w-[100px]"
        onSelect={(val) => {
          setSearchMode(val as typeof searchMode)
          props.onChange({ name: '', account: '' })
        }}
        defaultValue={'name'}
        options={options}
      />
      <Input.Search
        allowClear
        placeholder={options.find((item) => item.value === searchMode)?.placeholder}
        value={searchParams[searchMode]}
        onChange={(e) => setSearchParams({ [searchMode]: e.target.value })}
        onSearch={(val) => {
          setSearchParams({ [searchMode]: val })
          props.onChange({ [searchMode]: val })
        }}
      />
    </Space.Compact>
  )
}

export default Search

import { Modal } from 'antd';
import React, { ComponentProps } from 'react';
import { useUserSelect } from './../use-user-select';
import UserSelect, { UserOrDept, Props as UserSelectProps } from './user-select';
// TODO： 重载在这里失效了
// type OriginUserSelectProps = Omit<UserSelectProps, 'userSelectRef'>;

const UserSelectModal: React.FC<
  UserSelectProps & {
    open: boolean;
    title?: string;
    onOk: (userIds: string[], users: UserOrDept[]) => void;
    onCancel?: () => void;
    userSelectRef?: never;
  }
> = ({ title = '选择用户', open, onOk, onCancel, ...props }) => {
  const userSelectRef = useUserSelect();

  return (
    <Modal
      width={664}
      open={open}
      title={title}
      onOk={() => {
        const { userIds, users } = userSelectRef.current.getData();
        onOk?.(userIds, users);
      }}
      onCancel={onCancel}>
      <div className="h-[452px]">
        <UserSelect {...props} userSelectRef={userSelectRef} />
      </div>
    </Modal>
  );
};

export default UserSelectModal;

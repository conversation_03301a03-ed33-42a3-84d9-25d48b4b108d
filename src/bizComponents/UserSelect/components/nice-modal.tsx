import React, { ComponentProps } from 'react';
import UserSelect from './user-select';
import { Modal } from 'antd';
import NiceModal, { useModal } from '@ebay/nice-modal-react';

const UserSelectNiceModal: React.FC<ComponentProps<typeof UserSelect> & { title?: string; onOk: () => void }> = ({
  title = '选择用户',
  onOk,
  ...props
}) => {
  const modal = useModal();

  return (
    <Modal
      width={664}
      open={modal.visible}
      onCancel={() => {
        modal.hide();
        setTimeout(modal.remove, 400);
      }}
      title={title}
      okText="确定"
      cancelText="取消"
      onOk={() => {
        onOk();
        modal.hide();
        setTimeout(modal.remove, 400);
      }}>
      <UserSelect {...props} />
    </Modal>
  );
};

export default NiceModal.create(UserSelectNiceModal);

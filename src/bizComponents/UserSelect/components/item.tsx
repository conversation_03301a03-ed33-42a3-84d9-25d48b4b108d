import { Avatar, Checkbox, List } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useMemo } from 'react';
// @ts-ignore
import { IconFont } from 'main/MyIcon';
import Ternary from '@deopCmd/components/Ternary';

export const colorMap = {
  blue: 'linear-gradient(156deg, #0085FF 0%, #8BC7FF 100%)',
  orange: 'linear-gradient(156deg, #FF8400 -7%, #FFD18B 84%)',
  purple: 'linear-gradient(156deg, #D000FF -7%, #F49EFC 84%)',
  pink: 'linear-gradient(156deg, #FF5D5D -7%, #FF9C9C 82%)',
  yellow: 'linear-gradient(156deg, #FF7B00 -7%, #FFD18B 83%)',
  green: 'linear-gradient(156deg, #26C751 -7%, #89CE9C 83%)',
  cyan: 'linear-gradient(156deg, #23D4CF -7%, #7ADBCA 82%)',
};

export function getRandomColor(name: string) {
  const colorKeys = Object.keys(colorMap);
  const hash = Array.from(name).reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const index = hash % colorKeys.length;
  return colorMap[colorKeys[index] as keyof typeof colorMap];
}

const Item: React.FC<{
  showCheckbox: boolean;
  value: string;
  disabled: boolean;
  /**  1 是部门 2 是用户*/
  type: 1 | 2;
  label: string;
  onChange: (e: CheckboxChangeEvent) => void;
  actions?: React.ReactNode[];
}> = (props) => {
  const color = useMemo(() => getRandomColor(props.value), [props.value]);

  const avatar = useMemo(() => {
    return (
      <>
        <Avatar style={{ backgroundImage: color, border: 'none' }}>
          <Ternary
            falseElement={<IconFont type="iconzuzhijiagou" className="text-[16px]" />}
            condition={props.type === 2}>
            {props.label.slice(0, 1)}
          </Ternary>
        </Avatar>
        <span className="flex-1">{props.label}</span>
      </>
    );
  }, [props.type, props.label]);

  if (props.showCheckbox) {
    return (
      <List.Item
        style={{ contentVisibility: 'auto' }}
        className="w-full rounded-md !px-1 hover:bg-[#F0F0F0] [&_.ant-checkbox-wrapper]:w-full [&_.ant-checkbox-wrapper]:items-center [&_.ant-checkbox]:top-[1px] [&_.ant-list-item-action]:ms-0"
        actions={props.actions || []}>
        <Checkbox value={props.value} onChange={props.onChange}>
          <div className="flex w-full items-center justify-between gap-2">{avatar}</div>
        </Checkbox>
      </List.Item>
    );
  }

  return (
    <List.Item
      className="rounded-md hover:bg-[#F0F0F0]"
      style={{ contentVisibility: 'auto' }}
      actions={props.actions || []}>
      <div className="flex items-center gap-2">{avatar}</div>
    </List.Item>
  );
};

export default Item;

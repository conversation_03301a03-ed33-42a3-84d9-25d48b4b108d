import UserSelectModal from './components/modal';
import UserSelectNiceModal from './components/nice-modal';
import OriginUserSelect from './components/user-select';
type CompoundedComponent = typeof OriginUserSelect & {
  Modal: typeof UserSelectModal;
  NiceModal: typeof UserSelectNiceModal;
};

const UserSelect = OriginUserSelect as CompoundedComponent;

UserSelect.Modal = UserSelectModal;
UserSelect.NiceModal = UserSelectNiceModal;

export default UserSelect;

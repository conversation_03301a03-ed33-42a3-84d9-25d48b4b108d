import { useMemo, useRef } from 'react';
import type { SetStateAction } from 'react';
import { useMemoizedFn, useUpdate } from 'ahooks';
import { isFunction } from 'lodash';

export interface Options<T> {
  defaultValue?: T;
}

export type Props = Record<string, any>;

export interface StandardProps<T> {
  value: T;
  defaultValue?: T;
  onChange: (val: T) => void;
}

function useControllableValue<T = any>(props: StandardProps<T>): [T, (v: SetStateAction<T>) => void];
function useControllableValue<T = any>(
  props?: Props,
  options?: Options<T>,
): [T, (v: SetStateAction<T>, ...args: any[]) => void];
function useControllableValue<T = any>(props: Props = {}, options: Options<T> = {}) {
  const { defaultValue } = options;

  const value = props?.value as T;
  const isControlled = Object.prototype.hasOwnProperty.call(props, 'value');

  const initialValue = useMemo(() => {
    if (isControlled) {
      return value;
    }

    return defaultValue;
  }, []);

  const stateRef = useRef(initialValue);

  if (isControlled) {
    stateRef.current = value;
  }

  const update = useUpdate();

  function setState(v: SetStateAction<T>, ...args: any[]) {
    const r = isFunction(v) ? v(stateRef.current) : v;

    if (!isControlled) {
      stateRef.current = r;
      update();
    }
    props.onChange?.(r, ...args);
  }

  return [stateRef.current, useMemoizedFn(setState)] as const;
}

export default useControllableValue;

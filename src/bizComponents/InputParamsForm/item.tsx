// @ts-nocheck
import { useMemo } from 'react';
import { Input, Checkbox, Radio, Form, Table, Switch, InputNumber, Tooltip, Space } from 'antd';
import { EFormItemType, EDirection, EChecked, EFormItemTypeName } from './const';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import SheetWrapper from './DataTable/SheetWrapper';
import TableOpt from './DataTable/TableOpt';
import { SzDatePicker as SZDatePicker, SzSelect as SZSelect } from 'main/SzComponents';
import { InfoCircleOutlined } from '@ant-design/icons';
import { SM4Encrypt } from '@deopCmd/utils/crypto/sm4';

// 引入国际化设置（中文）
import locale from 'antd/es/date-picker/locale/zh_CN';

import style from './style/item.less';
import CloudAttachment from '@deopCmd/pages/flowArrange/components/Flow/components/MyInputParams/cloud-attachment';

// 必填项取值
const RequiredValue = '1';
// 必填项报错信息
const RequiredIsErrorStr = '不能为空或者至少选择一项';
const FormItem = Form.Item;
const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px',
};

// 获取时间展示值
const getTimeValue = (config: any, attrMap: any, format: any) => {
  if (!config || !attrMap) return null;
  if (!config?.hasValue) {
    if (attrMap.value) {
      return moment(attrMap.value, format);
    }
  }
  if (config?.value) {
    return moment(config?.value, format);
  }

  return null;
};

// 处理必填配置信息
const GetRequiredConfig = (attrMap: any, config: any, noMessage?: boolean) => {
  if (attrMap.isRequired === RequiredValue) {
    if (noMessage) {
      return {
        rules: [{ required: true }],
      };
    }
    const { pageConfig } = config;
    let errMsg = `请输入${attrMap.label}`;
    if (pageConfig?.errorType === 'title') {
      // 标题+错误信息
      errMsg = `${attrMap.label}${pageConfig?.errorMsg}`;
    } else if (pageConfig?.errorType === 'type') {
      // 类型+错误信息
      errMsg = `${EFormItemTypeName[attrMap.type]}${pageConfig?.errorMsg}`;
    }
    return {
      rules: [{ required: true, message: errMsg || RequiredIsErrorStr }],
    };
  }
  return {};
};

/**
 * 引用参数模版控件
 * 规则：
 * 1. 如果inputParams中存在控件属性，则直接使用inputParams中的值，忽略template
 * 2. 如果inputParams中不存在控件属性，则以template中的默认值
 */
const itemMap = {
  [EFormItemType.page]: (attrMap, onChange, config) => (
    <FormItem
      className={style.page}
      label={
        <span className="text-[18px]">
          {attrMap?.title}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }></FormItem>
  ),
  [EFormItemType.input]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <Input
        id={config.formItemId}
        className={style.input}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(e) => onChange(e.target.value, attrMap.name)}
        placeholder={attrMap.tips}
        disabled={config?.disabled}
      />
    </FormItem>
  ),
  [EFormItemType.inputNumber]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <InputNumber
        id={config.formItemId}
        className={style.input}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(value) => onChange(value, attrMap.name)}
        placeholder={attrMap.tips}
        disabled={config?.disabled}
        max={attrMap.max ? Number(attrMap.max) : 1000}
        min={attrMap.min ? Number(attrMap.min) : -1000}
        precision={attrMap.precision ? Number(attrMap.precision) : 0}
        style={{ width: '100%' }}
      />
    </FormItem>
  ),
  [EFormItemType.password]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <Input.Password
        id={config.formItemId}
        className={style.input}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(e) => {
          onChange(SM4Encrypt(e.target.value), attrMap.name);
        }}
        onFocus={(e) => {
          e.target.value = '';
          onChange('', attrMap.name, EFormItemType.password);
        }}
        placeholder={attrMap.tips}
        disabled={config?.disabled}
        visibilityToggle={false}
      />
    </FormItem>
  ),
  [EFormItemType.textarea]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <Input.TextArea
        id={config.formItemId}
        value={config?.hasValue ? config?.value : attrMap.value}
        style={{ height: `${attrMap.height}px` }}
        onChange={(e) => onChange(e.target.value, attrMap.name)}
        placeholder={attrMap.tips}
        disabled={config?.disabled}
      />
    </FormItem>
  ),
  [EFormItemType.text]: ({ value, fontFamily, fontSize, fontWeight }) => (
    <div className={style.text} style={{ fontFamily, fontSize, fontWeight }}>
      {value}
    </div>
  ),
  [EFormItemType.select]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <SZSelect
        id={config.formItemId}
        className={style.selectInput}
        value={config?.hasValue ? config?.value : attrMap.value}
        options={attrMap.options.map((label) => ({ label, value: label }))}
        onChange={(value) => onChange(value, attrMap.name)}
        popupClassName={style.dropdownClassName}
        disabled={config?.disabled}
        getPopupContainer={() => document.getElementById(config.formId)}
      />
    </FormItem>
  ),
  [EFormItemType.multiSelect]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? (Array.isArray(config?.value) ? config?.value : []) : attrMap.valueArray}
      {...GetRequiredConfig(attrMap, config)}>
      <SZSelect
        id={config.formItemId}
        mode="multiple"
        className={style.selectInput}
        value={config?.hasValue ? (Array.isArray(config?.value) ? config?.value : []) : attrMap.valueArray}
        options={attrMap.options.map((label) => ({ label, value: label }))}
        onChange={(value) => onChange(value, attrMap.name)}
        popupClassName={style.dropdownClassName}
        disabled={config?.disabled}
        getPopupContainer={() => document.getElementById(config.formId)}
      />
    </FormItem>
  ),
  [EFormItemType.selectFile]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <Input
        id={config.formItemId}
        className={style.input}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(e) => onChange(e.target.value, attrMap.name)}
        placeholder={attrMap.tips}
        disabled={config?.disabled}
      />
    </FormItem>
  ),
  [EFormItemType.selectFile]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config)}>
      <Input
        id={config.formItemId}
        className={style.input}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(e) => onChange(e.target.value, attrMap.name)}
        placeholder={attrMap.tips}
        disabled={config?.disabled}
      />
    </FormItem>
  ),
  [EFormItemType.radio]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config, true)}>
      <Radio.Group
        id={config.formItemId}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(e) => onChange(e.target.value, attrMap.name)}
        radioStyle={attrMap.direction === EDirection.lengthways ? radioStyle : {}}>
        <Space
          direction={
            attrMap.direction
              ? typeof attrMap.direction === 'number'
                ? attrMap.direction === 1
                  ? 'horizontal'
                  : 'vertical'
                : attrMap.direction
              : 'horizontal'
          }>
          {attrMap.options.map((item) => (
            <Radio key={item} value={item} disabled={config?.disabled}>
              {item}
            </Radio>
          ))}
        </Space>
      </Radio.Group>
    </FormItem>
  ),
  [EFormItemType.checkbox]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? (Array.isArray(config?.value) ? config?.value : []) : attrMap.valueArray}
      {...GetRequiredConfig(attrMap, config)}>
      <Checkbox.Group
        id={config.formItemId}
        value={config?.hasValue ? (Array.isArray(config?.value) ? config?.value : []) : attrMap.valueArray}
        // options={attrMap.options.map((label) => ({ label, value: label }))}
        onChange={(value) => onChange(value, attrMap.name)}
        disabled={config?.disabled}>
        <Space
          direction={
            attrMap.direction
              ? typeof attrMap.direction === 'number'
                ? attrMap.direction === 1
                  ? 'horizontal'
                  : 'vertical'
                : attrMap.direction
              : 'horizontal'
          }>
          {attrMap.options.map((item) => (
            <Checkbox key={item} value={item} disabled={config?.disabled}>
              {item}
            </Checkbox>
          ))}
        </Space>
      </Checkbox.Group>
    </FormItem>
  ),
  [EFormItemType.datePicker]: (attrMap, onChange, config) => {
    const format = useMemo(() => attrMap.dateTimeFormat.replace('yyyy', 'YYYY'), [attrMap.dateTimeFormat]);
    return (
      <FormItem
        className={style.default}
        label={
          <span>
            {attrMap?.label}
            {Boolean(attrMap?.showTip) && (
              <Tooltip title={attrMap?.tip} placement="top">
                <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
              </Tooltip>
            )}
          </span>
        }
        name={attrMap.name}
        initialValue={getTimeValue(config, attrMap, format)}
        {...GetRequiredConfig(attrMap, config)}>
        <SZDatePicker
          id={config.formItemId}
          locale={locale}
          className={style.datePicker}
          value={getTimeValue(config, attrMap, format)}
          showTime={{ format }}
          format={format}
          onChange={(_, dateString) => {
            // console.log(_, dateString);
            onChange(dateString, attrMap.name);
          }}
          popupClassName={style.dropdownClassName}
          disabled={config?.disabled}
          getPopupContainer={() => document.getElementById(config.formId)}
        />
      </FormItem>
    );
  },
  [EFormItemType.switch]: (attrMap, onChange, config) => (
    <FormItem
      className={style.default}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }
      name={attrMap.name}
      initialValue={config?.hasValue ? config?.value : attrMap.value}
      {...GetRequiredConfig(attrMap, config, true)}>
      <Switch
        unCheckedChildren="禁用"
        checkedChildren="启用"
        id={config.formItemId}
        key={attrMap.name}
        className={style.switch}
        value={config?.hasValue ? config?.value : attrMap.value}
        onChange={(checked) => {
          onChange(
            checked ? EChecked.checked : EChecked.unchecked,
            attrMap.name,
            attrMap.type,
            attrMap.items || undefined,
          );
        }}
        checked={config?.hasValue ? config?.value === EChecked.checked : attrMap.valueNumber === EChecked.checked}
        disabled={config?.disabled}
      />
    </FormItem>
  ),
  [EFormItemType.line]: (attrMap) => (
    <div className={style.lineWrap}>
      <div className={style.label} style={{ fontWeight: attrMap.fontWeight || 'normal' }}>
        {attrMap.value || attrMap.label}
      </div>
      <div
        className={style.line}
        style={{
          borderTopStyle: 'solid',
          borderTopWidth: `${attrMap.height || attrMap.lineWeight}px`,
        }}></div>
    </div>
  ),
  [EFormItemType.table]: (attrMap, onChange, config) => {
    // console.log('sdfsdfs', {
    //   attrMap,
    //   config,
    // });

    // eslint-disable-next-line no-underscore-dangle
    const _onChange = (value, index, title) => {
      const tableValue = cloneDeep(config?.hasValue ? config?.value : attrMap.valueTable);

      tableValue[index][title] = value;
      onChange(tableValue, attrMap.name);
    };
    const lengthX = useMemo(() => {
      if (config?.value) {
        if (Object.keys(config?.value[0]).length < 5) {
          return 340;
        }
        return 100 * Object.keys(config?.value[0]).length;
      }
      if (attrMap.valueTable) {
        if (Object.keys(attrMap.valueTable[0]).length < 5) {
          return 340;
        }
        return 100 * Object.keys(attrMap.valueTable[0]).length;
      }
      return 340;
    }, [config?.value, attrMap.valueTable]);
    return (
      <FormItem
        className={style.default}
        label={
          <span>
            {attrMap?.label}
            {Boolean(attrMap?.showTip) && (
              <Tooltip title={attrMap?.tip} placement="top">
                <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
              </Tooltip>
            )}
          </span>
        }
        name={attrMap.name}>
        <Table
          id={config.formItemId}
          className={style.table}
          pagination={false}
          scroll={{ x: lengthX }}
          bordered
          size="small"
          columns={(attrMap?.column || []).map((title, index) => ({
            dataIndex: index,
            title,
            render: (_, record, idx) => (
              <Input
                key={idx}
                className={style.tableInput}
                onChange={(e) => _onChange(e.target.value, idx, title)}
                value={config?.value?.[idx]?.[title] ?? record[title]}
                disabled={config?.disabled}
              />
            ),
          }))}
          dataSource={config?.hasValue ? config?.value : attrMap.valueTable}
        />
      </FormItem>
    );
  },
  [EFormItemType.dataTableWithSheet]: (attrMap, onChange, config, showInSide) => (
    <FormItem
      className={style.dataTableWithSheet}
      label={
        <span>
          {attrMap?.label}
          {Boolean(attrMap?.showTip) && (
            <Tooltip title={attrMap?.tip} placement="top">
              <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
            </Tooltip>
          )}
        </span>
      }>
      <TableOpt onChange={onChange} config={config} attrMap={cloneDeep(attrMap)} />
      <FormItem name={attrMap.name}>
        <SheetWrapper showInSide={showInSide} onChange={onChange} attrMap={cloneDeep(attrMap)} config={config} />
      </FormItem>
    </FormItem>
  ),

  // [EFormItemType.Annex]: (attrMap, onChange, config) => {

  //   return (
  //     <FormItem
  //       className={style.default}
  //       label={
  //         <span>
  //           {attrMap?.label}
  //           {Boolean(attrMap?.showTip) && (
  //             <Tooltip title={attrMap?.tip} placement="top">
  //               <InfoCircleOutlined
  //                 style={{ fontSize: '12px', marginLeft: '8px' }}
  //               />
  //             </Tooltip>
  //           )}
  //         </span>
  //       }
  //       name={attrMap.name}
  //       initialValue={config?.hasValue ? config?.value : attrMap.value}
  //       {...GetRequiredConfig(attrMap, config)}
  //     >
  //       {config.hasValue &&
  //         config.value?.map((item) => {
  //           return <Input value={item} key={item} disabled />;
  //         })}
  //     </FormItem>
  //   );
  // },
  [EFormItemType.Accessory]: (attrMap, onChange, config) => {
    // console.log(attrMap, config);

    return (
      <FormItem
        className={style.default}
        label={
          <span>
            {attrMap?.label}
            {Boolean(attrMap?.showTip) && (
              <Tooltip title={attrMap?.tip} placement="top">
                <InfoCircleOutlined style={{ fontSize: '12px', marginLeft: '8px' }} />
              </Tooltip>
            )}
          </span>
        }
        initialValue={config?.hasValue ? config?.value : attrMap.value}
        {...GetRequiredConfig(attrMap, config)}>
        {attrMap?.value?.map(
          (
            item: {
              fileType: number;
              fileValue: { label: string; value: string };
            },
            idx,
          ) => {
            return <Input value={item.fileValue.label} key={idx} disabled />;
          },
        )}
      </FormItem>
    );
  },
  [EFormItemType.AnnexUpgrade]: (attrMap, onChange, config) => {
    return (
      <FormItem
        className={style.default}
        label={<span>{attrMap?.label}</span>}
        initialValue={config?.hasValue ? config?.value : attrMap?.valueArrayUpgrade}
        {...GetRequiredConfig(attrMap, config)}>
        <CloudAttachment
          value={config?.value || attrMap?.valueArrayUpgrade}
          onChange={(files) => {
            onChange(files, attrMap.name, attrMap.type, attrMap.items || undefined);
          }}
        />
      </FormItem>
    );
  },
};

const Item = (props) => {
  const {
    item,
    onChange = Function,
    className = '',
    config = {
      disabled: false,
      value: undefined,
    },
  } = props;
  const { type } = item as TItem;
  // console.log(item, config, type);
  return (
    <>{itemMap[type] && <div className={`${style.item} ${className}`}>{itemMap[type](item, onChange, config)}</div>}</>
  );
};

export default Item;

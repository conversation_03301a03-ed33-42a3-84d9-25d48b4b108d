// 来自父组件的引用参数信息
export interface JsonFormInfo {
  /** 该版本流程包是否是来源于新的版本，新版本则需要对密码进行加密，默认：false */
  isNewVersion?: boolean;
  /** 该版本流程包是否有引用参数，默认false */
  isReference?: boolean;
  /** 该版本流程包引用参数模板 */
  template?: Record<string, unknown>[] | null;
  /** 该版本流程包引用参数的值 */
  inputParam?: Record<string, unknown> | null;
}

export type TItem = {
  /** 表单项类型 */
  type: string;
  /** 表单项id/name */
  name: string;
  /** 表单项label */
  label?: string;
};

export type TState = {
  inputParam: Record<string, TItem>;
};

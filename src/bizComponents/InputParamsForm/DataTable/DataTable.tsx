/** @format */
import { useEffect, useMemo, useRef, useState } from 'react';
import { CellHeader, CellColOrder, Cell } from './CellHeader';
import { MultiGrid } from 'react-virtualized';
import { GridCellProps } from 'react-virtualized/dist/es/Grid';
import { Tooltip } from 'antd';
import { IconFont } from 'main/MyIcon';

import sty from './style.less';

const getLens = (curNums: any, minNums: any, maxNums: any) => {
  if (curNums < minNums) return minNums;
  if (curNums > maxNums) return maxNums;
  return curNums;
};

const mergeData = (
  list: any[][] = [[]],
  minRows: number = 3,
  minCols: number = 5,
  maxCols: number = 100,
) => {
  const copyList = list.map((item) => [...item]);
  const curRows = copyList.length || 0;
  const curCols = copyList.reduce((prevValue, currentValue) => {
    if (prevValue < currentValue.length || 0) return currentValue.length;
    return prevValue;
  }, 0);

  let rowLen = curRows < minRows ? minRows : curRows;
  let colLen = getLens(curCols, minCols, maxCols);

  let resList = [];
  for (let i = 0; i < rowLen; i++) {
    let rowArr = [],
      item = copyList[i] || [];
    const currentColLen = copyList[i]?.length || 0;
    if (currentColLen > colLen) {
      item.splice(maxCols);
    } else {
      for (let j = currentColLen; j < colLen; j++) {
        rowArr.push('');
      }
      item = [...item, ...rowArr];
    }
    resList.push(item);
  }
  return resList;
};

const FIXED_CELL_BG = '#F6F9FB';
const maxCols = 100;

const STYLE = {
  border: '1px solid #ddd',
};
const STYLE_TOP_LEFT_GRID: React.CSSProperties = {
  backgroundColor: FIXED_CELL_BG,
};
const STYLE_BOTTOM_LEFT_GRID = {
  backgroundColor: FIXED_CELL_BG,
  borderLeft: 'none',
  fontFamily: 'SourceHanSansSCVF-Regular, SourceHanSansSCVF',
};
const STYLE_TOP_RIGHT_GRID: React.CSSProperties = {
  backgroundColor: FIXED_CELL_BG,
  fontFamily: 'SourceHanSansSCVF-Regular, SourceHanSansSCVF',
};

const cellSizeMap = {
  side: {
    width: 32,
    height: 36,
    firstColWidth: 32,
    firstRowHeight: 34,
  },
  view: {
    width: 67,
    height: 36,
    firstColWidth: 32,
    firstRowHeight: 34,
  },
};

export interface DataTableProps {
  value: any[][];
  showInSide: boolean; // 显示在side中，还是显示在view中
  isSerialFirstCol?: boolean;
  fixedColumnCount?: number;
  minRows?: number;
  minCols?: number;
  height?: number;
  width?: number;
  disabled: boolean;
  onChange: (data: any[][]) => void;
}

const DataTable = (props: DataTableProps) => {
  const {
    height = 200,
    width = 380,
    showInSide = false,
    minRows = 4,
    minCols = 6,
    value = [[]],
    disabled = false,
  } = props;
  const SpreadsheetRef = useRef<HTMLDivElement | null>(null);
  const [data, setData] = useState<any[][]>([[]]);
  const gridRef = useRef(null as any);
  const [cellSize, cheight, cwidth] = useMemo(() => {
    let csize = showInSide ? cellSizeMap['side'] : cellSizeMap['view'];
    let cwidth = showInSide ? 194 : width;
    return [csize, height, cwidth];
  }, [showInSide]);
  const columnCount = useMemo(() => data[0].length + 1, [data]);
  const rowCount = useMemo(() => data.length + 1, [data]);

  useEffect(() => {
    setData(mergeData(value));
  }, [value]);

  const forceUpdate = () =>
    gridRef.current && gridRef?.current.forceUpdateGrids();

  const onChange = (data: any[][]) => {
    const copyData = data.map((item) => [...item]);
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    typeof props.onChange === 'function' && props.onChange(copyData);
  };

  useEffect(() => {
    forceUpdate();
  }, [data]);

  const handleChangeCell =
    (columnIndex: number, rowIndex: number) => (v: any) => {
      if (columnIndex < 0 || rowIndex < 0) return;
      if (data[rowIndex][columnIndex] === v) return;
      setData((prevState) => {
        prevState[rowIndex][columnIndex] = v;
        return [...prevState];
      });
      onChange(data);
    };

  const addRow = () => {
    if (!data.length || disabled) return;
    setData((prevState) => {
      prevState.push(Array(prevState[0].length).fill(''));
      return [...prevState];
    });
    onChange(data);
  };

  const addCol = () => {
    if (!data.length || columnCount > maxCols || disabled) return;
    setData((prevState) => {
      prevState.forEach((item) => {
        item.push('');
      });
      return [...prevState];
    });
    onChange(data);
  };

  const deleteCol = (columnIndex: number) => () => {
    if (columnIndex < 0) return;
    setData((prevState) => {
      prevState.forEach((item) => {
        item.splice(columnIndex, 1);
      });
      return [...prevState];
    });
    onChange(data);
  };

  const deleteRow = (rowIndex: number) => () => {
    if (rowIndex < 0) return;
    setData((prevState) => {
      prevState.splice(rowIndex, 1);
      return [...prevState];
    });
    onChange(data);
  };

  const cellRenderer = ({
    columnIndex,
    key,
    rowIndex,
    style,
  }: GridCellProps) => {
    if (!rowIndex) {
      return <CellHeader columnIndex={columnIndex} style={style} key={key} />;
    }
    if (!columnIndex) {
      return <CellColOrder rowIndex={rowIndex} style={style} key={key} />;
    }
    return (
      <Cell
        key={key}
        readOnly={columnIndex === 0}
        rowIndex={rowIndex}
        columnIndex={columnIndex}
        canDelRow={rowCount > minRows}
        canDelCol={columnCount > minCols}
        value={columnIndex ? data[rowIndex - 1][columnIndex - 1] : rowIndex}
        style={style}
        onChange={handleChangeCell(columnIndex - 1, rowIndex - 1)}
        onDelCol={deleteCol(columnIndex - 1)}
        onDelRow={deleteRow(rowIndex - 1)}
        disabled={disabled}
      />
    );
  };

  return (
    <div className={sty.content}>
      <div className={sty.spreadsheet} ref={SpreadsheetRef}>
        {/* @ts-ignore */}
        <MultiGrid
          className="data-table"
          classNameBottomLeftGrid="data-table-bottomleft"
          classNameTopLeftGrid="data-table-topleft"
          classNameBottomRightGrid="data-table-bottomright"
          classNameTopRightGrid="data-table-topright"
          enableFixedColumnScroll
          enableFixedRowScroll
          fixedColumnCount={1}
          fixedRowCount={1}
          scrollToColumn={0}
          scrollToRow={0}
          ref={gridRef}
          cellRenderer={cellRenderer}
          columnWidth={(param) =>
            param.index ? cellSize.width : cellSize.firstColWidth
          }
          rowHeight={(param) =>
            param.index ? cellSize.height : cellSize.firstRowHeight
          }
          columnCount={columnCount}
          rowCount={rowCount}
          width={(SpreadsheetRef.current?.offsetWidth || cwidth) - 25}
          height={cheight}
          style={STYLE}
          styleBottomLeftGrid={STYLE_BOTTOM_LEFT_GRID}
          styleTopLeftGrid={STYLE_TOP_LEFT_GRID}
          styleTopRightGrid={STYLE_TOP_RIGHT_GRID}
          hideTopRightGridScrollbar
          hideBottomLeftGridScrollbar
        />
        <div className={sty.add}>
          <Tooltip title={columnCount > maxCols ? '列数已超过100' : ''}>
            {/* <Icon
              name="iconxinjian"
              size={14}
              onClick={addCol}
              className={`${columnCount > maxCols ? sty.disabled : ''}`}
            /> */}
            <IconFont
              type="iconjia"
              size={14}
              onClick={addCol}
              className={`${
                columnCount > maxCols || disabled ? sty.disabled : ''
              }`}
            />
          </Tooltip>
        </div>
      </div>
      {/* <Icon
        name="iconxinjian"
        size={14}
        onClick={addRow}
        className={showInSide ? sty.addRowIconInSide : sty.addRowIcon}
      /> */}
      <IconFont
        type="iconjia"
        size={14}
        onClick={addRow}
        className={disabled ? sty.addRowDisabled : sty.addRowIcon}
      />
    </div>
  );
};

export default DataTable;

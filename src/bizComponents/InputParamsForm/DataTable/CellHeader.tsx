import React, { useMemo, useState } from 'react';
import { Input, Tooltip, Dropdown, Menu } from 'antd';

import style from './style.less';

export interface CellHeaderProps {
  columnIndex: number;
  style: React.CSSProperties;
}

const numToHeaderString = (numm: number) => {
  const stringArray: any[] = [];
  function numToString(numm: any) {
    stringArray.length = 0;
    const numToStringAction = function (nnum: any) {
      const num = nnum - 1;
      const a = parseInt(String(num / 26));
      const b = num % 26;
      stringArray.push(String.fromCharCode(64 + parseInt(String(b + 1))));
      if (a > 0) {
        numToStringAction(a);
      }
    };
    numToStringAction(numm);
    return stringArray.reverse().join('');
  }

  return numToString(numm);
};

export const CellHeader = (props: any) => {
  const { columnIndex } = props;
  return (
    <div
      className={style.cell}
      style={props.style}
      key={columnIndex}
      tabIndex={110}
    >
      <span className={style['cell-text']} style={{ paddingRight: 16 }}>
        {columnIndex ? numToHeaderString(columnIndex) : ''}
      </span>
    </div>
  );
};

export const CellColOrder = (props: any) => {
  const { rowIndex } = props;
  return (
    <div
      className={style.cell}
      style={props.style}
      key={rowIndex}
      tabIndex={110}
    >
      <Tooltip title={rowIndex} placement="top">
        <span className={style['cell-text']}>
          <span className={style['cell-text-colIndex']}>{rowIndex}</span>
        </span>
      </Tooltip>
    </div>
  );
};

export const Cell = (props: any) => {
  const {
    value,
    onChange,
    canDelCol,
    canDelRow,
    readOnly = false,
    onDelRow,
    onDelCol,
    style: cellStyle,
    disabled,
    columnIndex,
    rowIndex,
  } = props;
  const [isFocus, setFocus] = useState(false);
  const [isEdit, setEdit] = useState(false);

  const saveNewContent = (e: any) => {
    if (typeof onChange === 'function') {
      onChange(e.target?.value);
    }
    setEdit(false);
  };

  const contextItems = [
    {
      label: '删除行',
      clearLabel: '清除行',
      key: 'delRow',
      callback: onDelRow,
    },
    {
      label: '删除列',
      clearLabel: '清除列',
      key: 'delCol',
      callback: onDelCol,
    },
  ];

  const handleMenuItem = (item: any) => {
    const index = contextItems.findIndex(
      (contextItem) => contextItem.key === item.key,
    );
    if (index !== -1) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      typeof contextItems?.[index]?.callback === 'function' &&
        contextItems?.[index].callback();
    }
  };

  const menu = useMemo(() => {
    let obj = { delCol: canDelCol, delRow: canDelRow };
    return (
      <Menu onClick={handleMenuItem}>
        {contextItems.map((item) => (
          <Menu.Item key={item.key}>
            {/* @ts-ignore */}
            {obj[item.key] ? item.label : item.clearLabel}
          </Menu.Item>
        ))}
      </Menu>
    );
  }, [onDelCol, onDelRow]);

  return !readOnly ? (
    <Dropdown
      key={`${rowIndex}-${columnIndex}`}
      menu={menu}
      trigger={['contextMenu']}
    >
      <div
        tabIndex={110}
        className={style.cell}
        style={cellStyle}
        onClick={() => setFocus(true)}
        onBlur={() => setFocus(false)}
        onDoubleClick={() => setEdit(true)}
        onContextMenu={() => setFocus(true)}
        onKeyDown={() => setEdit(true)}
      >
        {isEdit && !disabled ? (
          <Input
            autoFocus
            className={style['cell-textArea']}
            defaultValue={value}
            onBlur={saveNewContent}
          />
        ) : (
          <Tooltip title={value} placement="top">
            <span className={style['cell-text']}>{value}</span>
          </Tooltip>
        )}
        {isFocus && <div className={style.active} />}
      </div>
    </Dropdown>
  ) : (
    <div
      key={`${rowIndex}-${columnIndex}`}
      className={`${style.cell} ${style.orderCell}`}
      style={cellStyle}
    >
      <span className={style['cell-text']}>{value}</span>
    </div>
  );
};

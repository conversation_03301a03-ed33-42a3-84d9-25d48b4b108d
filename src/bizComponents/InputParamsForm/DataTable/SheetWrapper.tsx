import { useCallback, useMemo, useState } from 'react';
import DataTable from './DataTable';
import { cloneDeep } from 'lodash';

import style from './style.less';

const SheetWrapper = (props: any) => {
  const { showInSide, onChange, attrMap, config } = props;

  const [selectTable, setSelectTable] = useState(0);
  const { value, hasValue } = config;
  const [tableValue, tableData] = useMemo(() => {
    if (hasValue) {
      const tableData = cloneDeep(value);
      return [tableData[selectTable]?.data ?? [], tableData];
    } else {
      const tableData = cloneDeep(attrMap.value);
      return [tableData[selectTable]?.data ?? [], tableData];
    }
  }, [selectTable, attrMap, value]);

  const _onChange = useCallback(
    (value: any) => {
      const data = cloneDeep(tableData);
      if (data.length === 1) {
        setSelectTable(0);
        data[0].data = value;
        onChange(data, attrMap.name);
      } else {
        data[selectTable].data = value;
        onChange(data, attrMap.name);
      }
    },
    [onChange, selectTable, tableData],
  );

  return (
    <div className={style.sheetWrapper}>
      <div className={style.sheets}>
        {tableData.map(({ sheetName }: any, index: number) => {
          return (
            <div
              key={`${sheetName}-${index}`}
              className={`${style.sheetName} ${
                selectTable === index && style.active
              }`}
              onClick={() => setSelectTable(index)}
              title={sheetName}
            >
              {sheetName}
            </div>
          );
        })}
      </div>
      <DataTable
        showInSide={showInSide}
        onChange={_onChange}
        value={tableValue}
        disabled={config.disabled}
      />
    </div>
  );
};

export default SheetWrapper;

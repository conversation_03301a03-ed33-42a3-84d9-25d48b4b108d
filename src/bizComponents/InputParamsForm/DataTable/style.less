@import '~@/assets/styles/variable.less';

@maxWidth: 200px;

.excelTable {
  height: 100%;
  padding: 0 16px;
}

.excelTableWrap {
  height: calc(100% - 36px);
}

.dialogWorkSheet {
  display: flex;
  align-items: center;
}

.dialogWorkSheetSelect {
  flex: 1;
  margin: 0 8px;
  overflow: hidden;
}

.content {
  width: 100%;
  background: #f1f4f7;
  border: 1px solid #d9dde6;
  border-top: none;

  .spreadsheet {
    display: flex;
    width: 100%;
    max-height: 500px;
    margin-bottom: 2px;
    background-color: #fff;
    cursor: default;

    .datatable {
      width: 100%;
    }

    & > div:first-child {
      height: auto !important;
      max-height: 300px;
      border-left: 0 !important;
      width: 100% !important;

      & > div:first-child {
        width: 100% !important;

        & + div {
          width: 100% !important;
        }
      }
    }

    :global {
      /* stylelint-disable-next-line selector-class-pattern */
      .ReactVirtualized__Grid {
        outline: none;
      }

      /* stylelint-disable-next-line selector-class-pattern */
      .TopRightGrid_ScrollWrapper .ReactVirtualized__Grid {
        overflow-x: hidden !important;
      }
    }

    div.add {
      width: 25px;
      padding: 9px 5px;
      background: #f1f4f7;
      border-top: 1px solid #d9dde6;

      .disabled {
        /* stylelint-disable-next-line color-function-notation */
        color: rgba(0, 0, 0, 25%);
        cursor: not-allowed;
      }
    }
  }

  :global(.iconfont) {
    color: @text-color;
  }

  :global(.iconfont):hover {
    color: var(--primary-color);
  }

  .addRowIcon {
    margin-left: 9px;
  }

  .addRowDisabled {
    margin-left: 9px;
    /* stylelint-disable-next-line color-function-notation */
    color: rgba(0, 0, 0, 25%);
    cursor: not-allowed;
  }

  .addRowIconInSide {
    margin-left: 5px;
  }
}

.cell {
  position: relative;
  display: flex;
  align-items: center;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
  outline: none;
  user-select: none;
  //outline: var(--primary-color);
  .active {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    border: 1px solid var(--primary-color);
  }

  &-textArea {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2021;
    width: 100%;
    height: auto;
    //max-width: 400px !important;
    color: var(--text-color) !important;
    background-color: #fff;
    border-color: var(--primary-color) !important;
    border-radius: 0 !important;
  }

  &-editBtn {
    position: absolute;
    top: 50%;
    right: 4px;
    display: none;
    transform: translateY(-50%);

    &:hover {
      color: var(--primary-color);
    }
  }

  &:hover {
    /* stylelint-disable-next-line selector-class-pattern */
    .cell-editBtn {
      display: inline-block;
    }
  }

  &-text {
    display: inline-block;
    width: 100%;
    padding: 0 2px;
    overflow: hidden;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-colIndex {
      display: inline-block;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
    }

    &-value {
      color: var(--text-second-color);
    }
  }
}

.sheetWrapper {
  width: 100%;
  height: 100%;
  overflow-x: hidden;

  ::-webkit-scrollbar-track {
    background: #f8f8f8;
  }

  .sheets {
    position: relative;
    z-index: 1001;
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 4px;
    overflow-x: auto;
    overflow-y: hidden;

    .sheetName {
      min-width: 92px;
      max-width: 92px;
      margin-right: 2px;
      padding: 2px 16px;
      overflow: hidden;
      color: #848a90;
      white-space: nowrap;
      text-overflow: ellipsis;
      background: #f4f7f8;
      border-radius: 2px 2px 0 0;
      cursor: pointer;

      &.active {
        color: #505962;
        background: #f6f9fb;
        border: 1px solid #d9dde6;
        border-bottom: none;
      }
    }
  }
}

.tableOptWrapper {
  position: absolute;
  top: -28px;
  right: 0;
  display: flex;
  align-items: center;

  .opt {
    margin-left: 12px;
    color: #505962;
    font-size: 12px;
    cursor: pointer;

    &:hover {
      color: #2f9aff;

      span {
        color: #2f9aff;
      }
    }

    span {
      margin-right: 4px;
      color: #848a90;
      font-size: 13px;
      cursor: pointer;
    }
  }

  .optDisabled {
    /* stylelint-disable-next-line color-function-notation */
    color: rgba(0, 0, 0, 25%);
    cursor: not-allowed;

    span {
      /* stylelint-disable-next-line color-function-notation */
      color: rgba(0, 0, 0, 25%);
    }
  }

  .optDisabled:nth-child(3) {
    color: @text-color;
    cursor: pointer;

    span {
      color: @detail-entry-icon-color;
    }
  }
}

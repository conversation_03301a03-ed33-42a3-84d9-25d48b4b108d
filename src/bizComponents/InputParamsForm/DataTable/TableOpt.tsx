import { useRef } from 'react';
import { IconFont } from 'main/MyIcon';
import { Form, message, Select, Modal } from 'antd';
import XLSX from 'xlsx';
import { cloneDeep } from 'lodash';
import { SzSelect } from 'main/SzComponents';

import style from './style.less';

const MAX_FILE_SIZE = 200 * 1024;

const readData = (workbook: any, onChange: any) => {
  const { SheetNames, Sheets } = workbook;

  let sheetName = [SheetNames[0]];
  Modal.confirm({
    title: '导入表格',
    content: (
      <Form.Item label="选择需要导入的sheet页">
        <SzSelect
          mode="multiple"
          defaultValue={sheetName}
          maxTagCount={2}
          maxTagTextLength={5}
          onChange={(value: string[]) => {
            sheetName = value;
          }}
          style={{ width: '100%' }}
        >
          {SheetNames.map((name: string) => (
            <Select.Option value={name} key={name}>
              {name}
            </Select.Option>
          ))}
        </SzSelect>
      </Form.Item>
    ),
    onOk: () => {
      if (!sheetName.length) {
        message.error('请选择sheet页');
        return Promise.reject('');
      }
      const data = sheetName.map((item: string) => {
        const csv = XLSX.utils.sheet_to_json(Sheets[item], {
          header: 1,
          defval: '',
        });
        return {
          sheetName: item,
          data: csv,
        };
      });
      onChange(data);
    },
  });
};

const OptList = [
  {
    icon: 'icondaoru',
    name: '导入',
    type: 'import',
  },
  {
    icon: 'icondaochu',
    name: '导出',
    type: 'export',
  },
  {
    icon: 'iconshanchu',
    name: '清空',
    type: 'clear',
  },
];

const TableOpt = ({ onChange, config, attrMap }: any) => {
  const changeValue = (value: any) => {
    onChange(value, attrMap.name);
  };
  const SheetRef = useRef(null as any);

  // 模拟上传文件
  const selectFile = () => {
    SheetRef.current.click();
  };

  // 读取本地excel文件
  const readWorkbookFromLocalFile = (file: any, callback: any) => {
    const reader = new FileReader();
    reader.onload = function (e: any) {
      const data = e.target.result;
      const workbook = XLSX.read(data, { type: 'binary' });
      if (callback) callback(workbook);
    };
    reader.readAsBinaryString(file);
  };

  const importExcel = (e: any) => {
    const files = e.target.files;
    if (!files?.length) return;
    const f = files[0];
    e.target.value = '';
    if (!/\.xlsx?$/g.test(f.name)) {
      message.error('仅支持读取xlsx格式！');
      return;
    }

    if (f.size > MAX_FILE_SIZE) {
      message.error('文件大小不能超过200KB');
      return;
    }

    readWorkbookFromLocalFile(f, function (workbook: any) {
      readData(workbook, changeValue);
    });
  };

  // 导出Excel
  const exportExcel = async (data: any) => {
    const hasEmptySheet = data
      .map((item: any) => item.data)
      .some((val: any) => val.length === 0);
    if (hasEmptySheet) {
      message.error('不支持导出存在空sheet的表格');
      return;
    }
    const workbook = XLSX.utils.book_new();
    data.forEach(({ sheetName, data }: any) => {
      const workSheet = XLSX.utils.json_to_sheet(data, {
        skipHeader: true,
      });
      XLSX.utils.book_append_sheet(workbook, workSheet, sheetName);
    });

    XLSX.writeFile(workbook, '导出.xlsx', { compression: true, bookSST: true });
  };

  const clear = () => {
    const newArr = config?.value.map((item: any) => {
      return {
        sheetName: item.sheetName,
        data: [],
      };
    });
    changeValue(newArr);
  };

  const clickFunction = (type: string, disabled: boolean) => {
    switch (type) {
      case 'import':
        if (disabled) return;
        selectFile();
        break;
      case 'export':
        exportExcel(cloneDeep(config?.value));
        break;
      case 'clear':
        if (disabled) return;
        clear();
        // changeValue([{ sheetName: 'sheet', data: [] }]);
        break;
      default:
        break;
    }
  };

  return (
    <div className={style.tableOptWrapper}>
      <input
        type="file"
        id={attrMap.name}
        style={{ display: 'none' }}
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        ref={SheetRef}
        onChange={(e) => {
          importExcel(e);
        }}
      />
      {OptList.map(({ icon, name, type }) => {
        return (
          <div
            className={
              config.disabled ? `${style.opt} ${style.optDisabled}` : style.opt
            }
            onClick={() => clickFunction(type, config.disabled)}
            key={type}
          >
            <IconFont type={icon} />
            {name}
          </div>
        );
      })}
    </div>
  );
};

export default TableOpt;

/* stylelint-disable selector-class-pattern */
@item-input-height: 36px;

.item {
  margin-bottom: 2px;
  // padding: 0 16px;
  overflow-x: hidden;
  border-radius: 4px;

  &.active {
    background: rgb(55 156 255 / 12%);
  }

  & .deleteBtn {
    position: absolute;
    top: 9px;
    right: 17px;
    color: #999da8;

    &:hover {
      color: var(--error-font-color);
    }
  }

  :global {

    .ant-form-item-has-error .ant-checkbox-group,
    .ant-form-item-has-error.uploadInput .ant-form-item-control-input {
      border: 1px solid#FF493D;
    }
  }
}

.label {
  font-size: 16px;
  line-height: 18px;
}

.default {
  .label {
    margin-bottom: 4px;
  }

  .input {
    height: @item-input-height;
  }

  :global {

    .ant-form-item-control-input,
    .ant-select-selector {
      min-height: @item-input-height;
    }

    .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
      line-height: @item-input-height - 2px;
    }
  }
}

.dataTableWithSheet {
  :global {
    .ant-form-item-label {
      padding-right: 150px;
    }
  }
}

.textarea {
  padding: 9px 10px;
}

.text {
  padding: 16px 0;
}

.selectInput {
  width: 100%;

  :global(.ant-select-selection--single.ant-select-selection--single) {
    height: @item-input-height;
  }

  :global(.ant-select-selection__rendered.ant-select-selection__rendered) {
    margin-left: 11px;
  }

  :global(.ant-select-arrow.ant-select-arrow) {
    right: 11px;
  }

  :global(.ant-select-selection--multiple .ant-select-selection__rendered > ul > li) {
    height: 22px;
    margin-top: 6px;
    line-height: 22px;
  }

  /* stylelint-disable-next-line selector-class-pattern */
  :global(.ant-select-selection--multiple.ant-select-selection--multiple) {
    min-height: @item-input-height;
  }
}

.selectFile {
  display: flex;

  .selectFileBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: @item-input-height;
    margin-left: 4px;
    background-color: #fff;
    border: 1px solid #d0d5e0;
    border-radius: 4px;
    cursor: pointer;
    opacity: 1;

    &:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }
}

.switchWrap {
  .label {
    margin-bottom: 2px;
  }
}

.lineWrap {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-top: 12px;

  .label {
    margin-right: 8px;
  }

  .line {
    flex: 1;
    border-color: #d0d5e0;
  }
}

.table {
  :global(.ant-table-thead > tr > th) {
    height: 44px;
    padding: 13px 12px;
    background-color: #f5faff;
  }

  :global(.ant-table-tbody > tr > td) {
    height: 44px;
    padding: 0;
  }

  .tableInput {
    height: 100%;
    padding: 0 13px;
    border: none;
    box-shadow: none;
  }
}

.datePicker {
  width: 100%;
  height: @item-input-height;
}

.lengthwaysLayout {
  display: flex !important;
  flex-direction: column;

  &>* {
    height: 30px;
    line-height: 30px !important;
  }
}

/* stylelint-disable-next-line selector-class-pattern */
.webkitScroll-style {
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: rgb(0 0 0 / 20%);
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgb(0 0 0 /20%);
    }
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 0;
  }

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
}

.dropdownClassName {
  /* stylelint-disable-next-line CssSyntaxError */
  .webkitScroll-style();

  max-height: calc(100vh - 20px);
  overflow-y: scroll;

  &:global(.ant-picker-dropdown) {
    box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%),
      0 9px 28px 8px rgb(0 0 0 / 5%);
  }

  :global {
    .ant-select-dropdown-menu {
      max-height: 10000px;
    }
  }
}
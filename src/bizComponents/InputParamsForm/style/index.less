.container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  border-radius: 3px;

  &.had-padding {
    padding: 24px 8px 16px;
  }
}

.labelleft {
  :global {

    .ant-form-item-label,
    .ant-form-item-control {
      text-align: left;
    }
  }
}

.labelcenter {
  :global {

    .ant-form-item-label,
    .ant-form-item-control {
      text-align: center;
    }
  }
}

.labelright {
  :global {

    .ant-form-item-label,
    .ant-form-item-control {
      text-align: right;
    }
  }
}

.modalTitle {
  display: flex;
  flex: 0 0 40px;
  align-items: center;
  width: 468px;
  padding: 0 12px;
  background: #f7f8f9;
  border-radius: 3px;
  opacity: 1;

  .title {
    flex: 1;
    color: #2e2e2e;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
  }

  .placeHolder {
    width: 14px;
    height: 14px;
    background-color: #ebeef0;
    border-radius: 2px;
  }
}

.modalBody {
  position: relative;
  flex: 1;
  height: 0;

  .placeHolder {
    position: absolute;
    top: 8px;
    left: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(100% - 38px);
    height: 74px;
    color: var(--text-third-color);
    font-size: 12px;
    border: 1px dashed var(--border-color);
    border-radius: 5px;
  }
}

.list {
  height: 100%;
  padding: 8px 16px;
  overflow-y: scroll;

  & :global(.gu-transit) {
    opacity: 0.4;
  }
}

.modalFooter {
  display: flex;
  flex: 0 0 62px;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid rgb(0 0 0 / 6%);

  .placeHolder {
    width: 72px;
    height: 30px;
    margin-right: 16px;
    background-color: #ebeef0;
    border-radius: 4px;
  }
}
/** Form表单item项属性值 */
export type TAttr = {
  name: string;
  label: string;
  type: EAttrType;
  displayParams?: any;
  value: any; // name === 'value' 此类型必须为string
};

/** Form表单item项信息 */
export type TFormItem = {
  type: EFormItemType;
  icon: string;
  label: string;
  attrs: TAttr[];
};

/** Form表单item项类型映射 */
export enum EFormItemType {
  input = 'TextBox',
  password = 'Password',
  textarea = 'TextArea',
  text = 'Label',
  select = 'DropdownList',
  multiSelect = 'MultiSelect',
  selectFile = 'File',
  radio = 'Radio',
  checkbox = 'CheckBox',
  datePicker = 'Date',
  switch = 'Switch',
  line = 'Line',
  table = 'Table',
  dataTableWithSheet = 'DataTableWithSheet',
  inputNumber = 'InputNumber',
  page = 'Page',
  Annex = 'Annex',
  /** 低代码附件 */
  Accessory = 'Accessory',
  /** 云附件 */
  AnnexUpgrade = 'AnnexUpgrade',
}

/** Form表单item项类型映射 */
export const EFormItemTypeName = {
  TextBox: '输入框',
  Password: '密码输入框',
  TextArea: '多行文本域',
  DropdownList: '下拉列表',
  MultiSelect: '多选下拉框',
  File: '文件',
  Radio: '单选框',
  CheckBox: '复选框',
  Date: '日期时间选择',
  Switch: '开关',
  DataTableWithSheet: '数据表',
  InputNumber: '数值输入框',
};

/** Form表单item项属性类型映射 */
export enum EAttrType {
  input = 'input',
  password = 'password',
  number = 'number',
  select = 'select',
  textarea = 'textarea',
  multiInput = 'multiInput',
  multiSelect = 'multiSelect',
  selectFile = 'selectFile',
  datePicker = 'datePicker',
  dependent = 'dependent',
  commander = 'commander',
  sole = 'sole',
  table = 'table',
  dataTableWithSheet = 'DataTableWithSheet',
}

/**
 * Form表单item项-选择文件
 * 文件类型映射：文件/文件夹
 */
export enum EFileOrFolder {
  file = 'openFile',
  folder = 'openDirectory',
}

/**
 * Form表单item项-单选框(Radio)
 * 排列方向映射：横向/纵向
 */
export enum EDirection {
  crosswise = 1,
  lengthways,
}

/**
 * Form表单item项-开关(Switch)
 * 开关状态映射：开/关
 */
export enum EChecked {
  checked = 1,
  unchecked = 0,
}

/**
 * Form表单item项-分割线(Line)
 * 线条类型映射：solid
 */
export enum ELineType {
  solid = 'solid',
}

/**
 * Form表单item项-日期选择器(DatePicker)
 * 日期类型
 */
export const formatList = [
  'yyyy-MM-DD',
  'yyyy/MM/DD',
  'yyyy年MM月DD日',
  'HH:mm',
  'HH:mm:ss',
  'yyyy-MM-DD HH:mm',
  'yyyy-MM-DD HH:mm:ss',
  'yyyy/MM/DD HH:mm',
  'yyyy/MM/DD HH:mm:ss',
  'yyyy年MM月DD日 HH:mm',
  'yyyy年MM月DD日 HH:mm:ss',
];

/** 定义Form表单item项UI */
export const formItemMap: Record<string, TFormItem> = {
  [EFormItemType.input]: {
    type: EFormItemType.input,
    icon: 'icon-wenbenshuruzujian',
    label: '输入框',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'input',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '输入框',
      },
      {
        name: 'tips',
        label: '空白提示',
        type: EAttrType.input,
        value: '请输入文本内容',
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.input,
        value: '',
      },
    ],
  },
  [EFormItemType.password]: {
    type: EFormItemType.password,
    icon: 'icon-mima1',
    label: '密码框',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'password',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '密码框',
      },
      {
        name: 'tips',
        label: '空白提示',
        type: EAttrType.input,
        value: '请输入文本内容',
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.password,
        value: '',
      },
    ],
  },
  [EFormItemType.textarea]: {
    type: EFormItemType.textarea,
    icon: 'icon-wenbenyu',
    label: '文本域',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'textarea',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '文本域',
      },
      {
        name: 'tips',
        label: '空白提示',
        type: EAttrType.input,
        value: '请输入文本内容',
      },
      {
        name: 'height',
        label: '文本域显示高度',
        type: EAttrType.number,
        displayParams: {
          min: 70,
          max: 500,
        },
        value: 70,
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.textarea,
        value: '',
      },
    ],
  },
  [EFormItemType.text]: {
    type: EFormItemType.text,
    icon: 'icon-jingtaiwenben',
    label: '静态文本',
    attrs: [
      {
        name: 'fontFamily',
        label: '字体选择',
        type: EAttrType.select,
        displayParams: [
          {
            label: '仿宋',
            value: '仿宋',
          },
          {
            label: '黑体',
            value: '黑体',
          },
          {
            label: '楷体',
            value: '楷体',
          },
          {
            label: '隶书',
            value: '隶书',
          },
          {
            label: '宋体',
            value: '宋体',
          },
          {
            label: '微软雅黑',
            value: '微软雅黑',
          },
          {
            label: '新宋体',
            value: '新宋体',
          },
          {
            label: '幼圆',
            value: '幼圆',
          },
          {
            label: 'Arial',
            value: 'Arial',
          },
          {
            label: 'Microsoft JhengHei',
            value: 'Microsoft JhengHei',
          },
        ],
        value: '仿宋',
      },
      {
        name: 'fontSize',
        label: '字体大小',
        type: EAttrType.number,
        displayParams: {
          min: 9,
          max: 72,
        },
        value: 16,
      },
      {
        name: 'fontWeight',
        label: '字体属性',
        type: EAttrType.select,
        displayParams: [
          {
            label: '常规体',
            value: 'normal',
          },
          {
            label: '细体',
            value: 'lighter',
          },
          {
            label: '粗体',
            value: 'bold',
          },
        ],
        value: 'normal',
      },
      {
        name: 'value',
        label: '文本描述',
        type: EAttrType.input,
        value: '静态文本描述',
      },
    ],
  },
  [EFormItemType.select]: {
    type: EFormItemType.select,
    icon: 'icon-xialazujian',
    label: '下拉列表',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'select',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '下拉列表',
      },
      {
        name: 'options',
        label: '选项',
        type: EAttrType.multiInput,
        value: ['选项1', '选项2'],
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.dependent,
        displayParams: {
          target: 'options',
          type: EAttrType.select,
          displayParams: '$0',
        },
        value: '选项1',
      },
    ],
  },
  [EFormItemType.multiSelect]: {
    type: EFormItemType.multiSelect,
    icon: 'icon-xialaduoxuan',
    label: '多选下拉框',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'multiSelect',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '多选下拉框',
      },
      {
        name: 'options',
        label: '选项',
        type: EAttrType.multiInput,
        value: ['选项1', '选项2'],
      },
      {
        name: 'valueArray',
        label: '默认值',
        type: EAttrType.dependent,
        displayParams: {
          target: 'options',
          type: EAttrType.multiSelect,
          displayParams: '$0',
        },
        value: ['选项1'],
      },
    ],
  },
  [EFormItemType.selectFile]: {
    type: EFormItemType.selectFile,
    icon: 'icon-duihuakuangwenjianxuanze',
    label: '选择文件',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'selectFile',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '选择文件',
      },
      {
        name: 'tips',
        label: '空白提示',
        type: EAttrType.input,
        value: '请选择文件',
      },
      {
        name: 'fileOrFolder',
        label: '文件模式',
        type: EAttrType.select,
        displayParams: [
          {
            label: '打开文件',
            value: EFileOrFolder.file,
          },
          {
            label: '打开文件夹',
            value: EFileOrFolder.folder,
          },
        ],
        value: EFileOrFolder.file,
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.dependent,
        displayParams: {
          type: EAttrType.selectFile,
          target: 'fileOrFolder',
          displayParams: {
            properties: ['$0'],
          },
        },
        value: '',
      },
    ],
  },
  [EFormItemType.radio]: {
    type: EFormItemType.radio,
    icon: 'icon-danxuanzujian',
    label: '单选框',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'radio',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '单选框',
      },
      {
        name: 'options',
        label: '选项',
        type: EAttrType.multiInput,
        value: ['选项1', '选项2'],
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.dependent,
        displayParams: {
          target: 'options',
          type: EAttrType.select,
          displayParams: '$0',
        },
        value: '选项1',
      },
      {
        name: 'direction',
        label: '方向',
        type: EAttrType.select,
        displayParams: [
          {
            label: '横向',
            value: EDirection.crosswise,
          },
          {
            label: '纵向',
            value: EDirection.lengthways,
          },
        ],
        value: EDirection.crosswise,
      },
    ],
  },
  [EFormItemType.checkbox]: {
    type: EFormItemType.checkbox,
    icon: 'icon-duoxuanzujian',
    label: '复选框',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'checkbox',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '复选框',
      },
      {
        name: 'options',
        label: '选项',
        type: EAttrType.multiInput,
        value: ['选项1', '选项2'],
      },
      {
        name: 'valueArray',
        label: '默认值',
        type: EAttrType.dependent,
        displayParams: {
          target: 'options',
          type: EAttrType.multiSelect,
          displayParams: '$0',
        },
        value: ['选项1'],
      },
      {
        name: 'direction',
        label: '方向',
        type: EAttrType.select,
        displayParams: [
          {
            label: '横向',
            value: EDirection.crosswise,
          },
          {
            label: '纵向',
            value: EDirection.lengthways,
          },
        ],
        value: EDirection.crosswise,
      },
    ],
  },
  [EFormItemType.datePicker]: {
    type: EFormItemType.datePicker,
    icon: 'icon-riqi',
    label: '日期选择',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'datePicker',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '日期选择',
      },
      {
        name: 'dateTimeFormat',
        label: '日期类型',
        type: EAttrType.select,
        displayParams: formatList.map((format) => ({
          label: format,
          value: format,
        })),
        value: formatList[0],
      },
      {
        name: 'value',
        label: '默认值',
        type: EAttrType.dependent,
        displayParams: {
          target: 'dateTimeFormat',
          type: EAttrType.datePicker,
          displayParams: '$0',
        },
        value: '',
      },
    ],
  },
  [EFormItemType.switch]: {
    type: EFormItemType.switch,
    icon: 'icon-kaiguan',
    label: '开关',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'switch',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '开关',
      },
      {
        name: 'valueNumber',
        label: '默认值',
        type: EAttrType.select,
        displayParams: [
          {
            label: '启用',
            value: EChecked.checked,
          },
          {
            label: '禁用',
            value: EChecked.unchecked,
          },
        ],
        value: EChecked.checked,
      },
    ],
  },
  [EFormItemType.line]: {
    type: EFormItemType.line,
    icon: 'icon-fengexian',
    label: '分割线',
    attrs: [
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '基本属性',
      },
      {
        name: 'height',
        label: '线宽',
        type: EAttrType.number,
        displayParams: {
          min: 1,
          max: 10,
        },
        value: 1,
      },
    ],
  },
  [EFormItemType.table]: {
    type: EFormItemType.table,
    icon: 'icon-biaoge',
    label: '表格',
    attrs: [
      {
        name: 'name',
        label: '控件名称',
        type: EAttrType.sole,
        value: 'table',
      },
      {
        name: 'label',
        label: '标签文本',
        type: EAttrType.input,
        value: '表格',
      },
      {
        name: 'row',
        label: '行',
        type: EAttrType.commander,
        displayParams: {
          target: 'valueTable',
          transform: (row: any, targetValue: any) => {
            const { length } = targetValue;
            if (length < row) {
              const keys = Object.keys(targetValue[0]);
              for (let i = length + 1; i <= row; i += 1) {
                const obj = keys.reduce((pre, key) => {
                  const newPre = pre;
                  newPre[key] = '';
                  return newPre;
                }, {});
                targetValue.push(obj);
              }
              return targetValue;
            }
            if (length > row) {
              return targetValue.splice(0, row);
            }
            return targetValue;
          },
          type: EAttrType.number,
          displayParams: {
            min: 1,
            max: 99,
          },
        },
        value: 1,
      },
      {
        name: 'column',
        label: '列',
        type: EAttrType.commander,
        displayParams: {
          target: 'valueTable',
          transform: (column = [], targetValue: any) =>
            targetValue.map((obj: any) =>
              column.reduce((pre, key) => {
                const newPre = pre;
                // @ts-ignore
                newPre[key as number] = obj[key] || '';
                return newPre;
              }, {}),
            ),
          type: EAttrType.multiInput,
          displayParams: {
            ths: ['序号', '表头'],
            addText: '添加列',
          },
        },
        value: ['姓名', '年龄'],
      },
      {
        name: 'valueTable',
        label: '默认值',
        type: EAttrType.table,
        value: [{ 姓名: '张三', 年龄: '24' }],
      },
    ],
  },
};

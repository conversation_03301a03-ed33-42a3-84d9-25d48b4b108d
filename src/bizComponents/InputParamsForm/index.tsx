import { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Form, Modal } from 'antd';
import Item from './item';
import _, { set } from 'lodash';
import { ReferanceParameterUpdateTypeEnum } from './types';
import type { JsonFormInfo } from './interface';
import { EFormItemType } from './const';
import style from './style/index.less';

// 可传入组件的数据
export interface JsonFormProps {
  /** form id, 方便区分组件 */
  formId: string;
  /** 需要展示和关联的数据字段信息，包括是否有引用参数、是否是新版本、模版数据和inputParam值等 */
  jsonformInfo: JsonFormInfo;
  /** 组件展示方式, 默认：仅查看(ReferanceParameterUpdateTypeEnum.VIEW) */
  updateType?: ReferanceParameterUpdateTypeEnum;
  /** 组件背景颜色，默认：无 */
  backgroundColor?: string;

  /** 数据处理对象 */
  handleFns?: (handle: any) => void;
}

/**
 * 引用参数组件
 * @param props
 * @returns
 */

const InputParamsForm = (props: JsonFormProps, ref: any) => {
  const {
    formId,
    jsonformInfo,
    handleFns,
    updateType = ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR,
    backgroundColor,
  } = props;

  /** 用来记录原始的template值 */
  const [originTemplate, setOriginTemplate] = useState<any[]>([]);
  /** 用来记录原始的inputParam值 */
  const [originInputParam, setOriginInputParam] = useState<
    Record<string, unknown>[] | null
  >(null);
  /** 用来记录修改后的inputParam值 */
  const [inputParamValues, setInputParamValues] = useState<any>(null);
  const [formWidth, setFormWidth] = useState<string>('100%');
  const [labelAlign, setLabelAlign] = useState<'left' | 'right'>('left');
  const [pageConfig, setPageConfig] = useState<any>(null);
  const {
    template = null,
    isReference = false,
    inputParam = null,
  } = jsonformInfo;

  const isView =
    updateType === ReferanceParameterUpdateTypeEnum.VIEW ||
    updateType === ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR;

  const isColor =
    updateType === ReferanceParameterUpdateTypeEnum.EDITANDCOLOR ||
    updateType === ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR;

  const noPadding =
    updateType === ReferanceParameterUpdateTypeEnum.EDIT ||
    updateType === ReferanceParameterUpdateTypeEnum.VIEW;

  const [form] = Form.useForm();
  // const handlerType = Form.useWatch('handlerType', form);

  /**
   * 查询类型为’password‘的item
   */
  const getPasswordArr = (arr: any) => {
    const passwordArr = (arr || [])?.filter(
      (item: any) => item?.type === EFormItemType.password,
    );

    return passwordArr || [];
  };

  // 更新数据
  const updateItem = (
    value: any,
    name: string,
    type?: string,
    showAndHiddenIds?: string[] | undefined,
  ) => {
    // showAndHiddenIds-显隐开关关联的组件ids
    if (showAndHiddenIds && showAndHiddenIds.length > 0) {
      const newTemplate = originTemplate.map((item: any) => {
        if (showAndHiddenIds.includes(item.originId)) {
          return {
            ...item,
            previewShow: value,
          };
        }
        return item;
      });
      setOriginTemplate(newTemplate);
    }
    if (!!type && type === EFormItemType.password) {
      // 因为password需要在focus的时候把数据清空，相当于需要重新initvalue一次
      // 不全局都走该步骤是因为日期有涉及到moment及format格式问题
      const getJsonInputParam = form.getFieldsValue();
      form.setFieldsValue({ ...getJsonInputParam, [name]: value });
    }

    setInputParamValues((preState: any) => {
      // console.log({ ...preState, [name]: value });
      return {
        ...preState,
        [name]: value,
      };
    });
  };

  const handleData = {
    /** 校验form表单数据 */
    validateData: async () => {
      let validateResult = false;
      await form
        .validateFields()
        .then(() => {
          validateResult = true;
        })
        .catch(() => {
          Modal.error({
            content: '检测到你还有必填项需要填写，请检查～',
          });
          validateResult = false;
        });

      return validateResult;
    },

    /**
     * 组装要提交的数据: inputParamValues
     * 1. 通过isNewVersion确定是否需要把密码类型的数据进行加密
     * 2. 把目前密码和旧密码进行比较，如果密码为PASSWORD_DISPLAY_STRING，则把旧的数据回填；如果二者不相等则使用新的密码。
     */
    setupData: async () => {
      if (inputParamValues) {
        const formValue = _.cloneDeep(inputParamValues || {});
        return formValue;
      }
      return null;
    },

    /** 清除数据 */
    clearData: () => {
      setInputParamValues(null);
      setOriginInputParam(null);
      setOriginTemplate([]);
      setFormWidth('100%');
      setLabelAlign('left');
      setPageConfig(null);
    },
  };

  useImperativeHandle(
    ref,
    () => ({
      form: form,
      handleFns: { ...handleData },
    }),
    [handleData, form],
  );

  const init = () => {
    // 判断数据的合法性
    if (isReference && template && inputParam) {
      let originTemplateData: any = null;
      let originInputParamData: any = null;

      // 判断template值是否是json格式
      if (typeof template === 'string') {
        try {
          originTemplateData = JSON.parse(template);
        } catch (err) {
          originTemplateData = [];
        }
      } else {
        originTemplateData = [...(template as any)];
      }

      // 判断inputParam值是否是json格式
      if (typeof inputParam === 'string') {
        try {
          originInputParamData = JSON.parse(inputParam);
        } catch (err) {
          originInputParamData = {};
        }
      } else {
        originInputParamData = { ...inputParam };
      }

      /**
       * 组装初始化数据
       * 如果是密码类型，则把originInputParam设置为PASSWORD_DISPLAY_STRING
       */
      // if (
      //   typeof originInputParamData === 'object' &&
      //   originInputParamData !== null &&
      //   Object.keys(originInputParamData).length === 0
      // ) {
      //   originTemplateData.forEach((item: any) => {
      //     if (item.name) {
      //       originInputParamData[item.name] =
      //         item.value || item.valueArray || item.valueTable;
      //     }
      //   });
      // }
      const values = _.cloneDeep(originInputParamData || {});
      if (originTemplateData[0]?.type === 'Page') {
        // 在线表单
        setFormWidth(originTemplateData[0]?.formWidth + '%');
        setLabelAlign(originTemplateData[0]?.alignType);
        setPageConfig(originTemplateData[0]);
      }
      setOriginTemplate(originTemplateData);
      setOriginInputParam(originInputParamData);
      setInputParamValues({ ...values });
    }
  };

  useEffect(() => {
    init();
  }, [isReference, template, inputParam]);

  useEffect(() => {
    // 每次切换了模版则重置form表单数据
    form.resetFields();
  }, [originTemplate]);

  useEffect(() => {
    if (handleFns) handleFns(handleData);
  }, [inputParamValues]);

  return (
    <div style={{ display: 'flex', justifyContent: labelAlign || 'left' }}>
      <Form
        form={form}
        layout="vertical"
        className={`${style.container} ${style['label' + labelAlign]} ${
          noPadding ? '' : `${style['had-padding']}`
        }`}
        scrollToFirstError={true}
        style={{
          backgroundColor:
            backgroundColor || isColor ? backgroundColor || '#f9fafd' : '',
          width: formWidth,
        }}
        id={formId}
      >
        {/* {originTemplate && JSON.stringify(originTemplate)} */}

        {originTemplate &&
          originTemplate.map((item: any, index: number) => (
            <>
              {item.previewShow === undefined || item.previewShow === 1 ? (
                <Item
                  key={`${formId}-${item?.name}-${index}`}
                  item={item}
                  disabled={isView}
                  onChange={(
                    value: any,
                    name: any,
                    type?: string,
                    showAndHiddenIds?: string[] | undefined,
                  ) => {
                    updateItem(value, name, type, showAndHiddenIds);
                  }}
                  config={{
                    disabled: isView,
                    hasValue:
                      inputParamValues?.hasOwnProperty(item?.name) || false,
                    value:
                      (inputParamValues && inputParamValues[item?.name]) ??
                      undefined,
                    formId,
                    formItemId: `${formId}-${item?.name}-${index}`,
                    pageConfig,
                  }}
                />
              ) : null}
            </>
          ))}
      </Form>
    </div>
  );
};

export default forwardRef(InputParamsForm);

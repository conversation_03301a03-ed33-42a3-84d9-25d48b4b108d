@left-content-padding: 20px;
@left-content-item-padding: 0 8px;

.select-modal {
  :global {
    .ant-space-align-center {
      align-items: flex-start;
    }
  }

  &-content {
    min-width: 660px;
  }

  .content {
    display: flex;
    justify-content: space-between;
    height: 400px;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;

    &-col {
      width: calc((100% - 16px) / 2);

      &:last-of-type {
        background: rgba(247, 250, 251, 0.8);
      }

      .header {
        padding: 10px 10px 0;
      }

      .list {
        padding: 0 10px 20px;
        overflow-x: hidden;
        overflow-y: auto;
      }

      .item-info {
        &-icon {
          width: 24px;
          height: 24px;
          margin-top: -2px;
          border-radius: 50%;
        }

        &-name {
          width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }
    }

    &-source {
      .source-list {
        width: 100%;
        height: calc(100% - 50px);
        margin-top: 12px;
        padding-bottom: 30px;

        &-empty {
          padding-top: 40px;
        }

        &-next {
          color: #334355;
          font-weight: 400;

          &:hover {
            color: #e6f7ff;
            cursor: pointer;
          }
        }

        &-item {
          display: flex;
          align-items: center;
          width: 100%;
          margin-bottom: 4px;
          padding: 0 8px;
          line-height: 32px;

          &:hover,
          &.ant-checkbox-wrapper-checked {
            background-color: #e6f7ff;
            border-radius: 3px;
          }
        }

        .search-list {
          :global {
            .ant-tree-switcher {
              display: none;
            }
            .ant-tree-treenode {
              padding-right: 8px !important;
              padding-left: 8px !important;
            }
          }
        }

        :global {
          .ant-tree-treenode {
            & > * {
              line-height: 32px;
            }
            &::before {
              line-height: 32px;
              border-radius: 2px;
            }

            &:hover::before {
              background: #e6f7ff;
            }

            &.ant-tree-treenode-selected {
              &::before {
                background-color: rgba(47, 154, 255, 0.1);
              }

              .ant-tree-switcher,
              .ant-tree-node-content-wrapper {
                color: #2f9aff;
              }
            }
          }
          .ant-tree-checkbox {
            margin: 8px 8px 0 0;
            line-height: 32px;
          }
        }
      }
    }

    &-target {
      .target-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 3px 0 12px;
        padding: 8px 16px 6px;
        font-size: 13px;
        line-height: 24px;
        border-bottom: 1px solid #d9d9d9;

        .clear-all {
          height: 24px;
          padding: 0 10px;
          font-size: 12px;
          border-radius: 15px;
        }
      }

      .target-list {
        height: calc(100% - 78px);

        &-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin-bottom: 4px;
          padding: 0 8px;
          line-height: 32px;

          &:hover,
          &.ant-checkbox-wrapper-checked {
            background-color: #e6f7ff;
            border-radius: 3px;
          }

          .delete {
            width: 18px;
            height: 18px;
            padding: 0 2px;
            line-height: 18px;
            background: transparent;
            cursor: pointer;

            &:hover {
              background-color: rgba(92, 111, 136, 10%);
            }

            .icon {
              color: #9faab8;
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

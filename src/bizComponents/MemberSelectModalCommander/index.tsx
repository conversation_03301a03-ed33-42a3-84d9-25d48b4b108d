import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Empty, Modal, Space, Spin, Tree, Form, Input } from 'antd';
import type {
  FolderTeamWorkerInfoType,
  DepartmentAndUsersTreeBackType,
} from '@deopCmd/pages/fileManagement/services';
import { IconFont } from 'main/MyIcon';
import {
  DefaultNormalFolderOperateType,
  TeamWorkerTypeEnum,
  TeamworkerTypeInfo,
} from '@deopCmd/pages/fileManagement/type';
import * as Api from '@deopCmd/pages/fileManagement/services';
import _ from 'lodash';

import styles from './index.less';

const { TreeNode, DirectoryTree } = Tree;

interface Props {
  // 弹窗状态
  visible: boolean;
  // 已经选择的部门或者用户
  authList: FolderTeamWorkerInfoType[];
  // 执行成功回调
  onSuccess: (list: FolderTeamWorkerInfoType[]) => void;
  // 关闭/取消操作回调
  onClose: () => void;
}

/**
 * @deprecated 弃用
 */
const AddAuthModal = (props: Props) => {
  const { visible, authList, onSuccess, onClose } = props;
  const [treeList, setTreeList] = useState<DepartmentAndUsersTreeBackType[]>(
    [],
  );
  const [searchList, setSearchList] = useState<
    FolderTeamWorkerInfoType[] | null
  >(null);
  const [dataListLoading, setDataListLoading] = useState<boolean>(false);
  // 当前选中的目录
  const [curSelectedKeys, setCurSelectedKeys] = useState<any[]>([]);

  /** 获取部门用户列表 */
  const queryDepartmentUserList = useCallback(async () => {
    setDataListLoading(true);
    await Api.getDepartmentAndUsersTree()
      .then((res) => {
        setTreeList(res || []);
      })
      .finally(() => {
        setDataListLoading(false);
      });
  }, []);

  /** 模糊匹配获取租户下的所有部门和人员（出去自己） */
  const querySearchDeptAndUsers = useCallback(
    async (value: string) => {
      setDataListLoading(true);
      if (value) {
        await Api.searchDeptAndUser({ search: value })
          .then((res) => {
            const result: FolderTeamWorkerInfoType[] = [];
            if (!!res?.deptList?.length) {
              res.deptList?.map((dept) => {
                result.push({
                  authId: dept.id,
                  authName: dept.name,
                  authType: TeamWorkerTypeEnum.DEPARTMENT,
                });
              });
            }
            if (!!res?.userList?.length) {
              res.userList?.map((user) => {
                result.push({
                  authId: user.userId,
                  authName: user.name,
                  authType: TeamWorkerTypeEnum.PERSONAL,
                });
              });
            }

            setSearchList(
              result.filter((item) => item.authId !== authList[0]?.authId),
            );
          })
          .finally(() => {
            setDataListLoading(false);
          });
      } else {
        queryDepartmentUserList();
        setSearchList(null);
      }
    },
    [authList],
  );

  useEffect(() => {
    if (visible) {
      queryDepartmentUserList();
    }
  }, [visible]);

  // 每次打开初始化选中值
  useEffect(() => {
    if (visible) {
      const keys: any[] = [];
      const newAuthList = _.cloneDeep(authList);
      if (newAuthList?.length >= 1) {
        newAuthList.shift();
      }
      newAuthList?.map((item) => {
        if (item?.authId && !item?.isDelete) {
          if (item.authType === TeamWorkerTypeEnum.PERSONAL) {
            keys.push(`${item.authId}-${item.authId}`);
          } else {
            keys.push(item.authId);
          }
        }
        return item;
      }, []);
      setCurSelectedKeys(keys);
    }
  }, [authList, visible]);

  // 根据 checkedKeys 获取已选中的原始节点信息
  const SelectedList = useMemo(() => {
    const checkedNodes: FolderTeamWorkerInfoType[] = [];

    const traverse = (nodes: DepartmentAndUsersTreeBackType[]) => {
      nodes?.forEach((node) => {
        if (curSelectedKeys.includes(node.id)) {
          const originInfo = authList?.find((item) => item.authId === node.id);

          checkedNodes.push({
            ...(originInfo || {}),
            authId: node.id,
            authName: node.name,
            authType: TeamWorkerTypeEnum.DEPARTMENT,
            operateType: !_.isUndefined(originInfo?.operateType)
              ? originInfo?.operateType
              : DefaultNormalFolderOperateType,
          });
        }
        if (node?.children && node.children?.length > 0) {
          traverse(node.children);
        }
        if (node?.users && node.users?.length) {
          node.users.forEach((user) => {
            const originUserInfo = authList?.find(
              (item) => item?.authId === user.userId,
            );
            if (curSelectedKeys.includes(`${user?.userId}-${user?.userId}`))
              checkedNodes.push({
                ...(originUserInfo || {}),
                authId: user.userId,
                authName: user.name,
                authType: TeamWorkerTypeEnum.PERSONAL,
                operateType: !_.isUndefined(originUserInfo?.operateType)
                  ? originUserInfo?.operateType
                  : DefaultNormalFolderOperateType,
              });
          });
        }
      });
    };

    traverse(treeList);

    return checkedNodes;
  }, [treeList, curSelectedKeys]);

  // 取消
  const onHandleCancel = useCallback(() => {
    setTreeList([]);
    setDataListLoading(false);
    setCurSelectedKeys([]);
    onClose();
  }, [onClose]);

  // 确定
  const onHandleOk = useCallback(async () => {
    onSuccess(SelectedList);
  }, [onSuccess, SelectedList]);

  // 绘制文件夹树
  const renderTreeNodes = useCallback(
    (data: DepartmentAndUsersTreeBackType[]) => {
      return data.map((item: DepartmentAndUsersTreeBackType) => (
        <TreeNode
          expanded={item.id === treeList?.[0]?.id}
          title={
            <Space className={`${styles['tree-item']}`}>
              <IconFont
                type={TeamworkerTypeInfo[TeamWorkerTypeEnum.DEPARTMENT].icon}
              />
              <span className={styles['title']} title={item?.name}>
                {item?.name || ''}
              </span>
            </Space>
          }
          key={item.id}
        >
          {item.children &&
            item.children?.length > 0 &&
            renderTreeNodes(item.children)}
          {item?.users?.map((user) => {
            return (
              <TreeNode
                disabled={authList?.[0]?.authId === user?.userId}
                title={
                  <Space className={`${styles['tree-item']}`}>
                    <IconFont
                      type={
                        TeamworkerTypeInfo[TeamWorkerTypeEnum.PERSONAL].icon
                      }
                    />
                    <span className={styles['title']} title={user?.name}>
                      {user?.name || ''}
                    </span>
                  </Space>
                }
                key={`${user?.userId}-${user?.userId}`}
              />
            );
          })}
        </TreeNode>
      ));
    },
    [curSelectedKeys, treeList],
  );

  // 整个树的树型展示
  const TreeNodes = useMemo(() => {
    return (
      <DirectoryTree
        defaultExpandedKeys={[treeList?.[0]?.id]}
        checkable
        selectable={false}
        onCheck={(checked: any) => {
          setCurSelectedKeys(checked.checked);
        }}
        checkedKeys={curSelectedKeys}
        expandAction={false}
        icon={false}
        checkStrictly={true}
      >
        {renderTreeNodes(treeList)}
      </DirectoryTree>
    );
  }, [treeList, renderTreeNodes]);

  const SourceListRender = useMemo(() => {
    const EmptyElem = (
      <div className={styles['source-list-empty']}>
        <Empty />
      </div>
    );
    if (!searchList) {
      // 显示树
      if (!treeList?.length) {
        return EmptyElem;
      }
      return <>{TreeNodes}</>;
    }
    // 显示搜索列表
    if (!searchList?.length) {
      return EmptyElem;
    }
    return (
      <DirectoryTree
        checkable
        selectable={false}
        onCheck={(checked: any, info) => {
          const keyValue = info.node.key;
          if (info.checked && !curSelectedKeys.includes(keyValue)) {
            setCurSelectedKeys((prestate) => {
              return [...prestate, keyValue];
            });
          }
          if (!info.checked) {
            const index = curSelectedKeys.indexOf(keyValue);

            if (index > -1) {
              setCurSelectedKeys((prestate) => {
                prestate.splice(index, 1);
                return [...prestate];
              });
            }
          }
        }}
        checkedKeys={curSelectedKeys}
        expandAction={false}
        icon={false}
        checkStrictly={true}
        className={styles['search-list']}
      >
        {searchList.map((item) => {
          return (
            <TreeNode
              title={
                <Space className={`${styles['tree-item']}`}>
                  <IconFont type={TeamworkerTypeInfo[item.authType!].icon} />
                  <span className={styles['title']} title={item?.authName}>
                    {item?.authName || ''}
                  </span>
                </Space>
              }
              key={
                item.authType === TeamWorkerTypeEnum.PERSONAL
                  ? `${item.authId}-${item.authId}`
                  : item.authId
              }
            />
          );
        })}
      </DirectoryTree>
    );
  }, [searchList, treeList, TreeNodes, curSelectedKeys]);

  return (
    <Modal
      title={<div style={{ textAlign: 'center' }}>添加协作者</div>}
      visible={visible}
      onCancel={onHandleCancel}
      maskClosable={false}
      footer={[
        <Button key="back" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={onHandleOk}
          disabled={!SelectedList?.length}
        >
          确定
        </Button>,
      ]}
      width={660}
      className={`${styles['select-modal-content']}`}
      wrapClassName={`${styles['select-modal']}`}
      destroyOnClose
    >
      <Spin spinning={dataListLoading}>
        <section className={styles['content']}>
          <div
            className={`${styles['content-col']} ${styles['content-source']} `}
          >
            <div className={styles['header']}>
              {/* <SZSearchForm {...searchForm} /> */}
              <Input.Search
                placeholder="请输入搜索内容"
                onSearch={querySearchDeptAndUsers}
              />
            </div>
            <div className={`${styles['list']} ${styles['source-list']}`}>
              {SourceListRender}
            </div>
          </div>
          <div
            className={`${styles['content-col']} ${styles['content-target']}`}
          >
            <div className={`${styles['header']} ${styles['target-header']}`}>
              <span>
                已选：
                {curSelectedKeys?.length}个
              </span>
              {curSelectedKeys.length > 0 && (
                <Button
                  onClick={() => {
                    setCurSelectedKeys([]);
                  }}
                  className={styles['clear-all']}
                >
                  清空
                </Button>
              )}
            </div>
            {SelectedList.length > 0 && (
              <div className={`${styles['list']} ${styles['target-list']}`}>
                {SelectedList.map((item, index) => {
                  return (
                    <div
                      key={
                        item.authType === TeamWorkerTypeEnum.PERSONAL
                          ? `${item.authId}-${item.authId}`
                          : item.authId
                      }
                      className={styles['target-list-item']}
                    >
                      <Space className={`${styles['tree-item']}`}>
                        <IconFont
                          type={TeamworkerTypeInfo[item.authType!].icon}
                        />
                        <span className={styles['title']}>
                          {item?.authName}
                        </span>
                      </Space>
                      <span
                        className={styles['delete']}
                        onClick={() => {
                          // 从curSelectedKeys中删除该条数据
                          const newSelectedKeys: any[] = [...curSelectedKeys];
                          newSelectedKeys.splice(index, 1);
                          setCurSelectedKeys(newSelectedKeys);
                        }}
                      >
                        <IconFont
                          type={'iconguanbi'}
                          className={styles['icon']}
                        />
                      </span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </section>
      </Spin>
    </Modal>
  );
};

export default AddAuthModal;

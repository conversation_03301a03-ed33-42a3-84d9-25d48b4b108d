import { useControllableValue } from 'ahooks';
import { Input, Select, Space } from 'antd';
import { FC, useState } from 'react';

type ValueTuple<T extends string> = readonly [T, string?];

interface Options<T extends string> {
  label: string;
  value: T;
  child: {
    placeholder: string;
  };
}

interface ComboxSelectSearchProps<T extends string> {
  options: Options<T>[];
  defaultValue?: ValueTuple<T>;
  onChange?: (value: ValueTuple<T>) => void;
  onSearch?: (value: ValueTuple<T>) => void;
  value?: ValueTuple<T>;
}

const ComboxSelectSearch = <T extends string>(props: ComboxSelectSearchProps<T>) => {
  const [value, setValues] = useControllableValue<ValueTuple<T>>(props, {
    defaultValue: props.defaultValue,
  });
  const [curSelect, inputValue] = value;
  // const [inputValue, setInputValue] = useState(_inputValue);
  const { options } = props;

  const inputProps = options.find((item) => item.value === curSelect)?.child;

  return (
    <div className="inline-flex items-center">
      <Space.Compact>
        <Select<T>
          value={curSelect}
          onSelect={(val) => {
            setValues([val, ''] as ValueTuple<T>);
          }}
          options={options}
          className="min-w-[100px]"
        />
        <Input.Search
          allowClear
          value={inputValue}
          placeholder={inputProps?.placeholder}
          onChange={(e) => {
            setValues([curSelect, e.target.value] as ValueTuple<T>);
          }}
          onSearch={(val) => {
            props.onSearch?.([curSelect, val]);
          }}
        />
      </Space.Compact>
    </div>
  );
};

export default ComboxSelectSearch;

import { Table as AntdTable, TableProps } from 'antd';
import React from 'react';

interface CustomTableProps<T> extends TableProps<T> {
  pagination?: false | TableProps<T>['pagination'];
}

const defaultPagination = {
  size: 'small' as const,
  showSizeChanger: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
  showQuickJumper: true,
};

export function Table<T extends object = any>(props: CustomTableProps<T>) {
  const { pagination, ...rest } = props;

  return <AntdTable<T> {...rest} pagination={pagination === false ? false : { ...defaultPagination, ...pagination }} />;
}

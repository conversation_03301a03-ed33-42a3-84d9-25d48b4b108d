import { useMemo, useState } from 'react';
import { Table, Space, Button, Modal, Input, Alert, Typography, message, Switch } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { NO_DATA_IN_TABLE_COLUMN_DISPLAY } from '../../const';
import { useRequest } from 'ahooks';
import * as API from './services';
import NiceModal from '@ebay/nice-modal-react';
import AddAkSkModal from './components/AddAkSkModal';
import AkSkDetailModal from './components/AkSkDetailModal';
import { PageContainer } from 'main/PageContainer';
import { PlusOutlined } from '@ant-design/icons';
import { PageListContent } from 'main/PageListContent';
import { useFrameWorkContext } from '@/exposes/deopCmdEntry/context';
import { useSearchParams } from 'umi';
import { TableRecordType, AkSkDetailType } from './services/interface';
import { CopyIcon } from 'main/MyIcon';

const initParams = {
  pageNo: 1,
  pageSize: 20,
  name: undefined,
};

const AkSkManagement = () => {
  const frameWorkContextInfo = useFrameWorkContext();
  const [pageSearchParams] = useSearchParams();
  const pageCode = pageSearchParams.get('code');
  const [searchParams, setSearchParams] =
    // @ts-ignore
    useState<GetVariableListReq>(initParams);

  /** 按钮权限 */
  const { pageTabsAndbtnsPremission } = frameWorkContextInfo;
  const Auth = useMemo(() => {
    if (!pageCode) return {};
    const pageTabAndbuttonsPremission = pageTabsAndbtnsPremission?.[pageCode] || [];
    return {
      tagDefault: pageTabAndbuttonsPremission.includes('tagDefault'), // 页面
      disableOrEnable: pageTabAndbuttonsPremission.includes('hvcnxrvelf'), // 启用/禁用
      add: pageTabAndbuttonsPremission.includes('iniisneifsenf'), // 新增标签
      edit: pageTabAndbuttonsPremission.includes('matztsgxtf'), // 编辑
      delete: pageTabAndbuttonsPremission.includes('rhciadiaas'), // 删除
    };
  }, [pageTabsAndbtnsPremission]);

  const {
    data,
    loading,
    refresh: refreshList,
  } = useRequest(() => API.getPageList(searchParams), {
    refreshDeps: [searchParams],
    debounceLeading: true,
    debounceWait: 800,
  });

  const list = data?.records || [];

  const columns: ColumnsType<TableRecordType> = [
    {
      title: '秘钥名称',
      dataIndex: 'name',
      width: 130,
      ellipsis: true,
    },
    {
      title: 'AccessKey',
      dataIndex: 'appKey',
      width: 220,
      ellipsis: true,
      render: (text, record) => (
        <div className="flex items-center gap-[8px]">
          <div className="truncate" title={text}>
            {text}
          </div>
          <CopyIcon copyStr={text} />
        </div>
      ),
    },
    {
      title: 'SecretKey',
      dataIndex: 'appSecret',
      width: 250,
      ellipsis: true,
    },
    {
      title: '有效期',
      dataIndex: 'type',
      width: 200,
      ellipsis: true,
      render: (value: number, record: TableRecordType) => {
        return <div>{value === 1 ? '永久有效' : `${record.startDate} - ${record.endDate}`}</div>;
      },
    },
    {
      title: '启用/禁用',
      dataIndex: 'enabled',
      width: 100,
      filters: [
        {
          text: '禁用',
          value: 0,
        },
        {
          text: '启用',
          value: 1,
        },
      ],
      onFilter: (value: number, record: any) => record.enabled === value,
      render: (enabled: number, record: any) => {
        return (
          <Switch
            // unCheckedChildren="禁用"
            // checkedChildren="启用"
            onChange={async (checked) => {
              if (checked) {
                await API.enableAkSk({
                  id: record.id,
                });
                message.success('启用成功');
                refreshList();
              } else {
                Modal.confirm({
                  title: '是否确定【禁用】当前AK/SK?',
                  content: '禁用后使用此秘钥的 OpenAPI 将无法使用。',
                  okText: '禁用',
                  async onOk() {
                    await API.disableAkSk({
                      id: record.id,
                    });
                    message.success('禁用成功');
                    refreshList();
                  },
                });
              }
            }}
            checked={enabled === 1}
          />
        );
      },
    },
    {
      title: '更新日期',
      dataIndex: 'modifyTime',
      width: 170,
      ellipsis: true,
      render: (value: any) => value || NO_DATA_IN_TABLE_COLUMN_DISPLAY,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      fixed: 'right',
      width: 80,
      render: (text, record) => {
        return (
          <Space>
            <Typography.Link
              type="danger"
              onClick={async () => {
                Modal.confirm({
                  title: '是否确定【删除】当前AK/SK?',
                  content: '删除后使用此秘钥的 OpenAPI 将无法使用。',
                  okText: '删除',
                  okButtonProps: {
                    danger: true,
                    loading,
                  },
                  async onOk() {
                    await API.deleteAkSk({ id: record.id });
                    message.success('删除成功');
                    refreshList();
                  },
                });
              }}>
              删除
            </Typography.Link>
          </Space>
        );
      },
    },
  ];

  return (
    <NiceModal.Provider>
      <PageContainer>
        <PageListContent>
          <Alert
            className="mb-[20px]"
            message="AK/SK功能为租户提供全局通用安全访问秘钥，请谨慎使用。"
            type="info"
            banner
            showIcon
            closable
          />
          <div className="mb-[20px] flex justify-between">
            <Space>
              <Input.Search
                onSearch={(name) =>
                  setSearchParams((prev: any) => ({
                    ...prev,
                    name,
                    pageNo: 1,
                  }))
                }
                placeholder="请输入秘钥名称"
              />
            </Space>
            <div>
              <Button
                type="primary"
                onClick={() => {
                  NiceModal.show(AddAkSkModal, {
                    onSuccess: (detail: AkSkDetailType) => {
                      NiceModal.show(AkSkDetailModal, {
                        onSuccess: refreshList,
                        detail,
                      });
                    },
                  });
                }}
                icon={<PlusOutlined rev={1} />}>
                新增AK/SK
              </Button>
            </div>
          </div>
          <Table
            columns={columns}
            rowKey={(r) => r.id}
            dataSource={list}
            scroll={{ y: `calc(100vh - 318px - 58px)` }}
            loading={loading}
            pagination={{
              size: 'small',
              onChange(pageNo, pageSize) {
                setSearchParams((prev) => ({
                  ...prev,
                  pageNo,
                  pageSize,
                }));
              },
              total: Number(data?.total),
              pageSize: Number(data?.size),
              current: Number(data?.current),
              showSizeChanger: true,
              showTotal(total) {
                return `共 ${total} 条记录`;
              },
              showQuickJumper: true,
            }}
          />
        </PageListContent>
      </PageContainer>
    </NiceModal.Provider>
  );
};

export default AkSkManagement;

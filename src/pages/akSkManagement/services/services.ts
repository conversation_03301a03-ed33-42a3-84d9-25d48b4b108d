import request from '@deopCmd/utils/request';
export const getPageList = async (params: any) => {
  return request<any>('/opt/aksk/1.0.0/secret/v2/page', {
    method: 'post',
    data: params,
  });
};



export const addAkSk = async (params: any) => {
  return request<{ id: string }>(
     `/opt/aksk/1.0.0/secret/v2/save`,
    {
      method: 'post',
      data: params,
    },
  );
};

export const disableAkSk = async (params: any) => {
  return request<{ id: string }>(`/opt/aksk/1.0.0/secret/v2/disable`, {
    method: 'post',
    data: params,
  });
};

export const enableAkSk = async (params: any) => {
  return request<{ id: string }>(`/opt/aksk/1.0.0/secret/v2/enable`, {
    method: 'post',
    data: params,
  });
};

export const deleteAkSk = async (params: { id: string }) => {
  return request<{ id: string }>(`/opt/aksk/1.0.0/secret/v2/delete`, {
    method: 'post',
    data: params,
  });
};

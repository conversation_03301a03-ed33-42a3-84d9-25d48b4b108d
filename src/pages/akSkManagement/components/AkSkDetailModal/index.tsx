import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { Button, Modal, Space } from 'antd';
import React from 'react';
import { IconFont, CopyIcon } from 'main/MyIcon';
import { AkSkDetailType } from '../../services/interface';

interface Iprops {
  onSuccess: () => void;
  detail: AkSkDetailType;
}
export const AddAkSkModal: React.FC<Iprops> = (props) => {
  const { onSuccess, detail } = props;
  const modal = useModal();

  return (
    <Modal
      onCancel={() => {
        onSuccess?.();
        modal.hide();
        setTimeout(modal.remove, 500);
      }}
      open={modal.visible}
      title="生成AK/SK"
      width={570}
      footer={
        <Space>
          <Button
            type="primary"
            onClick={() => {
              onSuccess?.();
              modal.hide();
              setTimeout(modal.remove, 500);
            }}>
            完成
          </Button>
        </Space>
      }>
      <div className="mb-[20px]">
        <div className="flex items-center">
          <div className="mr-[20px] w-[84px] flex-none text-[14px] text-[#86909C]">调用系统名称</div>
          <div className="shrink truncate text-[14px] text-[#3B3D3D]" title={detail?.name}>
            {detail?.name}
          </div>
        </div>
        <div className="mt-[12px] flex items-center">
          <div className="mr-[20px] w-[84px] text-[14px] text-[#86909C]">有效期</div>
          <div
            className="flex-1 truncate text-[14px] text-[#3B3D3D]"
            title={detail?.type === 1 ? '永久有效' : `${detail?.startDate} - ${detail?.endDate}`}>
            {detail?.type === 1 ? '永久有效' : `${detail?.startDate} - ${detail?.endDate}`}
          </div>
        </div>
      </div>

      <div>
        <div className="flex h-[40px] items-center gap-[16px] bg-[#F3F3F3] px-[8px]">
          <div className="w-[50%] text-[14px] text-[#505962]">AccessKey</div>
          <div className="w-[50%] text-[14px] text-[#505962]">SecretKey</div>
        </div>
        <div className="mb-[20px] flex h-[40px] items-center gap-[16px] border-x-0 border-b-[1px] border-t-0 border-solid border-[#E7E7E7] bg-[#fff] px-[8px]">
          <div className="flex w-[50%] items-center gap-[8px] text-[14px] text-[#505962]">
            <div className="w-[220px] truncate" title={detail?.appKey}>
              {detail?.appKey}
            </div>
            <CopyIcon copyStr={detail?.appKey} />
          </div>
          <div className="flex w-[50%] items-center gap-[8px] text-[14px] text-[#505962]">
            <div className="w-[220px] truncate" title={detail?.appSecret}>
              {detail?.appSecret}
            </div>
            <CopyIcon copyStr={detail?.appSecret} />
          </div>
        </div>
      </div>

      <div className="rounded-[6px] bg-[#F5F4F4] px-[16px] py-[10px] text-[12px] text-[#3B3D3D]">
        <div className="flex items-center">
          <IconFont type="iconzhuyi" className="mr-[4px] text-[18px]" />
          <div className="text-[16px] text-[#4A4B4E]">注意</div>
        </div>
        <div className="my-[4px]">1. 请将您的秘钥复制后保存在本地，因为您将无法再次查看它。 </div>
        <div>2. 如果丢失秘钥，则需要重新生成一个新秘钥 </div>
      </div>
    </Modal>
  );
};

export default NiceModal.create(AddAkSkModal);

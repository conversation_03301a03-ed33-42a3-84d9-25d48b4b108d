import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { Button, Modal, Form, Input, Space, message, Radio, DatePicker } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import React from 'react';
import * as API from '../../services';
import { AkSkDetailType } from '../../services/interface';

interface Iprops {
  onSuccess: (detail: AkSkDetailType) => void;
}
export const AddAkSkModal: React.FC<Iprops> = (props) => {
  const { onSuccess } = props;
  const modal = useModal();
  const [form] = useForm<any>();

  const type = Form.useWatch('type', form);

  const onSubmit = async () => {
    const val = await form.validateFields();
    const { dates, name, type } = val;
    const params = {
      name: name?.trim(),
      type,
      enabled: 1,
      startDate: dates ? dates[0].format('YYYY-MM-DD') : undefined,
      endDate: dates ? dates[1].format('YYYY-MM-DD') : undefined,
    };
    const detail: any = await API.addAkSk(params);
    onSuccess?.(detail);
    message.success('新增成功');

    modal.hide();
    setTimeout(modal.remove, 500);
  };

  return (
    <Modal
      onCancel={() => {
        modal.hide();
        setTimeout(modal.remove, 500);
      }}
      open={modal.visible}
      title="新增AK/SK"
      width={570}
      footer={
        <Space>
          <Button
            onClick={() => {
              modal.hide();
              setTimeout(modal.remove, 500);
            }}>
            取消
          </Button>
          <Button type="primary" onClick={onSubmit}>
            确定
          </Button>
        </Space>
      }>
      <Form layout="vertical" form={form} initialValues={{ type: 0 }}>
        <Form.Item
          name="name"
          label="秘钥名称"
          rules={[
            {
              required: true,
              message: '请输入调用秘钥的名称',
            },
          ]}
          required>
          <Input placeholder="请输入调用秘钥的名称" maxLength={64} />
        </Form.Item>

        <Form.Item name="type" label="有效期">
          <Radio.Group
            options={[
              { label: '有效期范围', value: 0 },
              { label: '永久', value: 1 },
            ]}></Radio.Group>
        </Form.Item>

        {type === 0 && (
          <Form.Item
            name="dates"
            label="有效期时间"
            rules={[
              {
                required: true,
                message: '请选择有效期时间',
              },
            ]}>
            <DatePicker.RangePicker style={{ width: '100%' }} />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default NiceModal.create(AddAkSkModal);

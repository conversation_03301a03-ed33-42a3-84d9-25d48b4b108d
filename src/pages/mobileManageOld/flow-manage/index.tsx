import { useState, useEffect, useCallback, useMemo } from 'react';
import { Form, message, Space, Tooltip, Tag } from 'antd';
import { PageContainer } from 'main/PageContainer';
import { PageListContent } from 'main/PageListContent';
import { useSearchParams } from 'umi';
import { useFrameWorkContext } from '@/exposes/deopCmdEntry/context';
import { SHOW_PARENT } from 'rc-tree-select';
import { DefaultNoDataStr, PaginationInitData } from '@/constants';
import { ListBackType } from '@/interface';
import { ProcessListParamsType, ProcessListSingleBackType } from './interface';
import * as Api from './services';
import moment from 'moment';
import { setIdxForPaginationData } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import { StatusContent } from 'main/StatusContent';
import ProcessDetailDrawer from './components/ProcessDetailDrawer';
import * as GlobalServiceApi from '@/services/global';
import { loopUpdateTreeData } from '@/constants/formatDepartmentTree';
import {
  SzConfirm,
  SzSearchForm,
  SZDatePickerType,
  GetPopupContainerLayerType,
  SzTable,
  SzTooltip,
} from 'main/SzComponents';
import styles from './index.less';
import TagList from '@/bizComponents/TagList';
import NiceModal from '@ebay/nice-modal-react';
import AddProcessDrawer from './components/AddProcessDrawer';
import EditProcessDrawer from './components/EditProcessDrawer';
import useAddAndEditDrawerStore from '@/pages/taskPlan/components/addAndEditDrawer/store';
import AddAndEditDrawer from '@/pages/taskPlan/components/addAndEditDrawer';
import { TaskPlanTypeEnum } from '@/types/taskcenter/type';

// 流程列表
const ProcessManagement = () => {
  const { setVisible, setId, setTaskPlanType, setSingleFlow } = useAddAndEditDrawerStore();
  const frameWorkContextInfo = useFrameWorkContext();
  const [pageSearchParams] = useSearchParams();
  const pageCode = pageSearchParams.get('code');
  const [modalSearchForm] = Form.useForm();
  // 部门树信息/列表
  const [departmentTrees, setDepartmentTrees] = useState<any[]>([]);
  //  流程列表
  const [listData, setListData] = useState<ListBackType<ProcessListSingleBackType>>({});
  const [loading, setLoading] = useState<boolean>(false);
  /** 用来记录当前搜索数据是否处于变化未查询过，如果是的话默认从第1页开始查询 */
  const [isSearchValueChange, setIsSearchValueChange] = useState<boolean>(false);
  // 当前操作的流程
  const [curProcessInfo, setCurProcessInfo] = useState<ProcessListSingleBackType | null>(null);
  // 详情页开关
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [pageNo, setPageNo] = useState(PaginationInitData.pageNo);
  const [pageSize, setPageSize] = useState(PaginationInitData.pageSize);

  /** 按钮权限 */
  const { pageTabsAndbtnsPremission } = frameWorkContextInfo;
  // const Auth = useMemo(() => {
  const Auth = useMemo(() => {
    return {
      flowListDefault: true, // 页面
      uploadFlow: true, // 上传流程
      addPlan: true, // 新增计划
      edit: true, // 编辑
      detail: true, // 详情
      delete: true, // 删除流程
    };
  }, [pageTabsAndbtnsPremission]);

  // 获取部门树
  const queryDepartmentTrees = useCallback(async () => {
    await GlobalServiceApi.getDepartmentTrees().then(({ bizData }) => {
      if (bizData && bizData.id) {
        const data = loopUpdateTreeData([bizData], '0');
        setDepartmentTrees(data);
      }
    });
  }, []);

  // 查询列表数据
  const queryList = async (params?: ProcessListParamsType) => {
    setLoading(true);
    const values = modalSearchForm.getFieldsValue();
    const { date, ...restValues } = values;
    const newParams = params || {};
    if (date) {
      newParams.startModifiedTime = date[0] && moment(date[0]).format('YYYY-MM-DD 00:00:00');
      newParams.endModifiedTime = date[1] && moment(date[1]).format('YYYY-MM-DD 23:59:59');
    }
    if (isSearchValueChange) {
      newParams.pageNo = PaginationInitData.pageNo;
    }
    newParams.pageSize = newParams?.pageSize || Number(listData.size || 0) || PaginationInitData.pageSize;

    await Api.getProcessList({
      ...restValues,
      ...newParams,
      menuCode: pageSearchParams.get('code'),
      processChannel: 0,
    })
      .then(({ bizData }) => {
        setIdxForPaginationData(bizData?.records || [], newParams?.pageNo, newParams?.pageSize);
        setListData(bizData || {});
      })
      .finally(() => {
        setLoading(false);
        setIsSearchValueChange(false);
      });
  };

  const resetPageNumAndQueryList = (params?: ProcessListParamsType) => {
    queryList({ ...params, pageNo: 1 });
  };

  // 搜索
  const searchForm = {
    formData: [
      {
        name: 'processName',
        component: {
          name: 'Search',
          allowClear: true,
          style: { width: 200 },
          placeholder: '请输入流程名称',
          onSearch(value: string) {
            queryList({ processName: value });
          },
        },
      },
      {
        name: 'departmentIdsQuery',
        component: {
          name: 'TreeSelect',
          treeNodeFilterProp: 'title',
          popupContainerLayer: GetPopupContainerLayerType.PROGRIDCONTENT,
          treeData: departmentTrees,
          placeholder: '请选择所属部门',
          showSearch: true,
          showCheckedStrategy: SHOW_PARENT,
          allowClear: true,
          style: { width: 200 },
          onChange() {
            resetPageNumAndQueryList();
          },
        },
      },
      {
        name: 'isPublish',
        component: {
          name: 'Select',
          popupContainerLayer: GetPopupContainerLayerType.PROGRIDCONTENT,
          placeholder: '选择状态',
          allowClear: true,
          style: { width: 120 },
          options: {
            data: [
              { key: true, name: '已启用' },
              { key: false, name: '已禁用' },
            ],
            keyStr: 'key',
            valStr: 'name',
          },
          onChange() {
            resetPageNumAndQueryList();
          },
        },
      },
      {
        name: 'date',
        component: {
          name: 'RangePicker',
          placeholder: ['更新时间', '更新时间'],
          style: { width: 280 },
          dateType: SZDatePickerType.RANGEPICKER,
          popupContainerLayer: GetPopupContainerLayerType.PROGRIDCONTENT,
          allowClear: true,
          disabledDate: (current: any) => {
            return current && current > moment().endOf('day');
          },
          onChange: (date: any) => {
            modalSearchForm.setFieldsValue({ date });
            resetPageNumAndQueryList({
              startModifiedTime: date && date[0] && moment(date[0]).format('YYYY-MM-DD 00:00:00'),
              endModifiedTime: date && date[1] && moment(date[1]).format('YYYY-MM-DD 23:59:59'),
            });
          },
        },
      },
    ],
    actionData: [
      {
        name: 'addProcess11',
        component: {
          name: 'Button',
          hidden: !Auth?.uploadFlow,
          buttonText: '上传流程',
          icon: <PlusOutlined rev={undefined} />,
          type: 'primary',
          onClick: () => {
            NiceModal.show(AddProcessDrawer, {
              onClose: resetPageNumAndQueryList,
            });
          },
        },
      },
    ],
    form: modalSearchForm,
    initialValues: {},
    onFormChange: queryList,
    onValuesChange: () => {
      setIsSearchValueChange(true);
    },
  };

  // 列信息
  const columns = [
    // {
    //   title: '序号',
    //   dataIndex: 'idx',
    //   width: 80,
    // },
    {
      title: '流程名称',
      dataIndex: 'processName',
      width: 180,
    },
    {
      title: '标签',
      width: 220,
      dataIndex: 'tagList',
      render: (_: any, record: any) => <TagList list={record?.tagList || []} maxCount={2} emptyText={'--'} />,
    },
    {
      title: '最新版本',
      dataIndex: 'processLastVersion',
      width: 100,
      ellipsis: true,
    },
    {
      title: '启用版本',
      dataIndex: 'publishVersions',
      width: 150,
      render: (text: any) => text?.join('/') || DefaultNoDataStr,
    },
    {
      title: '版本数',
      dataIndex: 'processDetailCount',
      width: 90,
      ellipsis: true,
    },
    {
      title: '所属部门',
      dataIndex: 'deptInfos',
      ellipsis: {
        showTitle: false,
      },
      width: 220,
      render: (deptInfos: ProcessListSingleBackType['deptInfos']) => {
        if (!deptInfos || !deptInfos?.length) return DefaultNoDataStr;
        const deptNames: string[] = [];
        deptInfos.map((deptItem: { deptId?: string; name?: string }) => {
          if (!!deptItem?.deptId && !!deptItem?.name) {
            deptNames.push(deptItem.name);
          }

          return deptItem;
        });

        if (!deptNames?.length) return DefaultNoDataStr;
        return (
          <SzTooltip
            title={deptNames.join('，')}
            placement="topLeft"
            popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}>
            {deptNames.join('，')}
          </SzTooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'isPublish',
      width: 100,
      render: (val: boolean) => {
        return (
          <StatusContent
            statusMapInfo={{
              [true.toString()]: {
                label: '已启用',
                color: '#2F9AFF',
              },
              [false.toString()]: {
                label: '已禁用',
                color: '#5C6F88',
              },
            }}
            status={val}
          />
        );
      },
    },
    {
      title: '更新时间',
      dataIndex: 'modifyTime',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 200,
      render: (_: any, record: ProcessListSingleBackType) => {
        return (
          <Space>
            {/* <a
              hidden={!Auth.addPlan}
              onClick={() => {
                setVisible(true);
                setTaskPlanType(TaskPlanTypeEnum.SINGLE);
                setId(undefined);
                setSingleFlow({
                  jobName: `${record.processName} ${moment().format('YYYY-MM-DD HH:mm:ss')}`,
                  processDetailUuid: record.processUsedUuid || '',
                });
              }}>
              新增计划
            </a> */}
            <a
              hidden={!Auth.edit}
              onClick={async () => {
                await NiceModal.show(EditProcessDrawer, {
                  processUuid: record.processUuid,
                  onClose: () => queryList({ pageNo, pageSize }),
                });
              }}>
              编辑
            </a>
            <a
              hidden={!Auth.detail}
              onClick={() => {
                setCurProcessInfo(record);
                setDetailVisible(true);
              }}>
              详情
            </a>
            <a
              onClick={() => {
                SzConfirm({
                  title: '提示',
                  content: `将同时删除此流程下的所有版本，确定删除流程？`,
                  className: 'confirm-delete',
                  okText: '删除',
                  onOk: async () => {
                    await Api.deleteProcessAllVersion({
                      processUuid: record.processUuid!,
                    }).then(async ({ bizData }: any) => {
                      if (!!bizData && bizData?.success) {
                        message.success('流程删除成功～');
                        await queryList({ pageNo, pageSize });
                      } else {
                        message.success('流程删除失败～');
                      }
                    });
                  },
                });
              }}
              className="table-delete"
              hidden={!Auth.delete}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    queryDepartmentTrees();
    queryList();
  }, []);

  return (
    <NiceModal.Provider>
      <PageContainer>
        <div className={styles['page-list']}>
          <PageListContent>
            <SzSearchForm {...searchForm} />
            <SzTable
              rowKey={'processUuid'}
              tableData={listData}
              columns={columns}
              loading={loading}
              scroll={{ x: 1600, y: `calc(100vh - 318px)` }}
              onPageChange={(currentPage: number, pageSize: number) => {
                setPageNo(currentPage);
                setPageSize(pageSize);
                queryList({ pageNo: currentPage, pageSize: pageSize });
              }}
            />
          </PageListContent>
        </div>
        {/* 流程详情 */}
        <ProcessDetailDrawer
          type={1}
          detail={curProcessInfo}
          visible={detailVisible}
          onClose={() => {
            setCurProcessInfo(null);
            setDetailVisible(false);
          }}
          onRefreshList={queryList}
        />

        {/** 立即创建计划 */}
        {/* <AddAndEditDrawer /> */}
      </PageContainer>
    </NiceModal.Provider>
  );
};
export default ProcessManagement;

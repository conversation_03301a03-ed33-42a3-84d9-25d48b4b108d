import { PaginationParamsType, BizDictBackType } from '@/interface.d';
import { TaskExcuteRecordEnum } from '@/bizComponents/WorkRecord/types';
import { ProcessStateLabelEnum } from './type';

/** ************************************ 请求信息 ************************************* */
/** 获取流程列表的请求参数 */
export interface ProcessListParamsType extends PaginationParamsType {
  /** 所属部门 */
  departmentIdsQuery?: number[];
  /** 上架状态 */
  isPublish?: boolean;
  /** 流程名称 */
  processName?: string;
  /** 更新开始时间 */
  startModifiedTime?: string;
  /** 更新结束时间 */
  endModifiedTime?: string;
  /** 业务类型中文查询 */
  dictValue?: string;
}

/** 上传流程包的请求参数 */
export interface UploadProcessParamsType {
  /** 文件路径 */
  filePath?: string;
  /** 文件名称 */
  fileName?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 文件md5 */
  fileMd5?: string;
}

/** ************************************** 更新流程包 ******************************************* */
/** 更新流程包的请求参数 */
export interface UpdateParamsType {
  packageUuid: string;
  isPublish?: boolean;
  departmentIds?: number[] | string[];
  processHumanMinute?: number;
  processRobotMinute?: number;
  processName: string;
  processDescription?: string;
  processProfile?: string;
  bots?: object[];
  processVersionLabel?: ProcessStateLabelEnum;
  roiType?: number;
  tagIds?: string[];
}

export interface EditParamsType {
  processUuid: string;
  isPublish?: boolean;
  processHumanMinute?: number;
  processRobotMinute?: number;
  processName: string;
  processProfile?: string;
  departmentIds?: string[];
  tagIds?: string[];
  // 指定机器人
  bots?: object[];
}

/** ************************************** 删除流程包 ******************************************* */
/** 删除流程包所有版本 */
export interface DeleteProcessAllVersionParamsType {
  processUuid: string;
}

/** ************************************** 流程包详情 ******************************************* */
/** 查询流程详情 */
export interface QueryProcessDetail {
  processUuid: string;
}

/** 上/下架、删除流程包版本的请求参数 */
export interface ChangeProcessPublishStateParamsType {
  processDetailUuid?: string;
}

/** 查询流程版本信息 */
export interface GetProcessVersionDetailParamsType {
  packageUuid: string;
  processChannel: number;
}

export interface UpdateProcessParamsType
  extends ChangeProcessPublishStateParamsType {
  processHumanMinute?: string;
  processRobotMinute?: string;
}

export interface GetWorkExcuteListParamsType extends PaginationParamsType {
  status?: TaskExcuteRecordEnum;
  processDetailUuid?: string;
  processUuid?: string;
}

/** ************************************** 流程包使用说明 ******************************************* */
/** 获取流程介绍详情的请求参数 */
export interface GetProcessIntroduceDetailParamsType {
  // 请求唯一标志性
  requestUuid?: string;
  // 流程包id
  processUuid?: string;
}

/** 更新流程介绍详情的请求参数 */
export interface UpdateProcessIntroduceParamsType {
  // 流程包id
  processUuid?: string;
  // 富文本内容
  richExtFormat?: string;
}

/** 更新流程介绍详情的请求参数 */
export interface DeleteOneVersionProcessParamsType {
  // 流程包版本号
  processDetailUuid?: string;
}

/** ************************************ 返回信息 ************************************* */
/** 流程列表的单条返回数据 */
export interface ProcessListSingleBackType {
  // 流程id
  processUuid?: string;
  packageInnerId?: string;
  // 流程名称
  processName?: string;
  // 是否有说明，1-有
  hasIntroduction?: 0 | 1;
  // 最新版本
  processUsedVersion?: string;
  // 是否已发布
  isPublish?: boolean;
  // 更新时间
  modifyTime?: string;
  // 预估人工时间
  processHumanMinute?: number;
  processRobotMinute?: number;
  // 上传者
  processCreatorRealName?: string;
  processProfile?: string;
  // 所属部门
  deptInfos?: {
    name?: string;
    deptId?: string;
  }[];
  // 指定机器人
  bots?: object[];
  processLatestUuid: string;
  processChannel: number;
  processUsedUuid: string;
}

/** 上传流程包返回数据 */
export interface UploadProcessBackType {
  processHumanMinute?: number;
  roiPercent?: number;
  bizDict?: BizDictBackType;
  labelDict?: BizDictBackType;
  packageInnerId?: string;
  packageUuid?: string;
  processType?: number;
  processVersion?: string;
  processName?: string;
  packageUploader?: string;
  processIcon?: string;
  processDescription?: string;
  deptIds?: string[];
  hasReference?: boolean;
  roiType?: number;
}

/** 流程版本详情信息 */
export interface ProcessVersionDetailType {
  packageUuid: string;
  packageInnerId: string;
  processUuid: string;
  processDetailUuid: string;
  processName: string;
  processVersion: string;
  processIcon?: string;
  isPublish?: boolean;
  processProfile?: string;
  processCreatorRealName?: string;
  processCreatorId?: string;
  processHumanMinute?: string;
  processRobotMinute?: string;
  processPackageInnerId?: string;
  processCreateTime?: string;
  processChannel?: number;
  processRobotMinuteTime?: string;
}

/** 使用说明返回数据类型 */
export interface ProcessIntroduceType {
  processUuid: string;
  processChannel: number;
  richExtFormat: string;
  exist: boolean;
}

/** 流程包详情-查看流程版本文件信息 */
export interface ProcessVersionFileType {
  packageId: number;
  packageUuid: string;
  processChannel: number;
  processName: string;
  packageName: string;
  packagePath: string;
  packageMd5: string;
  packageSize: number;
}

import React, { useMemo } from 'react';
import { useModal } from '@ebay/nice-modal-react';
import NiceModal from '@ebay/nice-modal-react';
import { Button, Drawer, Form, message, Space, Spin } from 'antd';

import * as ServiceApi from './../services';
import { useRequest } from 'ahooks';
import { omit, pick } from 'lodash';
import EditProcessForm, { FormType, ProcessFormInstance } from './EditProcessForm';

interface Props {
  processUuid: string;
  onClose?: () => void;
}
/** 编辑流程弹窗 */
const EditProcessDrawer: React.FC<Props> = (props) => {
  const [form] = Form.useForm() as [form: ProcessFormInstance];
  const modal = useModal();

  const { data: processDetail, loading } = useRequest(
    async () => {
      const res = await ServiceApi.getProcessDetail({
        processUuid: props.processUuid,
      });
      return res.bizData;
    },
    {
      ready: Boolean(props.processUuid),
      refreshDeps: [props.processUuid],
      onSuccess: (data) => {
        const val: Partial<FormType> = pick(data, [
          'processName',
          'tagList',
          'processProfile',
        ]);

        val.departmentIds = data?.deptInfos.map((item) => item.deptId);

        form.setFieldsValue(val);
      },
    },
  );

  const handleClose = () => {
    props.onClose?.();
    modal.hide();

    setTimeout(modal.remove, 600);
  };

  if (!props.processUuid) {
    message.warn('请重新选择要编辑的流程');
    return null;
  }

  return (
    <Drawer
      width={650}
      open={modal.visible}
      title="上传流程"
      onClose={handleClose}
      footer={
        <Space>
          <Button onClick={handleClose}>取消</Button>
          <Button
            type="primary"
            onClick={async () => {
              const val = await form.validateFields();
              const tagIds = val?.tagList?.map((item) => item.id);
              const formatVal: Parameters<typeof ServiceApi.editProcess>['0'] =
                {
                  ...omit(val, ['tagList']),
                  tagIds,
                  processUuid: props.processUuid,
                  isPublish: true,
                };
              await ServiceApi.editProcess(formatVal);

              message.success('编辑成功');
              handleClose();
            }}
          >
            确定
          </Button>
        </Space>
      }
      destroyOnClose
    >
      <Spin spinning={loading}>
        <EditProcessForm form={form} processDetail={processDetail} />
      </Spin>
    </Drawer>
  );
};

export default NiceModal.create(EditProcessDrawer);

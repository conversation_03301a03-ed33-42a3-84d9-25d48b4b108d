import { useCallback, useEffect, useState } from 'react';
import { Spin, Pagination, Collapse, Space } from 'antd';
import {
  GetWorkExcuteListParamsType,
  ProcessListSingleBackType,
} from '../../interface';
import { getWorkRecordList } from '../../services';
import { WorkRecordType as WorkRecordListBackType } from '@/bizComponents/WorkRecord/interface';
import { deleteRunnableWorkRecord } from '../../services';
import { ListBackType } from '@/interface';
import { PaginationParamsType } from '@/interface.d';
import { PaginationInitData } from '@/constants';
import {
  TimeConversion,
  RenderText,
} from '@/bizComponents/WorkRecord/constants';
import { IconFont, CopyIcon } from 'main/MyIcon';
import InputParamsModal from '@/bizComponents/WorkRecord/InputParamsModal';
import WorkLogModal from '@/bizComponents/WorkRecord/WorkLogModal';
import WorkVideoModal from '@/bizComponents/WorkRecord/WorkVideoModal';
import WorkRecordSteps from '@/bizComponents/WorkRecord/WorkRecordSteps';
import {
  TaskExcuteRecordEnum,
  ActiveStatusColorMap,
  ActiveStatusIconMap,
  TaskExcuteRecordMap,
} from '@/bizComponents/WorkRecord/types';
import { SzConfirm, SzTooltip, SzEmpty } from 'main/SzComponents';

import styles from './index.less';

const { Panel } = Collapse;

interface Props {
  detailInfo: ProcessListSingleBackType | null;
}

// 任务列表列表-左侧状态（标题）
export const StepIconAndColor = (status: TaskExcuteRecordEnum) => {
  const newStatus = status || TaskExcuteRecordEnum.RUNNABLE;
  return (
    <div
      className={`${styles['status-content']} ${styles['status' + newStatus]}`}
      style={{
        color: ActiveStatusColorMap[newStatus],
      }}
    >
      <IconFont type={ActiveStatusIconMap[newStatus]} />
      <span className={styles['des']}>{TaskExcuteRecordMap[newStatus]}</span>
    </div>
  );
};

const ProcessDetailRecordList = (props: Props) => {
  const { detailInfo } = props;

  // 任务列表数据信息
  const [workRecordList, setWorkRecordList] = useState<
    WorkRecordListBackType[]
  >([]);
  const [workRecordDataLoading, setWorkRecordDataLoading] =
    useState<boolean>(false);
  const [total, setTotal] = useState<number>(PaginationInitData.total);
  const [pagePaginationInfo, setPagePaginationInfo] =
    useState<PaginationParamsType>({
      ...PaginationInitData,
    });

  // 右侧弹窗
  const [curWorkItem, setCurWorkItem] =
    useState<WorkRecordListBackType | null>();
  const [videoVisible, setVideoVisible] = useState<boolean>(false);
  const [logVisible, setLogVisible] = useState<boolean>(false);
  const [inputParamsVisible, setInputParamsVisible] = useState<boolean>(false);

  // 获取任务列表信息
  const queryWorkRecordList = useCallback(
    async (params?: GetWorkExcuteListParamsType) => {
      if (!detailInfo?.processUuid) return;
      setWorkRecordDataLoading(true);
      await getWorkRecordList({
        processUuid: detailInfo.processUuid!,
        ...params,
      })
        .then(
          ({ bizData }: { bizData: ListBackType<WorkRecordListBackType> }) => {
            if (!!bizData) {
              const list = bizData?.records || [];
              list.map((item: any) => {
                item.workExecuteDetails = [
                  { ...item, processName: detailInfo.processName },
                ];
                return item;
              });

              setWorkRecordList(list);
              setPagePaginationInfo({
                ...params,
                pageNo: Number(bizData?.current),
                pageSize: Number(bizData?.size),
              });
              setTotal(Number(bizData?.total));
            }
          },
        )
        .finally(() => {
          setWorkRecordDataLoading(false);
        });
    },
    [detailInfo],
  );

  useEffect(() => {
    queryWorkRecordList();
  }, [queryWorkRecordList]);

  // 渲染视频icon
  const renderVideoIcon = (item: WorkRecordListBackType) => {
    const { hasRecord, hasClearVideo } = item;
    let toolTitle = '';
    if (!hasRecord && hasClearVideo) {
      toolTitle = '视频已清除';
    } else if (!hasRecord && !hasClearVideo) {
      toolTitle = '未上传视频';
    } else {
      toolTitle = '查看视频';
    }
    return (
      <SzTooltip title={toolTitle}>
        <IconFont
          type="iconlupingbofang"
          className={`${styles['view-icon']} ${styles['icon-video']}  ${
            !item?.hasRecord ? styles['disabled'] : styles['normal']
          }`}
          onClick={() => {
            setCurWorkItem(item);
            if (!!item?.hasRecord) {
              setVideoVisible(true);
            }
          }}
          disabled={!item?.hasRecord}
        />
      </SzTooltip>
    );
  };

  // 渲染日志icon
  const renderLogIcon = (item: WorkRecordListBackType) => {
    const { hasLog, hasClearLog } = item;
    let toolTitle = '';
    if (!hasLog && hasClearLog) {
      toolTitle = '日志已清除';
    } else if (!hasLog && !hasClearLog) {
      toolTitle = '未上传日志';
    } else {
      toolTitle = '查看日志';
    }
    return (
      <SzTooltip title={toolTitle}>
        <IconFont
          type="iconrizhichakan"
          className={`${styles['view-icon']} ${styles['icon-log']} ${
            !item?.hasLog ? styles['disabled'] : styles['normal']
          }`}
          onClick={() => {
            setCurWorkItem(item);
            if (!!item?.hasLog) {
              setLogVisible(true);
            }
          }}
          disabled={!item?.hasLog}
        />
      </SzTooltip>
    );
  };

  // 渲染引用参数icon
  const renderInputParamsIcon = (item: WorkRecordListBackType) => (
    <IconFont
      title="引用参数"
      type="iconchakancanshu"
      className={`${styles['view-icon']} ${styles['icon-inputparam']}  ${
        !item?.hasReference ? styles['disabled'] : styles['normal']
      }`}
      onClick={() => {
        setCurWorkItem(item);
        if (!!item?.hasReference) {
          setInputParamsVisible(true);
        }
      }}
      disabled={!item?.hasReference}
    />
  );

  // 关闭弹窗时候清空的数据
  const onCancle = () => {
    setCurWorkItem(null);
  };

  return (
    <div className={styles['tab-conetent-workrecord']}>
      <Spin spinning={workRecordDataLoading}>
        {!workRecordList?.length && (
          <div className={styles['tab-conetent-workrecord-empty']}>
            <SzEmpty />
          </div>
        )}
        <div className={styles['tab-conetent-workrecord-list']}>
          {workRecordList.map((item: WorkRecordListBackType) => {
            console.log(item);

            return (
              <Collapse
                key={item.workUuid}
                ghost
                className={styles['single-record']}
                expandIcon={({ isActive }) => (
                  <IconFont
                    type={isActive ? 'iconjian' : 'iconjia'}
                    className={styles['collapse-icon']}
                  />
                )}
              >
                <Panel
                  key={`panel${item.workUuid}`}
                  header={
                    <>
                      <div className={styles['panel-header']}>
                        <div className={styles['left-actions']}>
                          <Space size={5} className={styles.runInfo}>
                            <span className={styles['status']}>
                              {item?.status && StepIconAndColor(item.status)}
                            </span>
                            <span className={styles['time']}>
                              {TimeConversion(item?.runTime || 0)}
                            </span>
                          </Space>
                        </div>

                        <div
                          className={styles['right-actions']}
                          onClick={(event) => {
                            // 该部分点击不展开
                            event.stopPropagation();
                          }}
                        >
                          {item?.status !== TaskExcuteRecordEnum.RUNNABLE ? (
                            <Space size={8}>
                              {renderLogIcon(item)}
                              {renderVideoIcon(item)}
                              <SzTooltip
                                title={
                                  item.hasReference
                                    ? '引用参数'
                                    : '暂无引用参数'
                                }
                                placement={'left'}
                                getPopupContainer={(triggerNode: any) =>
                                  triggerNode.parentElement
                                }
                              >
                                {renderInputParamsIcon(item)}
                              </SzTooltip>
                            </Space>
                          ) : (
                            <SzTooltip title={'删除作业'}>
                              <IconFont
                                title="删除作业"
                                type="iconshanchu1"
                                className="delete-task"
                                onClick={() => {
                                  SzConfirm({
                                    title: '提示',
                                    content: '删除后该作业将不存在，确定删除？',
                                    className: 'confirm-delete',
                                    okText: '删除',
                                    onOk: async () => {
                                      await deleteRunnableWorkRecord({
                                        workUuid: item.workUuid,
                                      }).then(async ({ bizData }) => {
                                        if (!!bizData && bizData?.success) {
                                          await queryWorkRecordList();
                                        }
                                      });
                                    },
                                  });
                                }}
                              />
                            </SzTooltip>
                          )}
                        </div>
                      </div>
                      <div
                        className={styles['panel-subheader']}
                        onClick={(event) => {
                          // 该部分点击不展开
                          event.stopPropagation();
                        }}
                      >
                        <span
                          onClick={(e) => {
                            // 该部分点击不展开
                            e.stopPropagation();
                          }}
                        >
                          <span className="text-gray-400">{item.workUuid}</span>
                          <CopyIcon copyStr={item.workUuid} />
                        </span>
                        <div className={styles['endtime']}>{item.endTime}</div>
                        <div className={styles['faildes']}>
                          {item?.status === TaskExcuteRecordEnum.RUNFAILED && (
                            <>
                              <div>
                                失败类型：
                                {RenderText(item.failReason)}
                              </div>
                              <div>
                                失败原因：
                                {item.failDescription || '未知错误'}
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </>
                  }
                >
                  <div className={styles['panel-content']}>
                    <WorkRecordSteps
                      recordInfo={item}
                      panelContentBgColor={'#fff'}
                    />
                  </div>
                </Panel>
              </Collapse>
            );
          })}
        </div>
        {total > PaginationInitData.total && (
          <Pagination
            className={styles['tab-conetent-workrecord-pagination']}
            size="small"
            pageSize={pagePaginationInfo.pageSize}
            total={total}
            showTotal={(total) => `共 ${total} 条`}
            current={pagePaginationInfo.pageNo}
            onChange={(pageNumber?: number, pageSize?: number) => {
              queryWorkRecordList({ pageNo: pageNumber, pageSize: pageSize });
            }}
          />
        )}
      </Spin>
      {curWorkItem && (
        <>
          <WorkVideoModal
            visible={videoVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setVideoVisible(false);
            }}
          />
          <WorkLogModal
            visible={logVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setLogVisible(false);
            }}
          />
          <InputParamsModal
            visible={inputParamsVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setInputParamsVisible(false);
            }}
          />
        </>
      )}
    </div>
  );
};
export default ProcessDetailRecordList;

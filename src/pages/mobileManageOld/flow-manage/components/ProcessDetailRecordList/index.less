@import '~@/assets/styles/variable.less';

@work-record-line: 1px solid #dee6ee;

.tab-conetent-workrecord {
  &-empty {
    padding-top: 40px;
  }

  &-list {
    .panel-header {
      display: flex;
      padding-left: 5px;
      justify-content: space-between;
      align-items: center;

      .left-actions {
        .status {
          &-content {
            font-weight: 600;
            line-height: 18px;

            .des {
              margin-left: 3px;
            }
          }
        }

        .time {
          font-size: 12px;
          font-weight: 400;
          color: @text-color-secondary;
          line-height: 16px;
        }
      }

      .right-actions {
        .view-icon {
          font-size: 16px;

          &.normal:hover {
            color: #2e99fe;
          }

          &.disabled {
            opacity: 0.4;
            cursor: not-allowed;
          }
        }
      }
    }

    .panel-subheader {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      padding: 0 0 10px 14px;
      margin-left: -9px;
      border-left: @work-record-line;
      cursor: default;

      .endtime {
        color: @text-color-secondary;
      }

      .faildes {
        color: @text-color;
      }
    }

    .panel-content {
      background: #f9fafd;
      border-radius: 4px;
      padding: 10px;
      margin-top: -8px;
    }

    :global {
      .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content {
        margin-left: 7px;
        border-left: @work-record-line;
      }

      .ant-collapse > .ant-collapse-item > .ant-collapse-header {
        padding: 0 0 0 16px;
        line-height: 24px;
      }

      .ant-collapse
        > .ant-collapse-item
        > .ant-collapse-header
        .ant-collapse-arrow {
        margin: 0 -15px;
        padding: 0;
        font-size: 11px;
        border: @work-record-line;
      }

      .ant-collapse-ghost
        > .ant-collapse-item
        > .ant-collapse-content
        > .ant-collapse-content-box {
        padding-top: 5px;
      }
    }
  }

  &-pagination {
    margin-top: 20px;
    text-align: right;

    & > * {
      font-size: 12px;
    }
  }
}

import React from 'react';
import { Modal, Spin } from 'antd';

interface IProps {
  visible: boolean;
  videoURL: string;
  loading: boolean;
  onOk: () => void;
  onCancel: () => void;
}

const VideoModal: React.FC<IProps> = (props: IProps) => {
  const { visible, videoURL, loading, onOk, onCancel } = props;

  return (
    <Modal
      open={visible}
      title={'查看视频'}
      width={800}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <video
          src={videoURL}
          controls={true}
          width="750"
          height="500"
          controlsList="nodownload"
        ></video>
      </Spin>
    </Modal>
  );
};

export default VideoModal;

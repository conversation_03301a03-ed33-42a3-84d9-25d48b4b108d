import { UploadFileTypeEnum } from '@/bizComponents/UploadFiles/types';
import {
  Form,
  FormInstance,
  Input,
  InputNumber,
  Radio,
  RadioGroupProps,
  Select,
  SelectProps,
  Switch,
  TreeSelect,
} from 'antd';
import * as ServiceApi from '../../services';

import DraggerUpload from '@/bizComponents/UploadFiles/DraggerUpload';
import DepartmentSelect from '@deopCmd/bizComponents/DepartmentSelect';
import { BaseFormInstance } from '@deopCmd/utils/utils_pika';
import { RuleObject } from 'antd/lib/form';
import { IconFont } from 'main/MyIcon';
import { TipsContent } from 'main/TipsContent';
import type { UploadRequestOption } from 'rc-upload/lib/interface';
import { z } from 'zod';
import { UploadProcessBackType, UploadProcessParamsType } from '../../interface';

import { TagItem } from '@deopCmd/bizComponents/AddTagsModal/types';
import InfoCard from '@deopCmd/components/InfoCard';
import { ProcessChannelEnum } from '@deopCmd/pages/flowShare/type';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';
import { ProcessStateLabelEnum, ProcessStateLabelMap, RoiTypeEnum, RoiTypeInfoMap } from '../../type';
import SelectTags from '@deopCmd/components/FormSelectTagsItem';
import { omit } from 'lodash';
import { createSharedState } from '@ai-indeed/hooks';
import { GetProps } from 'main/types';

const { SHOW_ALL } = TreeSelect;

const RoiTypeOptions: SelectProps['options'] = Object.entries(RoiTypeInfoMap).map(([key, item]) => {
  return {
    label: item.text,
    value: Number(key),
  };
});
// 检查人工用时时间
const checkTime: RuleObject['validator'] = (_, value) => {
  const res = z.number().positive().safeParse(value);
  if (!res.success) return Promise.reject(new Error('请输入大于0的正整数'));
  return Promise.resolve();
};

// 检查人/机器用时比
const checkRate: RuleObject['validator'] = (_, value) => {
  if (!value || (value && !/^(?=.*[1-9])\d*(\.\d{1,2})?$/.test(value))) {
    return Promise.reject(new Error('请输入大于0的数值，至多保留2位小数'));
  }
  return Promise.resolve();
};

const processVersionLabelOptions: RadioGroupProps['options'] = Object.entries(
  omit(ProcessStateLabelMap, [ProcessStateLabelEnum.Offline]),
).map(([key, item]) => {
  return {
    label: item.label,
    value: item.key,
  };
});

export interface FormType {
  packageUuid: string;
  processDescription: string;
  departmentIds: string[];
  processName: string;
  processProfile: string;
  processVersionLabel: ProcessStateLabelEnum;
  tagList?: TagItem[];
  roiType?: RoiTypeEnum;
  processHumanMinute?: number;
  roiPercent?: number;
  isPublish?: boolean;
}

export type ProcessFormInstance = BaseFormInstance<FormType>;

const initialValue: Partial<FormType> = {
  processVersionLabel: ProcessStateLabelEnum.AlreadyOnline,
  roiType: RoiTypeEnum.BYESTIMATEHUMANTIME,
  processHumanMinute: 30,
  roiPercent: 1,
  isPublish: true,
};

interface Props {
  form?: ProcessFormInstance;
  initialValue?: Partial<FormType>;
  processDetail?: Awaited<ReturnType<typeof ServiceApi.getProcessDetail>>['bizData'];
}

export const useIsLoading = createSharedState(false);

// 添加和编辑是两个表单
const AddProcessForm: React.FC<Props> = (props) => {
  const [isLoading, setIsLoading] = useIsLoading();

  const [form] = Form.useForm(props.form as FormInstance) as [form: ProcessFormInstance];

  const { data: processInfo, runAsync: uploadProcess } = useRequest(
    async (params: UploadProcessParamsType) => {
      const { bizData } = await ServiceApi.uploadProcess({
        ...params,
        processChannel: ProcessChannelEnum.APPMANAGEMENT,
      });
      return bizData as UploadProcessBackType;
    },
    {
      manual: true,
      onBefore: () => {
        setIsLoading(true);
      },
      onFinally: () => {
        setIsLoading(false);
      },
    },
  );

  const infoCardItems = useMemo(() => {
    const items: GetProps<typeof InfoCard>['items'] = [];
    items.push(
      ...[
        {
          key: 1,
          label: '版本号',
          value: processInfo?.processVersion || '',
          valueProps: {
            span: 14,
          },
        },
        {
          key: 2,
          label: '开发者',
          value: processInfo?.packageUploader || '',
          valueProps: {
            span: 14,
          },
        },
      ],
    );
    return items;
  }, [props.processDetail, processInfo]);

  // 解析流程包
  const handleUploadSuccess = async (props: UploadProcessParamsType, options: UploadRequestOption) => {
    try {
      const data = await uploadProcess(props);

      if (data) {
        form.setFieldsValue({
          ...initialValue,
          processName: data.processName,
          processProfile: data.processDescription,
          packageUuid: data.packageUuid,
          // departmentIds: data.deptIds,
        });
      }

      options?.onSuccess?.({});
    } catch (error) {
      // 解析异常，需要重新传流程包或者显示错误
      options?.onError?.(error as any);
      // handleUploadFailed();
    }
  };

  return (
    <Form
      form={form as FormInstance}
      labelCol={{ span: 24 }}
      wrapperCol={{ span: 24 }}
      initialValues={props.initialValue || initialValue}>
      <Form.Item name="packageUuid" hidden rules={[{ required: true, message: '请上传流程包' }]}></Form.Item>
      <Form.Item<FormType>
        shouldUpdate={(prev, cur) => {
          return prev.packageUuid !== cur.packageUuid || !cur.packageUuid;
        }}>
        {() => {
          const errors = form.getFieldError('packageUuid');
          return (
            <div>
              <DraggerUpload
                uploadType={UploadFileTypeEnum.ZIP}
                maxCount={1}
                onUploadSuccess={handleUploadSuccess}
                onUploadFailed={() => {}}
                onRemove={() => {
                  form.setFieldValue(['packageUuid'], undefined);
                  form.validateFields(['packageUuid']);
                }}
              />
              {errors?.[0] && <span className="ant-form-item-explain-error">{errors[0]}</span>}
            </div>
          );
        }}
      </Form.Item>
      <Form.ErrorList></Form.ErrorList>
      <div className="relative">
        <InfoCard
          title={props.processDetail?.processName}
          titleAllowCopy={true}
          border
          wrapperClassName="p-4"
          items={infoCardItems}
        />
        {(processInfo?.packageUuid || props.processDetail?.processUuid) && (
          <div className="top-3 right-4 absolute text-[rgb(66,152,253)] text-6xl">
            <IconFont type={processInfo?.processIcon || 'iconqiantaiyingyong'} />
          </div>
        )}
      </div>

      <Form.Item<FormType>
        label="所属部门"
        name="departmentIds"
        rules={[{ required: true, message: '请选择部门' }]}
        normalize={(val: { label: string; value: string }[]) => {
          return val?.map((item) => item.value);
        }}>
        <DepartmentSelect
          // @ts-ignore
          value={[1]}
          allowClear
          treeNodeFilterProp="title"
          treeCheckable={true}
          showCheckedStrategy={SHOW_ALL}
          treeCheckStrictly={true}
          multiple
        />
      </Form.Item>

      <Form.Item
        label="上/下架"
        name="isPublish"
        valuePropName="checked"
        labelCol={{ span: 4 }}
        labelAlign="left"
        wrapperCol={{ span: 18 }}
        rules={[{ required: true }]}>
        <Switch />
      </Form.Item>
    </Form>
  );
};

export default AddProcessForm;

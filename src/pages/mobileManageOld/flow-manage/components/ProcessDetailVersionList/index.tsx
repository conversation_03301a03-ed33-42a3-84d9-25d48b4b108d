import { useState, useCallback, useEffect } from 'react';
import { Spin } from 'antd';
import {
  ProcessListSingleBackType,
  ProcessVersionDetailType,
} from '../../interface';
import { getProcessVersionList } from '../../services';
import SingleVersionDetailCard from './SingleVersionDetailCard';

import styles from './index.less';

interface Props {
  detailInfo: ProcessListSingleBackType | null;
  type: 1 | 2; //1-流程列表 2-流程共享
  onRefreshList: () => void;
  onClose: () => void;
  // 改变流程上/下架， 预估人工时间时候的处理
  onChangeProcessInfo: () => void;
}
const ProcessDetailVersionList = (props: Props) => {
  const { detailInfo, onRefreshList, onClose, onChangeProcessInfo } = props;
  const [versionDetailList, setVersionDetailList] = useState<
    ProcessVersionDetailType[]
  >([]);
  const [versionDetailListLoading, setVersionDetailListLoading] =
    useState<boolean>(false);

  // 查询版本列表
  const queryProcessVersionList = useCallback(async (processUuid: string) => {
    if (!processUuid) return;
    setVersionDetailListLoading(true);
    await getProcessVersionList({ processUuid })
      .then(({ bizData }) => {
        if (!!bizData) {
          setVersionDetailList(bizData);
        } else {
          setVersionDetailList([]);
        }
      })
      .finally(() => {
        setVersionDetailListLoading(false);
      });
  }, []);

  useEffect(() => {
    if (!!detailInfo?.processUuid) {
      queryProcessVersionList(detailInfo?.processUuid);
    }
  }, [detailInfo]);

  return (
    <div className={styles['version-lists']}>
      <Spin spinning={versionDetailListLoading}>
        {(versionDetailList || []).map((item) => {
          return (
            <SingleVersionDetailCard
              key={item.processDetailUuid}
              versionItem={item}
              onDeleteOneVersion={(processDetailUuid: string) => {
                const list = versionDetailList.filter(
                  (versionDetail) =>
                    versionDetail.processDetailUuid !== processDetailUuid,
                );
                setVersionDetailList(list);
                onRefreshList();
                if (!!detailInfo?.processUuid) {
                  if (!list.length) {
                    onClose();
                  } else {
                    onChangeProcessInfo();
                  }
                }
              }}
              onChangeProcessInfo={onChangeProcessInfo}
              onRefreshList={onRefreshList}
              onRefreshVersionList={(uuid: string) => {
                if (!uuid) return false;
                queryProcessVersionList(uuid);
              }}
              type={props.type}
            />
          );
        })}
      </Spin>
    </div>
  );
};

export default ProcessDetailVersionList;

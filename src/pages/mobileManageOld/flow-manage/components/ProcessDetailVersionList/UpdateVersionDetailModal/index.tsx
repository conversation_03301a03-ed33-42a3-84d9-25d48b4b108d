import React, { useEffect, useState } from 'react';
import { Modal, Radio, Form, Select, InputNumber } from 'antd';
import { RoiTypeEnum, RoiTypeInfoMap } from '@/types/application/type';
// @ts-ignore
import { renderTipsIcon } from '@/components/HelpTooltip/ToolTip.tsx';
import { SzSelect, GetPopupContainerLayerType } from 'main/SzComponents';

import styles from './index.less';

interface Props {
  visible: boolean;
  onClose: () => any;
  onOK: (params: any) => any;
  currentItem: any;
}

// 检查人工用时时间
const checkTime: (_: any, value: string) => Promise<Error | void> = (
  _,
  value,
) => {
  if (!value || (value && !/^\+?[1-9][0-9]*$/.test(value))) {
    return Promise.reject(new Error('请输入大于0的正整数'));
  }
  return Promise.resolve();
};

// 检查人/机器用时比
const checkRate: (_: any, value: string) => Promise<Error | void> = (
  _,
  value,
) => {
  if (!value || (value && !/^(?=.*[1-9])\d*(\.\d{1,2})?$/.test(value))) {
    return Promise.reject(new Error('请输入大于0的数值，至多保留2位小数'));
  }
  return Promise.resolve();
};

const EditTimeModal = (props: Props) => {
  const { visible, onClose, onOK, currentItem } = props;
  const [form] = Form.useForm();
  const [roiType, setRoiType] = useState<RoiTypeEnum>(
    RoiTypeEnum.BYESTIMATEHUMANTIME,
  );

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        processHumanMinute:
          currentItem?.processHumanMinute ||
          RoiTypeInfoMap[RoiTypeEnum.BYESTIMATEHUMANTIME].defaultValue,
        versionLabel: currentItem?.processVersionLabel,
        roiType: currentItem?.roiType,
        roiPercent:
          currentItem?.roiPercent ||
          RoiTypeInfoMap[RoiTypeEnum.BYACTUALRUNNINGTIME].defaultValue,
      });
      setRoiType(currentItem?.roiType || RoiTypeEnum.BYESTIMATEHUMANTIME);
    }
  }, [visible]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      const defaultParams = {
        versionLabel: values.versionLabel,
        roiType,
      };
      const params = { ...defaultParams, ...values };
      onOK(params);
    });
  };

  return (
    <Modal
      open={visible}
      title={'版本信息编辑'}
      onOk={handleOk}
      width={600}
      onCancel={() => {
        form.resetFields();
        onClose();
      }}
      destroyOnClose
    >
      <Form form={form} layout="vertical" className={styles['edit-form']}>
        <Form.Item name="versionLabel" label={'状态标签'}>
          <Radio.Group>
            <Radio value={30}>{'交付中'}</Radio>
            <Radio value={20}>{'试运行'}</Radio>
            <Radio value={10}>{'已上线'}</Radio>
            <Radio value={100}>{'已下线'}</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="roiType"
          label={'流程效益'}
          initialValue={roiType}
          // @ts-ignore
          tooltip={
            roiType === RoiTypeEnum.NOTCOUNTED
              ? undefined
              : renderTipsIcon(RoiTypeInfoMap?.[roiType]?.tip || '')
          }
        >
          <SzSelect
            popupContainerLayer={GetPopupContainerLayerType.PROGRIDCONTENT}
            onChange={(val: RoiTypeEnum) => {
              setRoiType(val);
            }}
          >
            {Object.entries(RoiTypeInfoMap).map(([key, item]: any) => {
              return (
                <Select.Option key={key} value={Number(key)}>
                  {item.text}
                </Select.Option>
              );
            })}
          </SzSelect>
        </Form.Item>
        {roiType === RoiTypeEnum.BYESTIMATEHUMANTIME && (
          <Form.Item
            name="processHumanMinute"
            label={'预估人工执行时间(分钟)'}
            rules={[{ required: true, validator: checkTime }]}
            initialValue={
              RoiTypeInfoMap[RoiTypeEnum.BYESTIMATEHUMANTIME].defaultValue
            }
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        )}
        {roiType === RoiTypeEnum.BYACTUALRUNNINGTIME && (
          <Form.Item
            name="roiPercent"
            label={'人/机器用时比'}
            rules={[{ required: true, validator: checkRate }]}
            initialValue={
              RoiTypeInfoMap[RoiTypeEnum.BYACTUALRUNNINGTIME].defaultValue
            }
            // @ts-ignore
            tooltip={renderTipsIcon(
              '人效比：人/机器用时比=人工操作所需时间÷机器人操作所需时间',
            )}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};
export default EditTimeModal;

@import '~@/assets/styles/variable.less';

.version-detail-card {
  position: relative;
  width: 100%;
  padding: 16px 20px;
  border: @border-line;
  border-radius: 4px;
  margin-bottom: 20px;

  .main-content {
    .main-content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      margin-bottom: 16px;

      .left {
        display: flex;
        flex: 1;
        align-items: center;

        .userName {
          max-width: 260px;
          color: @first-font-color;
          margin-left: 10px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: keep-all;
        }

        .version {
          color: @second-font-color;
        }
      }

      .right {
        display: flex;
        color: @third-font-color;
        font-size: 12px;
      }
    }

    p {
      margin: 6px 0 0;
      color: @second-font-color;
      display: flex;

      &.text-des {
        display: flex;

        span:nth-of-type(1) {
          flex-shrink: 0;
        }
      }

      &:last-of-type {
        margin-bottom: 10px;
      }

      .info-text {
        color: @first-font-color;

        .ant-badge-status-text {
          margin-left: 5px;
        }
      }

      .info-desc {
        /* stylelint-disable */
        display: -webkit-box;
        /* stylelint-enable */
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  .divider {
    margin: 16px 0;
    border-top: @border-line;
  }

  .action-btns {
    display: flex;
    justify-content: space-between;

    .btn-icon {
      font-size: 18px;
      color: #5c6f88;
    }
  }
}

import { useState, useMemo } from 'react';
import { Button, Space, Switch } from 'antd';
import { IconFont, CopyIcon } from 'main/MyIcon';
import { ProcessVersionDetailType, ProcessVersionFileType } from '../../../interface';
import * as Api from '../../../services';
import UpdateVersionDetailModal from '../UpdateVersionDetailModal';
import * as GlobalServiceApi from '@/services/global';
import { useSearchParams } from 'umi';
import { useFrameWorkContext } from '@/exposes/deopCmdEntry/context';
import { SzConfirm, SzTooltip } from 'main/SzComponents';
import { DefaultNoDataStr } from '@/constants';
import styles from './index.less';
import { RoiTypeInfoMap, RoiTypeEnum, ProcessSourceEnum, ProcessSourceMap } from '../../../type';
import { getSourceTag } from '@/utils/constants/constReactNode';

interface Props {
  versionItem: ProcessVersionDetailType;
  onDeleteOneVersion: (detailUuid: string) => void;
  onRefreshList: () => void;
  onRefreshVersionList: (uuid: string) => void;
  onChangeProcessInfo: () => void;
  type: 1 | 2; //1-流程列表 2-流程共享
}
const SingleVersionDetailCard = (props: Props) => {
  const frameWorkContextInfo = useFrameWorkContext();
  const [pageSearchParams] = useSearchParams();
  const pageCode = pageSearchParams.get('code');
  const { type = 2 } = props;
  const isFromFlowList = type === 1;
  const statusEnum = {
    up: isFromFlowList ? '已启用' : '已上架',
    down: isFromFlowList ? '已禁用' : '已下架',
  };
  const { versionItem, onDeleteOneVersion, onRefreshList, onRefreshVersionList, onChangeProcessInfo } = props;
  // 编辑预估人工时间
  const [updateHumanVisible, setUpdateHumanVisible] = useState<boolean>(false);
  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);

  /** 按钮权限 */
  const { pageTabsAndbtnsPremission } = frameWorkContextInfo;
  // const Auth = useMemo(() => {
  //   if (!pageCode) return {};
  //   const pageTabAndbuttonsPremission =
  //     pageTabsAndbtnsPremission?.[pageCode] || [];
  //   return {
  //     xiazailiuchengbao: pageTabAndbuttonsPremission.includes('pmdxzlib'), // 下载流程包
  //     shanchuliucheng: pageTabAndbuttonsPremission.includes('pmdlcsc'), // 删除流程
  //   };
  // }, [pageTabsAndbtnsPremission]);
  const Auth = useMemo(() => {
    return {
      xiazailiuchengbao: true, // 下载流程包
      shanchuliucheng: true, // 删除流程
    };
  }, [pageTabsAndbtnsPremission]);

  // 上架
  const onProcessPublish = (versionInfo: ProcessVersionDetailType) => {
    SzConfirm({
      title: isFromFlowList ? '是否确认启用' : '是否确认上架',
      content: isFromFlowList
        ? `即将启用${versionInfo.processVersion}版本。`
        : `即将上架${versionInfo.processVersion}版本。`,
      onOk: async () => {
        await Api.publishProcessVersion({
          processDetailUuid: versionInfo.processDetailUuid,
        }).then(async ({ bizData }: any) => {
          if (!!bizData && bizData?.success) {
            onRefreshList();
            onChangeProcessInfo();
            onRefreshVersionList(versionInfo.processUuid);
          }
        });
      },
    });
  };

  // 下架
  const offProcessPublish = (versionInfo: ProcessVersionDetailType) => {
    SzConfirm({
      title: isFromFlowList ? '是否确认禁用' : '是否确认下架？',
      content: isFromFlowList ? '禁用后流程版本不可被任务引用' : `下架后数字员工池将不显示该流程。`,
      onOk: async () => {
        await Api.offProcessVersion({
          processDetailUuid: versionInfo.processDetailUuid,
        }).then(async ({ bizData }: any) => {
          if (!!bizData && bizData?.success) {
            onRefreshList();
            onChangeProcessInfo();
            onRefreshVersionList(versionInfo.processUuid);
          }
        });
      },
    });
  };

  // 下载-查询fileId
  const onProcessDownLoad = async (versionInfo: ProcessVersionDetailType) => {
    setDownloadLoading(true);
    await Api.processVersionDetail({
      packageUuid: versionInfo.packageUuid,
      processChannel: versionInfo?.processChannel || 0,
    }).then(async ({ bizData }: { bizData: ProcessVersionFileType }) => {
      await GlobalServiceApi.coeFileDown(bizData.packagePath)
        .then((data: any) => {
          const blob = new Blob([data]);
          const objectURL = URL.createObjectURL(blob);
          let btn: any = document.createElement('a');
          btn.download = `${bizData.packageName}`;
          btn.href = objectURL;
          btn.click();
          URL.revokeObjectURL(objectURL);
          btn = null;
        })
        .finally(() => {
          setDownloadLoading(false);
        });
    });
  };

  const getSourceString = (sourceCode?: number) => {
    switch (sourceCode) {
      case ProcessSourceEnum.FACTORY:
        return ProcessSourceMap[ProcessSourceEnum.FACTORY];
      default:
        return DefaultNoDataStr;
    }
  };

  return (
    <div className={styles['version-detail-card']}>
      <div className={styles['main-content']}>
        <div className={styles['main-content-header']}>
          <div className={styles['left']}>
            <IconFont
              type={versionItem.processIcon || 'iconqiantaiyingyong'}
              style={{
                color: 'rgb(66, 152, 253)',
                fontSize: 40,
              }}
            />
            <SzTooltip title={versionItem.processCreatorRealName}>
              <div className={styles['userName']}>{versionItem.processCreatorRealName}</div>
            </SzTooltip>

            <div style={{ color: '#DEE6EE', padding: '0 5px' }}>|</div>
            <div className={styles['version']}>v{`${versionItem.processVersion}`}</div>
          </div>
          <div className={styles['right']}>{versionItem.processCreateTime} 上传</div>
        </div>
        <p>
          <span>版本ID：</span>
          <span className={styles['info-text']}>{versionItem.processDetailUuid}</span>
          {versionItem?.processDetailUuid && <CopyIcon copyStr={versionItem?.processDetailUuid} />}
        </p>
        <p>
          <span>流程效益：</span>
          <span className={styles['info-text']}>
            {versionItem?.roiType ? RoiTypeInfoMap?.[versionItem.roiType]?.text : DefaultNoDataStr}
          </span>
        </p>
        {versionItem?.roiType === RoiTypeEnum.BYESTIMATEHUMANTIME && (
          <p>
            <span>预估人工时间：</span>
            <span className="info-text">{versionItem?.processHumanMinute || 0}分钟</span>
          </p>
        )}
        {versionItem?.roiType === RoiTypeEnum.BYACTUALRUNNINGTIME && (
          <p>
            <span>人/机器用时比：</span>
            <span className="info-text">{versionItem?.roiPercent || 0}</span>
          </p>
        )}
        <p>
          <span>来源：</span>
          <span className="info-text">
            {/* {getSourceString(versionItem.source)} */}
            {ProcessSourceMap?.[versionItem.source as ProcessSourceEnum] || DefaultNoDataStr}
          </span>
        </p>
        <p>
          <span>状态标签：</span>
          <span className="info-text">{getSourceTag(versionItem.processVersionLabel)}</span>
        </p>
        <p>
          <span>设计器版本：</span>
          <span className="info-text">{versionItem.factoryVersion || DefaultNoDataStr}</span>
        </p>
        {/* <p>
          <span>预计机器人办理时间（分）：</span>
          <span className={styles['info-text']}>
            {versionItem.processRobotMinute}
          </span>
        </p>
        <p>
          <span>预计人工办理时间（分）：</span>
          <span className={styles['info-text']}>
            {versionItem.processHumanMinute}
          </span>
        </p>
        <p>
          <span>上传者：</span>
          <span className={styles['info-text']}>
            {versionItem.processCreatorRealName}
          </span>
        </p>
        <p>
          <span>上传时间：</span>
          <span className={styles['info-text']}>
            {versionItem.processCreateTime}
          </span>
        </p> */}
        <p className={styles['text-des']}>
          <span>简介：</span>
          <SzTooltip title={versionItem.processProfile} placement="topLeft">
            <span className={`${styles['info-text']} ${styles['info-desc']}}`}>{versionItem.processProfile}</span>
          </SzTooltip>
        </p>
      </div>
      <div className={styles['divider']}></div>
      <div className={styles['action-btns']}>
        <Space size={5}>
          <Switch
            unCheckedChildren="禁用"
            checkedChildren="启用"
            size="default"
            checked={!!versionItem.isPublish}
            onClick={async (checked) => {
              if (checked) {
                onProcessPublish(versionItem);
              } else {
                offProcessPublish(versionItem);
              }
            }}
          />
          <span>{!!versionItem.isPublish ? statusEnum.up : statusEnum.down}</span>
        </Space>

        <Space size={5}>
          {Auth.xiazailiuchengbao && (
            <SzTooltip title="下载">
              <Button
                type="text"
                onClick={() => onProcessDownLoad(versionItem)}
                loading={downloadLoading}
                icon={<IconFont type="iconxiazai" className={styles['btn-icon']} />}
                size="small"
              />
            </SzTooltip>
          )}

          {/* <SzTooltip title="编辑">
            <Button
              type="text"
              onClick={() => {
                setUpdateHumanVisible(true);
              }}
              icon={<IconFont type="iconbianji-xi" className={styles['btn-icon']} />}
              size="small"
            />
          </SzTooltip> */}

          <SzTooltip title="删除">
            <Button
              type="text"
              hidden={!Auth?.shanchuliucheng}
              onClick={() => {
                SzConfirm({
                  title: '是否确认删除？',
                  content: `删除后无法恢复此版本信息。`,
                  className: 'confirm-delete',
                  okText: '删除',
                  onOk: async () => {
                    await Api.deleteOneVerisonProcess({
                      processDetailUuid: versionItem.processDetailUuid,
                    }).then(async ({ bizData }: any) => {
                      if (!!bizData && bizData?.success) {
                        onDeleteOneVersion(versionItem.processDetailUuid);
                      }
                    });
                  },
                });
              }}
              icon={<IconFont type="iconshanchu" className={styles['btn-icon']} />}
              size="small"
            />
          </SzTooltip>
        </Space>
      </div>
      {/* 预估人工时间 */}
      <UpdateVersionDetailModal
        visible={updateHumanVisible}
        currentItem={versionItem}
        onClose={() => {
          setUpdateHumanVisible(false);
        }}
        onOK={async (params: any) => {
          await Api.updateProcessHumanMinute({
            ...params,
            processDetailUuid: versionItem.processDetailUuid,
          }).then(({ bizData }) => {
            if (!!bizData && bizData.success) {
              setUpdateHumanVisible(false);
              onChangeProcessInfo();
              onRefreshVersionList(versionItem.processUuid);
            }
          });
        }}
      />
    </div>
  );
};

export default SingleVersionDetailCard;

import React, { useEffect, useState } from 'react';
import { Modal, Form, TreeSelect, message } from 'antd';
import { SzTreeSelect } from 'main/SzComponents';
import { getDepartmentTrees } from '@/services/global';
import { changeShiChangBuMen } from '../../services';
interface Editprops {
  deptIds: any[];
  uuid: string;
  visible: boolean;
  onClose: () => void;
}

const EditDeptModal = (props: Editprops) => {
  const { visible, onClose, uuid, deptIds } = props;
  const [form] = Form.useForm();
  const [treeData, setTreeData] = useState([]);

  const { SHOW_ALL } = TreeSelect;
  const tProps = {
    treeData,
    treeCheckable: true,
    showCheckedStrategy: SHOW_ALL,
    treeDefaultExpandAll: true,
    treeCheckStrictly: true,
    placeholder: '请选择所属部门',
    style: {
      width: '100%',
    },
  };
  const loopUpdateTreeData = (list: any[]) => {
    return list.map((val: any) => {
      const obj = { ...val };
      obj.key = val.id;
      obj.value = val.id;
      obj.title = val.name;
      if (obj.children && obj.children?.length) {
        obj.children = loopUpdateTreeData(obj.children);
      }
      return obj;
    });
  };
  const getOrgDepartmentList = async () => {
    const { bizData } = await getDepartmentTrees();
    const treeDatas = loopUpdateTreeData([bizData]);
    setTreeData(treeDatas as any);
  };

  const handleOk = () => {
    form.validateFields().then((res) => {
      const { departmentIds } = res;
      changeShiChangBuMen({
        uuid: uuid,
        deptIds: (departmentIds || []).map((item: any) => item.value),
      }).then(() => {
        message.success('操作成功');
        onClose();
      });
    });
  };

  const handleCancel = () => {
    onClose();
  };

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue({ departmentIds: deptIds });
    getOrgDepartmentList();
  }, [visible]);

  return (
    <Modal
      title="所属部门编辑"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="departmentIds"
          label={'所属部门'}
          rules={[{ required: true }]}
        >
          <TreeSelect {...tProps} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditDeptModal;

import { useCallback, useEffect, useMemo, useState } from 'react';
import { Tabs, Modal, Tooltip, Space, Tag, Drawer } from 'antd';
import * as ServiceApi from '../../services';
import { DefaultNoDataStr } from '@/constants';
import { ProcessListSingleBackType } from '../../interface';
import { CopyIcon } from 'main/MyIcon';
import ProcessDetailVersionList from '../ProcessDetailVersionList';
import ProcessDetailRecordList from '../ProcessDetailRecordList';
import { SzDrawer } from 'main/SzComponents';
import VideoModal from '../VideoModal';
import { FormOutlined } from '@ant-design/icons';
import EditDeptModal from './editDeptModal';
import InfoCard from '@deopCmd/components/InfoCard';
import TagsGroup from '@deopCmd/components/TagsGroup';
import { isEmpty } from 'lodash';

interface Props {
  type: 1 | 2; //1-流程列表 2-流程共享
  detail?: ProcessListSingleBackType | null;
  visible: boolean;
  onClose: () => void;
  onRefreshList: () => void;
}

// 详情页tab枚举
export enum DetailTabsMenuEnum {
  // 流程版本信息
  PROCESSVERSIONTYPE = '1',
  // 任务列表
  WORKRECORD = '2',
}

const ProcessDetailDrawer = (props: Props) => {
  const { detail, visible = false, type, onClose, onRefreshList } = props;
  const [detailInfo, setDetailInfo] = useState<ProcessListSingleBackType | null>(null);
  const [curAciveTabKey, setCurActiveTabKey] = useState<DetailTabsMenuEnum>(DetailTabsMenuEnum.PROCESSVERSIONTYPE);

  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const [videoURL, setVideoURL] = useState('');
  const [videoLoading, setVideoLoading] = useState(false);
  const [showEditDeptModal, setShowEditDeptModal] = useState<boolean>(false);

  // 取消/关闭
  const handleCancel = useCallback(() => {
    setDetailInfo(null);
    onClose();
    setCurActiveTabKey(DetailTabsMenuEnum.PROCESSVERSIONTYPE);
  }, [onClose]);

  // 获取详情信息
  const queryDetailInfo = useCallback(async (processUuid: string) => {
    setPageLoading(true);
    await ServiceApi.getProcessDetail({ processUuid })
      .then(({ bizData }: { bizData: ProcessListSingleBackType }) => {
        if (!!bizData) {
          if (!!bizData && bizData?.processUuid === processUuid) {
            setDetailInfo(bizData);
          } else {
            setDetailInfo(null);
          }
        }
      })
      .finally(() => {
        setPageLoading(false);
      });
  }, []);

  useEffect(() => {
    if (visible && !!detail?.processUuid) {
      queryDetailInfo(detail.processUuid);
    }
  }, [visible, detail]);

  // 获取所属部门信息
  const getDepartmentNames = (deptInfos: ProcessListSingleBackType['deptInfos']) => {
    const deptNames: string[] = [];
    (deptInfos || []).map((deptItem: { deptId?: string; name?: string }) => {
      if (!!deptItem?.deptId && !!deptItem?.name) {
        deptNames.push(deptItem.name);
      }

      return deptItem;
    });

    return deptNames.join('，');
  };

  const openVideo = async (Uuid: string) => {
    setVideoVisible(true);
    setVideoLoading(true);
    const data = await ServiceApi.videoDownload(Uuid);
    setVideoLoading(false);
    const fileReader = new FileReader();
    fileReader.onload = (e: any) => {
      try {
        const obj = JSON.parse(e.target.result);
        Modal.error({ content: obj.msg });
      } catch (err) {
        const blob = new Blob([data]);
        const objectURL = URL.createObjectURL(blob);
        setVideoURL(objectURL);
      }
    };
    fileReader.readAsText(data);
  };

  // 基本信息模块展示
  const BasicContentElem = useMemo(() => {
    return (
      <>
        <InfoCard
          title="基本信息"
          backgroundColor="#f8f8f8"
          wrapperClassName="mb-4 py-[30px] px-[24px]"
          items={[
            {
              key: 1,
              label: '流程ID',
              value: detailInfo?.processUuid,
              allowCopy: true,
            },
            { key: 2, label: '流程名', value: detailInfo?.processName },
            {
              key: 3,
              label: '标签',
              value: <>{isEmpty(detailInfo?.tagList) ? '--' : <TagsGroup tagsData={detailInfo?.tagList} />}</>,
            },
            {
              key: 4,
              label: '所属部门',
              value: (
                <>{!!detailInfo?.deptInfos?.length ? getDepartmentNames(detailInfo?.deptInfos) : DefaultNoDataStr}</>
              ),
            },
            {
              key: 5,
              label: '当前版本号',
              value: detailInfo?.processUsedVersion,
              hide: type !== 2,
            },
            { key: 6, label: '创建时间', value: detailInfo?.modifyTime },
            {
              key: 7,
              label: '创建者',
              value: detailInfo?.processCreatorRealName,
              hide: type !== 2,
            },
          ]}
        />

        {/* 流程共享-流程详情显示视频 */}
        {/* {type === 2 && (
          <div className={styles['basic-info-item']}>
            <label>视频</label>
            {detailInfo?.videoUrl ? (
              <span
                style={{ cursor: 'pointer', color: '#2F9AFF' }}
                onClick={() => {
                  openVideo(detailInfo?.processUuid);
                }}
              >
                <IconFont type="iconshiyongshipin" style={{ marginRight: 3 }} />
                查看视频
              </span>
            ) : (
              <span>未上传视频</span>
            )}
          </div>
        )} */}
      </>
    );
  }, [detailInfo]);

  return (
    <Drawer
      className="[&_.ant-drawer-body]:!p-0"
      title="流程详情"
      open={visible}
      onClose={handleCancel}
      width={510}
      footer={null}
      maskClosable={true}
      destroyOnClose>
      {BasicContentElem}
      <Tabs
        className="px-[24px]"
        activeKey={curAciveTabKey}
        onChange={(val) => {
          setCurActiveTabKey(val as DetailTabsMenuEnum);
        }}
        items={[
          {
            label: '版本信息',
            key: DetailTabsMenuEnum.PROCESSVERSIONTYPE,
            children: (
              <ProcessDetailVersionList
                detailInfo={detailInfo}
                type={type}
                onRefreshList={() => {
                  onRefreshList();
                }}
                onChangeProcessInfo={() => {
                  // 版本改变状态，改变预估人工时间以后触发详情更新
                  if (!!detail?.processUuid) {
                    queryDetailInfo(detail.processUuid);
                  }
                }}
                onClose={handleCancel}
              />
            ),
          },
          // {
          //   label: '任务列表',
          //   key: DetailTabsMenuEnum.WORKRECORD,
          //   children: <ProcessDetailRecordList detailInfo={detailInfo} />,
          // },
        ]}
      />
      {videoVisible && (
        <VideoModal
          visible={videoVisible}
          videoURL={videoURL}
          loading={videoLoading}
          onOk={() => {
            setVideoVisible(false);
          }}
          onCancel={() => {
            setVideoVisible(false);
          }}
        />
      )}
      <EditDeptModal
        deptIds={detailInfo?.deptInfos?.map((item) => item.deptId) || []}
        uuid={detail?.processUuid || ''}
        visible={showEditDeptModal}
        onClose={() => {
          setShowEditDeptModal(false);
          if (detail && !!detail.processUuid) {
            queryDetailInfo(detail.processUuid);
          }
        }}></EditDeptModal>
    </Drawer>
  );
};
export default ProcessDetailDrawer;

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { Button, Drawer, Form, message, Space } from 'antd';
import { omit } from 'lodash';
import React from 'react';
import * as ServiceApi from './../services';
import AddProcessForm, {
  ProcessFormInstance,
  useIsLoading,
} from './AddProcessForm';
import { useRequest } from 'ahooks';

interface Props {
  processUuid: string;
  onClose?: () => void;
}
/** 新的添加流程弹窗 */
const AddProcessDrawer: React.FC<Props> = (props) => {
  const modal = useModal();
  const [form] = Form.useForm() as [form: ProcessFormInstance];
  const [isLoading, setIsLoading] = useIsLoading();
  const handleClose = () => {
    props.onClose?.();
    modal.hide();

    setTimeout(modal.remove, 600);
  };

  const { loading, run: addProcess } = useRequest(ServiceApi.addProcess, {
    manual: true,
    debounceLeading: true,
    debounceWait: 700,
    onSuccess() {
      message.success('新增/编辑成功');
      handleClose();
    },
    onError() {},
  });

  return (
    <Drawer
      width={650}
      open={modal.visible}
      title="上传流程"
      onClose={handleClose}
      footer={
        <Space>
          <Button onClick={handleClose}>取消</Button>
          <Button
            disabled={isLoading || loading}
            type="primary"
            onClick={async () => {
              const val = await form.validateFields();
              const tagIds = val?.tagList?.map((item) => item.id);

              const formatVal: Parameters<typeof ServiceApi.addProcess>['0'] = {
                ...omit(val, ['tagList']),
                tagIds,
              };
              addProcess(formatVal);
            }}
          >
            确定
          </Button>
        </Space>
      }
      destroyOnClose
    >
      <AddProcessForm form={form} />
    </Drawer>
  );
};

export default NiceModal.create(AddProcessDrawer);

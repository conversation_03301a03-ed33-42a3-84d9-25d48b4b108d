import { UploadFileTypeEnum } from '@/bizComponents/UploadFiles/types';
import { Form, FormInstance, Input, TreeSelect } from 'antd';
import * as ServiceApi from '../../services';

import DraggerUpload from '@/bizComponents/UploadFiles/DraggerUpload';
import DepartmentSelect from '@deopCmd/bizComponents/DepartmentSelect';
import { BaseFormInstance } from '@deopCmd/utils/utils_pika';
import { IconFont } from 'main/MyIcon';
import type { UploadRequestOption } from 'rc-upload/lib/interface';
import {
  UploadProcessBackType,
  UploadProcessParamsType,
} from '../../interface';

import { TagItem } from '@deopCmd/bizComponents/AddTagsModal/types';
import InfoCard from '@deopCmd/components/InfoCard';
import { ProcessChannelEnum } from '@deopCmd/pages/flowShare/type';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';
import { GetProps } from '../../../flowArrange/components/Flow/utils/common';
import { ProcessStateLabelEnum, RoiTypeEnum } from '../../type';
import SelectTags from '@deopCmd/components/FormSelectTagsItem';

const { SHOW_ALL } = TreeSelect;

export interface FormType {
  packageUuid: string;
  processDescription: string;
  departmentIds: string[];
  processName: string;
  processProfile: string;
  processVersionLabel: ProcessStateLabelEnum;
  tagList?: TagItem[];
  // roiType?: RoiTypeEnum;
  // processHumanMinute?: number;
  // roiPercent?: number;
  // isPublish?: boolean;
}

export type ProcessFormInstance = BaseFormInstance<FormType>;

const initialValue: Partial<FormType> = {
  processVersionLabel: ProcessStateLabelEnum.AlreadyOnline,
};

interface Props {
  form?: ProcessFormInstance;
  initialValue?: Partial<FormType>;
  processDetail?: Awaited<
    ReturnType<typeof ServiceApi.getProcessDetail>
  >['bizData'];
}

const EditProcessForm: React.FC<Props> = (props) => {
  const [form] = Form.useForm(props.form as FormInstance) as [
    form: ProcessFormInstance,
  ];

  // const packageUuid = useWatch('packageUuid', form as FormInstance);

  const {
    data: processInfo,
    runAsync: uploadProcess,
    loading,
  } = useRequest(
    async (params: UploadProcessParamsType) => {
      const { bizData } = await ServiceApi.uploadProcess({
        ...params,
        processChannel: ProcessChannelEnum.APPMANAGEMENT,
      });
      return bizData as UploadProcessBackType;
    },
    {
      manual: true,
    },
  );

  const infoCardItems = useMemo(() => {
    const items: GetProps<typeof InfoCard>['items'] = [];
    items.push(
      ...[
        {
          key: 4,
          label: '流程ID',
          value: props.processDetail?.processUuid || '',
          allowCopy: true,
          valueProps: {
            span: 14,
          },
        },
        {
          key: 1,
          label: '版本号',
          value: props.processDetail?.processUsedVersion || '',
          valueProps: {
            span: 14,
          },
        },
        {
          key: 2,
          label: '开发者',
          value: props.processDetail?.processCreatorRealName || '',
          valueProps: {
            span: 14,
          },
        },
        {
          key: 5,
          label: '更新时间',
          value: props.processDetail?.modifyTime || '',
          valueProps: {
            span: 14,
          },
        },
      ],
    );
    return items;
  }, [props.processDetail, processInfo]);

  // 解析流程包
  const handleUploadSuccess = async (
    props: UploadProcessParamsType,
    options: UploadRequestOption,
  ) => {
    try {
      const data = await uploadProcess(props);

      if (data) {
        form.setFieldsValue({
          ...initialValue,
          processName: data.processName,
          processProfile: data.processDescription,
          packageUuid: data.packageUuid,
          // departmentIds: data.deptIds,
        });
      }

      options?.onSuccess?.({});
    } catch (error) {
      // 解析异常，需要重新传流程包或者显示错误
      options?.onError?.(error as any);
      // handleUploadFailed();
    }
  };

  return (
    <Form
      form={form as FormInstance}
      labelCol={{ span: 24 }}
      wrapperCol={{ span: 24 }}
      initialValues={props.initialValue || initialValue}
    >
      <Form.Item name="packageUuid" hidden>
        <></>
      </Form.Item>
      <Form.Item hidden>
        <DraggerUpload
          uploadType={UploadFileTypeEnum.ZIP}
          maxCount={1}
          onUploadSuccess={handleUploadSuccess}
          onUploadFailed={() => {}}
        />
      </Form.Item>
      <div className="relative">
        <InfoCard
          title={props.processDetail?.processName || ''}
          titleAllowCopy={true}
          border
          wrapperClassName="p-4"
          items={infoCardItems}
        />
        {(processInfo?.packageUuid || props.processDetail?.processUuid) && (
          <div className="absolute right-4 top-3 text-[rgb(66,152,253)] text-6xl">
            <IconFont
              type={processInfo?.processIcon || 'iconqiantaiyingyong'}
            />
          </div>
        )}
      </div>

      <Form.Item
        name="processName"
        label="流程名称"
        rules={[{ required: true, message: '请输入流程包名称' }]}
      >
        <Input disabled placeholder="请输入流程包名称" />
      </Form.Item>
      <Form.Item
        name="processProfile"
        label="流程简介"
        rules={[{ required: true, message: '请输入流程简介' }]}
      >
        <Input.TextArea placeholder="请输入流程简介" rows={4} />
      </Form.Item>

      <Form.Item<FormType>
        label="所属部门"
        name="departmentIds"
        rules={[{ required: true, message: '请选择部门' }]}
        normalize={(val: { label: string; value: string }[]) => {
          return val?.map((item) => item.value);
        }}
      >
        <DepartmentSelect
          value={[1]}
          allowClear
          treeNodeFilterProp="title"
          treeCheckable={true}
          showCheckedStrategy={SHOW_ALL}
          treeCheckStrictly={true}
          multiple
        />
      </Form.Item>
      <Form.Item name="tagList" noStyle>
        <SelectTags />
      </Form.Item>
    </Form>
  );
};

export default EditProcessForm;

import { request } from 'umi';
import {
  ProcessListParamsType,
  UploadProcessParamsType,
  UpdateParamsType,
  DeleteProcessAllVersionParamsType,
  ChangeProcessPublishStateParamsType,
  GetProcessIntroduceDetailParamsType,
  UpdateProcessIntroduceParamsType,
  QueryProcessDetail,
  GetProcessVersionDetailParamsType,
  UpdateProcessParamsType,
  GetWorkExcuteListParamsType,
  EditParamsType,
} from './interface';
import { PaginationInitData } from '@/constants';
import { IRes } from '@deopCmd/types/common';
import { ApiResponseBackType } from '@deopCmd/interface';

// 解析流程接口超时时间， 1小时超时时间
const UploadTimeout = 60 * 60 * 60;

/** ****************************** 首页 ******************************* */
/** 获取流程列表接口 */
const getProcessList = async (params: ProcessListParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/page', {
    method: 'POST',
    data: {
      pageSize: PaginationInitData.pageSize,
      pageNo: PaginationInitData.pageNo,
      ...params,
    },
  });
};

/** 删除流程包所有版本 */
const deleteProcessAllVersion = async (params: DeleteProcessAllVersionParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/delete', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/**
 * 获取业务领域列表
 */
const getDomainList = async () => {
  return await request('/uc/uc/1.0.0/menu/domain/list', {
    method: 'POST',
  });
};

/** ****************************** 上传流程包 ******************************* */
/** 流程包上传 */
const uploadProcess = async (params: any) => {
  return await request('/opt/oldMobile/1.0.0/web/process/package-upload', {
    method: 'POST',
    data: {
      ...params,
    },
    timeout: UploadTimeout,
  });
};

/** 新增流程 */
const addProcess = async (params: UpdateParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/add', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** ****************************** 流程包详情 ******************************* */
/** 获取流程详情 */
const getProcessDetail = async (params: QueryProcessDetail) => {
  return await request<
    ApiResponseBackType<{
      processLatestVersion: string;
      processProfile: string;
      useExplain: any;
      processUsedVersion: string;
      processUsedUuid: string;
      hasIntroduction: number;
      isPublish: boolean;
      processDetailUuid: string;
      packageInnerId: string;
      processCreatorRealName: string;
      tagList: {
        color: string;
        id: string;
        tagName: string;
        status: number;
      }[];
      modifyTime: string;
      processHumanMinute: number;
      processRobotMinute: number;
      processName: string;
      createTime: string;
      deptInfos: {
        deptName: string;
        deptId: string;
        name: string;
      }[];
      processUuid: string;
      processLatestUuid: string;
    }>
  >('/opt/oldMobile/1.0.0/web/process/info', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 流程包版本上架 */
const publishProcessVersion = async (params: ChangeProcessPublishStateParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/detail-state-on', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 流程包下架 */
const offProcessVersion = async (params: ChangeProcessPublishStateParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/detail-state-off', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 删除流程包版本 */
const deleteOneVerisonProcess = async (params: ChangeProcessPublishStateParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/detail-delete', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 下载流程包， 用于获取流程详情 */
const processVersionDetail = async (params: GetProcessVersionDetailParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/package-detail', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 流程编辑 */
const updateProcessHumanMinute = async (params: UpdateProcessParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/detail-update', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

// 获取流程版本列表
const getProcessVersionList = async (params: QueryProcessDetail) => {
  return await request('/opt/oldMobile/1.0.0/web/process/detail-list', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

// 获取任务列表列表
const getWorkRecordList = async (params: GetWorkExcuteListParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/work-execute-page', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** ****************************** 使用说明 ******************************* */
/** 流程介绍详情 */
const getProcessIntroduceInfo = async (params: GetProcessIntroduceDetailParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/introduction-info-uuid', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 更新流程介绍详情 */
const updateProcessIntroduceInfo = async (params: UpdateProcessIntroduceParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/introduction-update', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** ****************************** 编辑流程 ******************************* */
const editProcess = async (params: EditParamsType) => {
  return await request('/opt/oldMobile/1.0.0/web/process/update', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};
// 删除待任务列表RUNNABLE
const deleteRunnableWorkRecord = async (params: any) => {
  return await request('/opt/oldMobile/1.0.0/web/work/delete-wait', {
    method: 'POST',
    data: {
      ...params,
    },
  });
};

/** 上架流程 */
const putOnProcess = async (params: { processUuid: string | undefined }) => {
  return await request('/opt/deop/1.0.0/web/process/main-state-on', {
    method: 'POST',
    data: params,
  });
};

/** 下架流程 */
const pullOffProcess = async (params: { processUuid: string | undefined }) => {
  return await request('/opt/deop/1.0.0/web/process/main-state-off', {
    method: 'POST',
    data: params,
  });
};

/** 视频下载 */
const videoDownload = async (UUid: any) => {
  return request('/opt/deop/1.0.0/web/application/data/video-download', {
    method: 'POST',
    responseType: 'blob',
    data: {
      applicationUUID: UUid,
    },
  });
};

/**
 *企业市场编辑部门
 */
const changeShiChangBuMen = async (params: any) => {
  return request(`/opt/deop/1.0.0/web/process/dept-update`, {
    method: 'post',
    data: params,
  });
};

export {
  putOnProcess,
  pullOffProcess,
  videoDownload,
  changeShiChangBuMen,
  getProcessList,
  uploadProcess,
  getDomainList,
  addProcess,
  deleteProcessAllVersion,
  getProcessDetail,
  publishProcessVersion,
  offProcessVersion,
  processVersionDetail,
  updateProcessHumanMinute,
  getProcessVersionList,
  getWorkRecordList,
  getProcessIntroduceInfo,
  updateProcessIntroduceInfo,
  deleteOneVerisonProcess,
  editProcess,
  deleteRunnableWorkRecord,
};

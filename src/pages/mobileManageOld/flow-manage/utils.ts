// 获取部门树上所有的id值
export const getAllDeptKeys = (data: any) => {
  let deptKeyValue: Record<string, string>[] = [];
  const traverse = (node: any) => {
    if (!!node?.levelKey) {
      deptKeyValue.push({
        key: node.key,
        value: node.value,
        label: node.title,
        levelKey: node.levelKey,
      });
    }

    if (!!node?.children && !!node.children?.length) {
      node.children.forEach((child: any) => {
        traverse(child);
      });
    }
  };

  data.forEach((item: any) => {
    traverse(item);
  });

  return deptKeyValue;
};

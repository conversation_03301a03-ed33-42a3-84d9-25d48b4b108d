import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useRequest } from 'ahooks';
import { Drawer } from 'antd';
import React, { FC } from 'react';
import { getBotDetail } from './services';
import InfoCard from '@deopCmd/components/InfoCard';

const DetailDrawer: FC<{
  mobileBotId: string;
}> = ({ mobileBotId }) => {
  const modal = useModal();
  const { data: response } = useRequest(() => getBotDetail({ mobileBotId }), {
    // ready: !!mobileBotId,
  });

  const bizData = response?.bizData;

  return (
    <Drawer open={modal.visible} title="机器人详情" onClose={modal.hide} width={530}>
      <InfoCard
        title="基本信息"
        items={[
          // 用户名、当前设备、操作系统、设备IP，所属部门
          {
            key: 'userName',
            label: '用户名',
            value: bizData?.userName,
          },
          {
            key: 'machineName',
            label: '当前设备',
            value: bizData?.machineName,
          },
          {
            key: 'machineOperateSystem',
            label: '操作系统',
            value: bizData?.machineOperateSystem,
          },
          {
            key: 'machineIp',
            label: '设备IP',
            value: bizData?.machineIp,
          },
          {
            key: 'departmentName',
            label: '所属部门',
            value: bizData?.departmentName,
          },
        ]}
      />
    </Drawer>
  );
};

export default NiceModal.create(DetailDrawer);

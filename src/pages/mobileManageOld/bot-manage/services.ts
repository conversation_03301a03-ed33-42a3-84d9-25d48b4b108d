import { IList, IPageParams, IRes } from 'main/types';
import { request } from 'umi';

export const getBotList = async (
  params: IPageParams<{ userName: string; status: string }>,
): Promise<IRes<IList<any>>> => {
  console.log('🚀 -> params:', params);
  const mockData: IList<any> = {
    total: 3,
    pages: 1,
    size: 20,
    current: 1,
    records: [
      {
        mobileBotId: '1892476103103942657',
        userId: '162453',
        userName: 'rock',
        machineName: null,
        machineOperateSystem: null,
        machineIp: null,
        machineBotVersion: null,
        lastedLoginTime: null,
        status: 3,
        realName: 'rock',
      },
      {
        mobileBotId: '1815199625529073666',
        userId: '74117',
        userName: 'nuoya',
        machineName: null,
        machineOperateSystem: null,
        machineIp: null,
        machineBotVersion: null,
        lastedLoginTime: null,
        status: 3,
        realName: '诺娅1',
      },
      {
        mobileBotId: '1809160477395460097',
        userId: '4156',
        userName: '15757124737',
        machineName: null,
        machineOperateSystem: null,
        machineIp: null,
        machineBotVersion: null,
        lastedLoginTime: null,
        status: 3,
        realName: '佩恩',
      },
    ],
  };
  return request('/bot', {
    method: 'get',
    params,
  });
  return {
    bizData: mockData,
    code: '0',
    msg: 'success',
    subCode: '0',
    subMsg: 'success',
  };
};

export const getBotDetail = async (params: {
  mobileBotId: string;
}): Promise<
  IRes<{
    mobileBotId: string;
    machineName: string | null;
    machineOperateSystem: string | null;
    machineIp: string | null;
    userId: string;
    userName: string;
    departmentId: string;
    departmentName: string;
    machineBotVersion: string | null;
  }>
> => {
  return request(`/bot/${params.mobileBotId}`, {
    method: 'get',
  });
  return {
    bizData: {
      mobileBotId: '1892476103103942657',
      machineName: null,
      machineOperateSystem: null,
      machineIp: null,
      userId: '162453',
      userName: 'rock',
      departmentId: '91',
      departmentName: '个人',
      machineBotVersion: null,
    },
    code: '0',
    msg: 'success',
    subCode: '0',
    subMsg: 'success',
  };
};

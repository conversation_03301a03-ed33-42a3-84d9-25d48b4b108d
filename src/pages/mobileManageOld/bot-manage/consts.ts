export enum BotStatusEnum {
  All = '', // 全部
  Connecting = '-1', // 连接中
  Free = '1', // 空闲中
  Running = '2', // 运行中
  Offline = '3', // 离线
  StandAlone = '4', // 单机中
}

export const botStatusList = [
  {
    value: BotStatusEnum.All,
    text: '全部',
  },
  {
    value: BotStatusEnum.Running,
    text: '运行中',
  },
  {
    value: BotStatusEnum.Connecting,
    text: '连接中',
  },
  {
    value: BotStatusEnum.Free,
    text: '空闲中',
  },
  {
    value: BotStatusEnum.Offline,
    text: '离线',
  },
  {
    value: BotStatusEnum.StandAlone,
    text: '单机中',
  },
];

export const BotStatusMap = {
  [BotStatusEnum.All]: {
    value: '全部',
    color: '',
    background: '',
  },
  [BotStatusEnum.Connecting]: {
    value: '连接中',
    tooltips: '机器人连接中',
    color: '#FFB937',
    background: 'rgba(255, 185, 55, 0.06)',
  },
  [BotStatusEnum.Free]: {
    value: '空闲中',
    color: '#2F9AFF',
    background: 'rgba(47, 154, 255, 0.06)',
  },
  [BotStatusEnum.Running]: {
    value: '运行中',
    color: '#2FC37C',
    background: 'rgba(47, 195, 124, 0.06)',
  },
  [BotStatusEnum.Offline]: {
    value: '离线',
    tooltips: '机器人离线',
    color: '#8C9AAB',
    background: 'rgba(92, 111, 136, 0.06)',
  },
  [BotStatusEnum.StandAlone]: {
    value: '单机中',
    color: '#5c6f88',
    background: 'rgba(92,111,136,0.10)',
  },
};

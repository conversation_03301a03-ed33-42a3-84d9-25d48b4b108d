import { NO_DATA_IN_TABLE_COLUMN_DISPLY } from '@/utils/constants/const';
import useStatus from '@deopCmd/hooks/useStatus';
import { useAntdTable } from 'ahooks';
import { Badge, Form, FormInstance, Select, Tooltip, Typography } from 'antd';
import { Table } from '@/components/ui/Table';
import type { FC } from 'react';
import { BotStatusEnum, BotStatusMap } from './consts';
import * as botAPI from './services';
import { PageContainer } from 'main/PageContainer';
import { PageListContent } from 'main/PageListContent';
import NiceModal from '@ebay/nice-modal-react';
import ComboxSelectSearch from '@/components/ui/combox-select/combox-select-search';
import { GetProps } from 'main/types';
import DetailDrawer from './detail-drawer';
const selectOptions: GetProps<typeof Select>['options'] = [
  { value: BotStatusEnum.All, label: '全部' },
  { value: BotStatusEnum.Connecting, label: '连接中' },
  { value: BotStatusEnum.Free, label: '空闲中' },
  { value: BotStatusEnum.Running, label: '运行中' },
  { value: BotStatusEnum.Offline, label: '离线' },
  // { value: BotStatusEnum.StandAlone, label: '单机中' },
];

const columns = [
  {
    title: '登录用户',
    dataIndex: 'userName',
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '用户姓名',
    dataIndex: 'realName',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '当前设备',
    dataIndex: 'machineName',
    width: '14%',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '操作系统',
    dataIndex: 'machineOperateSystem',
    width: '12%',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: 'IP地址',
    dataIndex: 'machineIp',
    width: '12%',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '最近使用版本',
    dataIndex: 'machineBotVersion',
    width: '10%',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '登录时间',
    dataIndex: 'lastedLoginTime',
    key: 'lastedLoginTime',
    width: '15%',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '10%',
    ellipsis: {
      showTitle: false,
    },

    render: (text: any) => {
      // @ts-ignore
      return <Badge color={BotStatusMap[text].color} text={BotStatusMap[text as any].value} />;
    },
  },
  {
    title: '操作',
    dataIndex: 'ypmc',
    key: 'ypmc',
    fixed: 'right',
    width: '10%',
    render: (text: any, record: any) => (
      <>
        <Typography.Link
          onClick={() => {
            NiceModal.show(DetailDrawer, {
              mobileBotId: record.mobileBotId,
            });
          }}>
          详情
        </Typography.Link>
      </>
    ),
  },
];

const MobileBot: FC = () => {
  const [form] = Form.useForm<{ realName: string; userName: string }>();
  const { tableProps, search } = useAntdTable(
    async (p, formValue) => {
      const { SHOULD_SPREAD, ...rest } = formValue;

      const res = await botAPI.getBotList({ ...SHOULD_SPREAD, ...rest });
      return {
        list: res.bizData.records,
        total: res.bizData.total,
      };
    },
    {
      defaultPageSize: 10,
      defaultCurrent: 1,
      form,
    },
  );

  return (
    <>
      <NiceModal.Provider>
        <PageContainer>
          <PageListContent>
            <div className="space-y-4">
              <SeachHeader form={form} onSubmit={search.submit} />
              <Table
                {...tableProps}
                scroll={window.innerWidth < 1300 ? { x: 1300 } : {}}
                rowKey={(r) => r.uuid}
                columns={columns as any}
              />
            </div>
          </PageListContent>
        </PageContainer>
      </NiceModal.Provider>
    </>
  );
};

const SeachHeader: FC<{ form: FormInstance; onSubmit: () => void }> = ({ form, onSubmit }) => {
  return (
    <Form form={form} initialValues={{ status: BotStatusEnum.All, SHOULD_SPREAD: ['userName', ''] }}>
      <div className="flex gap-4">
        {/* 应该展开 */}
        <Form.Item noStyle shouldUpdate={() => true}>
          {({ setFieldValue }) => {
            return (
              <>
                <ComboxSelectSearch
                  onSearch={(val) => {
                    setFieldValue('SHOULD_SPREAD', { [val[0]]: val[1] });
                    onSubmit();
                  }}
                  defaultValue={['userName', '']}
                  options={[
                    {
                      value: 'userName',
                      label: '登录用户',
                      child: { placeholder: '请输入登录用户' },
                    },
                    {
                      value: 'realName',
                      label: '用户姓名',
                      child: { placeholder: '请输入用户姓名' },
                    },
                  ]}
                />
                <Form.Item noStyle name={['SHOULD_SPREAD']} />
              </>
            );
          }}
        </Form.Item>
        <Form.Item noStyle name={['status']}>
          <Select onChange={onSubmit} options={selectOptions} className="min-w-[100px]" />
        </Form.Item>
      </div>
    </Form>
  );
};

export default MobileBot;

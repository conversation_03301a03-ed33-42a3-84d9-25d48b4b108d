import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { StepNumberEnum } from '@/types/taskcenter/type';
import type { TaskUpdateDetailType } from '@/types/taskcenter/interface';

type ReducerFunction = () => void;
interface State {
  /** 用来记录创建任务(组)的步骤 */
  stepNumber?: StepNumberEnum;
  /** 用来记录每一个步骤将要发送到后台的数据 */
  stepData?: Record<string, unknown>;
  /** 编辑任务详情 */
  taskDetailData?: TaskUpdateDetailType;
  /** 触发父组件列表状态更新，其实就是一个随机数 */
  taskListReload?: number;
  appList?: Array<any>;
  appVersionList?: Array<any>;
  appVersionsInApp?: Record<string, unknown>;
  appDetail?: any;
}
const initStepState = {
  stepNumber: StepNumberEnum.ONE,
  stepData: {},
};
const initAppState = {
  appList: [],
  appVersionList: [],
  appDetail: {},
};
const initTaskDetailData = {
  taskDetailData: {} as TaskUpdateDetailType,
};
const initState = {
  selectAppInfo: { appId: null, appVersionId: null },
  ...initStepState,
  ...initAppState,
  ...initTaskDetailData,
};

interface Actions {
  resetStepInfo: ReducerFunction;
}

export const useTaskPlanAddStore = create<State & Actions>()(
  devtools(
    immer<State & Actions>(() => ({
      ...initState,
      resetStepInfo() {},
    })),
    {
      name: 'useTaskPlanAddStore',
    },
  ),
);

import { Button, Drawer, Form, Input, Space, Spin, Tabs } from 'antd';
import { useState } from 'react';
import TaskManageServiceApi from '../../../services';

import { TaskPlanTypeEnum } from '@deopCmd/types/taskcenter/type';
import { useRequest } from 'ahooks';
import { FC } from 'preact/compat';
import ApplicationForm from '../ApplicationSelectForm';
import styles from './index.less';
import DepartmentSelect from '@deopCmd/bizComponents/DepartmentSelect';
import { SzTreeSelect } from 'main/SzComponents';

const { TabPane } = Tabs;

enum TabTypeEnum {
  STEP1 = '1',
  STEP2 = '2',
  STEP3 = '3',
}

type EditStepProps = {
  /** 是否是任务组， 不传则默认为false */
  isGroup?: boolean;
  jobUuid: string | null;
  visible: boolean;
  /** 关闭编辑弹窗 */
  onClose: () => void;
};
// TaskApi.getMobileTaskmanageDetail
const EditTaskEntry = (props: EditStepProps) => {
  const { isGroup = false, visible = false, jobUuid = null, onClose } = props;
  const [activeTab, setActiveTab] = useState<string>(TabTypeEnum.STEP1);
  const [step1DataHandle, setStep1DataHandle] = useState<any>();
  const [step2DataHandle, setStep2DataHandle] = useState<any>();
  const [step3DataHandle, setStep3DataHandle] = useState<any>();
  const [submitBtnLoading, setSubmitBtnLoading] = useState<boolean>(false);

  const { data, loading } = useRequest(() => TaskManageServiceApi.getTaskmanageDetail(jobUuid!), {
    ready: Boolean(visible && jobUuid),
  });
  const taskDetailData = data?.processDetails[0];

  const onSubmit = () => {};

  return (
    <Drawer
      className={`${styles['update-task']} ${styles['task-edit-drawer']}`}
      title={isGroup ? '编辑任务组' : '编辑任务'}
      width={`calc(100vw - 208px)`}
      visible={visible}
      destroyOnClose
      footer={
        <div className={styles['commit-btn-layer']}>
          <Space>
            <Button shizai-key="update-cancel" onClick={() => {}}>
              取消
            </Button>
            <Button shizai-key="update-submit" type="primary" onClick={onSubmit} loading={submitBtnLoading}>
              提交
            </Button>
          </Space>
        </div>
      }>
      <div className={styles['edit-con']}>
        <div className={styles['task-edit-card']}>
          <div className={styles.content}>
            <Spin spinning={loading}>
              <Tabs activeKey={activeTab}>
                <TabPane tab="流程信息" key={TabTypeEnum.STEP1}>
                  <Step1 initialValues={{}} />
                </TabPane>
              </Tabs>
            </Spin>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

const Step1: FC<{ initialValues: any }> = ({ initialValues }) => {
  const [form] = Form.useForm();
  return (
    <div className="max-w-[300px] mx-auto">
      <Form form={form} initialValues={initialValues}>
        <Form.Item name="jobName" label="任务名称" rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Form.Item label="所属部门" name="departmentId" rules={[{ required: true }]}>
          <DepartmentSelect />
        </Form.Item>
      </Form>
    </div>
  );
};

export default EditTaskEntry;

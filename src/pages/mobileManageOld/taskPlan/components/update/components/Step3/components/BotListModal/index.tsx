/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useEffect, useRef } from 'react';
import { Table, Space, Input, Modal, Select } from 'antd';
import { getBotList } from '../../../../../../../services';
import { NO_DATA_IN_TABLE_COLUMN_DISPLY } from '@/utils/constants/const';
import { BotStatusEnum } from '@/types/type';
import { taskBotStatusList } from '@/types/taskcenter/type';
import type { TaskStatusEnum } from '@/types/taskcenter/type';
import { cloneDeep } from 'lodash';
import { statusNode } from '@/hooks';
import { SzSelect } from 'main/SzComponents';

const { Option } = Select;
const { Search } = Input;

interface BotInfoType {
  lastedLoginTime: string;
  machineBotVersion?: string;
  machineIp?: string;
  machineName?: string;
  machineOperateSystem?: string;
  status?: TaskStatusEnum;
  priority?: number;
  userId: string;
  userName: string;
  realName: string;
  uuid: string;
}

interface Props {
  visible: boolean;
  /** 本地已经选择了的机器人Id数组 */
  hadSelectedBotIds?: string[];
  /** 点击确定时候，通知父元素新增的数据 */
  okCb: (botInfo: any) => any;
  /** 点击取消/关闭时候的回调 */
  onClose: () => void;
}

interface SearchFormType {
  userName?: string;
  status?: BotStatusEnum;
  realName?: string;
}
interface PaginationType {
  pageNo?: number;
  pageSize?: number;
  total?: number;
}
const initPaginationInfo: PaginationType = {
  pageNo: 1,
  pageSize: 10,
};

const BotListModal = (props: Props) => {
  const { visible = false, hadSelectedBotIds = [], okCb, onClose } = props;

  /** 搜索框真实可以使用的搜索词 */
  const SearchkeywordsSearchMap = useRef<any>({
    userName: '用户名',
    realName: '用户姓名',
  });
  /** 当前选中的搜索字段名称 */
  const [curSearchName, setCurSearchName] = useState<string>('userName');
  /** 搜索条件-获取机器人列表的搜索条件 */
  const [searchForm, setSearchForm] = useState<SearchFormType>({});
  /** 分配方式为指定bot时候选中的bot信息 */
  const [selectedBotsData, setSelectedBotsData] = useState<any[]>([]);
  /** bot列表 */
  const [botList, setBotList] = useState<BotInfoType[]>([]);
  /** 加载bot列表 */
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  /** 页码相关的数据 */
  const [paginationInfo, setPaginationInfo] = useState<any>({
    ...initPaginationInfo,
  });

  /** 查询bot列表 */
  const fetchBotList = (
    params?: SearchFormType,
    pagination?: PaginationType,
  ) => {
    const newSearchForm = { ...searchForm, ...(params || {}) };
    setTableLoading(true);
    setSearchForm({ ...newSearchForm });
    getBotList({
      notInBotUuid: hadSelectedBotIds.join(','),
      ...newSearchForm,
      ...(pagination || {}),
    })
      .then(
        (res: {
          records: BotInfoType[];
          current: string;
          size: string;
          total: string;
        }) => {
          setPaginationInfo({
            pageNo: Number(res.current),
            pageSize: Number(res.size),
            total: Number(res.total),
          });
          setBotList(res?.records || []);
        },
      )
      .finally(() => {
        setTableLoading(false);
      });
  };

  // 搜索框的选择项
  const selectBefore = (
    <SzSelect
      value={curSearchName}
      onSelect={(value: string) => {
        setCurSearchName(value);
      }}
    >
      {Object.entries(SearchkeywordsSearchMap.current).map(
        ([key, val]: any) => {
          return (
            <Option key={key} value={key}>
              {val}
            </Option>
          );
        },
      )}
    </SzSelect>
  );

  /** 顶部搜索数据变更接口, 除日期接口以外的 */
  const searchBarUpdate = (key: string, val?: any) => {
    const newSearchForm = cloneDeep(searchForm);

    // 重置搜索输入框未选中的字段
    if (key === curSearchName) {
      Object.keys(SearchkeywordsSearchMap.current).forEach((name: string) => {
        if (name !== key && newSearchForm?.[name as keyof SearchFormType]) {
          newSearchForm[name as keyof SearchFormType] = undefined;
        }
      });
    }
    fetchBotList(
      {
        ...newSearchForm,
        [key]: val,
      },
      {
        pageNo: 1,
        pageSize: 10,
      },
    );
  };

  /** 机器人列表 */
  const columns = [
    {
      title: '用户名',
      dataIndex: 'userName',
      key: 'userName',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '用户姓名',
      dataIndex: 'realName',
      key: 'realName',
      width: '15%',
      ellipsis: true,
      render: (text: string) => text || NO_DATA_IN_TABLE_COLUMN_DISPLY,
    },
    {
      title: '当前设备',
      dataIndex: 'machineName',
      key: 'machineName',
      width: '15%',
      ellipsis: true,
      render: (text: any) => {
        return text || NO_DATA_IN_TABLE_COLUMN_DISPLY;
      },
    },
    {
      title: '操作系统',
      dataIndex: 'machineOperateSystem',
      key: 'machineOperateSystem',
      width: '15%',
      ellipsis: true,
      render: (text: any) => {
        return text || NO_DATA_IN_TABLE_COLUMN_DISPLY;
      },
    },
    {
      title: 'IP地址',
      dataIndex: 'machineIp',
      key: 'machineIp',
      width: '15%',
      ellipsis: true,
      render: (text: any) => {
        return text || NO_DATA_IN_TABLE_COLUMN_DISPLY;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '12%',
      ellipsis: true,
      // eslint-disable-next-line react-hooks/rules-of-hooks
      render: (text: any) => statusNode(text, 0),
    },
  ];

  /** 获取当前已经选中的机器人id */
  // const getSelectedRowKeys = useCallback(() => {
  //   const selectedRowKeys: React.Key[] = [];
  //   selectedBotsData?.map((item: TaskAccountsType) => {
  //     selectedRowKeys.push(item.accountUuid!);

  //     return selectedBotsData;
  //   });

  //   return selectedRowKeys;
  // }, [selectedBotsData]);

  useEffect(() => {
    if (visible) {
      fetchBotList(searchForm);
    }
    setPaginationInfo({ ...initPaginationInfo });
    setSearchForm({});
    setSelectedBotsData([]);
  }, [visible]);

  return (
    <Modal
      className={`task-botlist-modal popup-center-modal normal-min-height`}
      title={'选择机器人'}
      open={visible}
      width={900}
      onCancel={onClose}
      onOk={() => okCb(selectedBotsData)}
      destroyOnClose
    >
      <div className="flex mb-[10px] header-filter-part">
        <Space wrap align="start">
          {Object.keys(SearchkeywordsSearchMap.current).length > 0 && (
            <Search
              addonBefore={
                Object.keys(SearchkeywordsSearchMap.current).length > 1
                  ? selectBefore
                  : null
              }
              onSearch={(val: string) => {
                curSearchName && searchBarUpdate(curSearchName, val);
              }}
              placeholder={`请输入${
                curSearchName &&
                SearchkeywordsSearchMap.current[curSearchName as any]
              }`}
              allowClear
              style={{ width: 300 }}
            />
          )}
          <SzSelect
            placeholder={'选择状态'}
            defaultValue={BotStatusEnum.All}
            onChange={(val: BotStatusEnum) => {
              searchBarUpdate('status', val);
            }}
            style={{ width: 100 }}
          >
            {taskBotStatusList.map(
              (item: { value: BotStatusEnum; text: string }) => {
                return (
                  <Option value={item.value} key={item.value}>
                    {item.text}
                  </Option>
                );
              },
            )}
          </SzSelect>
        </Space>
      </div>
      <Table
        size={'small'}
        dataSource={botList}
        rowKey={(re: BotInfoType) => re.uuid}
        columns={columns}
        className={'modal-table'}
        pagination={{
          current: paginationInfo.current,
          pageSize: paginationInfo.size,
          total: paginationInfo.total,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (val: number) => `共${val}条`,
          onChange: (pageNum?: number, pageSize?: number) => {
            fetchBotList(undefined, {
              pageNo: pageNum || paginationInfo.current,
              pageSize: pageSize || paginationInfo.size,
            });
          },
        }}
        rowSelection={{
          type: 'checkbox',
          onChange: (
            selectedRowKeys: React.Key[],
            selectedRows: BotInfoType[],
          ) => {
            const selectedRowsInfo = selectedRows.map((val: BotInfoType) => ({
              accountId: val.userId,
              accountUuid: val.uuid,
              accountName: val.userName,
              realName: val.realName,
              priority: 5,
            }));
            setSelectedBotsData(selectedRowsInfo);
          },
        }}
        loading={tableLoading}
      />
    </Modal>
  );
};

export default BotListModal;

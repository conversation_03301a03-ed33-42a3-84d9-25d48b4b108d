/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Select,
  Input,
  Space,
  Table,
  Form,
  Button,
  Radio,
  Spin,
  message,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  getTaskmanageSetup,
  getSelectPriorityList,
} from '../../../../../services';
import { HelpToolTip } from 'main/HelpToolTip';
import {
  StepNumberEnum,
  OpacityEnum,
  DistributionModeEnum,
  ExecuteTypeEnum,
  DistributionModeMap,
} from '@/types/taskcenter/type';
import type { JobCreateParams } from '@/types/taskcenter/interface';
import BotListModal from './components/BotListModal';
import taskUtil from '../../const';
import type {
  TaskUpdateDetailType,
  TaskAccountsType,
} from '@/types/taskcenter/interface';
import { RenderTipsIcon } from 'main/HelpToolTip';
import { formValidateDataA<PERSON> } from '@/utils/utils';
import { Sz<PERSON>onfirm, SzSelect } from 'main/SzComponents';
import { TASKCENTER_TEXT } from '../../../../const';
import _ from 'lodash';
import { NO_DATA_IN_TABLE_COLUMN_DISPLY } from '@/utils/constants/const';
import { GetPopupContainerLayerType } from '../../../../const';
import { useTaskUpdateModel } from '../../../store/update';
import styles from './index.less';

const { Option } = Select;
interface Props {
  detail?: TaskUpdateDetailType;
  opacity?: OpacityEnum;
  handleFns?: (handle: any) => void;
  stepData?: any;
  /** 关闭弹窗 */
  onClose?: () => void;
}
const defaultPriority = 5;
const Step3 = (props: Props) => {
  const { detail, opacity, handleFns, onClose } = props;
  const { saveStepFormData, saveCurrentStep, stepData, save } =
    useTaskUpdateModel();
  const [form] = Form.useForm();
  const [distributionType, setDistributionType] =
    useState<DistributionModeEnum>(DistributionModeEnum.AUTO);
  /** 从后台取回来的优先级列表，只需要加载一次 */
  const [priorityList, setPriorityList] = useState([]);
  /** 分配方式为自动分配时候的优先级取值 */
  const [currentPriorty, setCurrentPriorty] = useState<number>(defaultPriority);
  /** 优先级列表查询状态 */
  const [priorityListLoading, setPriorityListLoading] = useState(false);

  /** 渲染/更新后的机器人列表(所有被选中的，注意只渲染分页的当前页) */
  const [selectedAllBotDataSource, setSelectedAllBotDataSource] = useState<
    TaskAccountsType[]
  >([]);
  /** 渲染/更新后的当前页机器人列表 */
  const [selectedCurPageBotDataSource, setSelectedCurPageBotDataSource] =
    useState<TaskAccountsType[]>([]);
  /** 选择机器人是否显示 */
  const [selectBotVisible, setSelectBotVisible] = useState<boolean>(false);
  /** 已选择机器人列表的分页信息 */
  const [botPaginationInfo, setBotPaginationInfo] = useState<{
    current: number;
    total: number;
    size: number;
  }>({
    current: 1,
    total: 0,
    size: 10,
  });
  const [loading, setLoading] = useState(false);

  /** 查询优先级列表 */
  const fetchPriorityList = () => {
    setPriorityListLoading(true);
    getSelectPriorityList()
      .then((res: any) => {
        setPriorityList(res);
      })
      .finally(() => {
        setPriorityListLoading(false);
      });
  };

  /** 获取当前已经选中的机器人id数组 */
  const getBotIds = useCallback(() => {
    const botIdsArr: string[] = [];
    selectedAllBotDataSource.map((item: TaskAccountsType) => {
      if (item?.accountUuid) {
        botIdsArr.push(item.accountUuid);
      }
      return item;
    });

    return botIdsArr;
  }, [selectedAllBotDataSource]);

  /** 选择机器人子组件点击确定时候的回调方法 */
  const updateSelectedAllDataSource = useCallback(
    (addDatas: TaskAccountsType[]) => {
      if (addDatas?.length > 0) {
        setSelectedAllBotDataSource([
          ...selectedAllBotDataSource,
          ...(addDatas || []),
        ]);
      }
      setSelectBotVisible(false);
    },
    [selectedAllBotDataSource],
  );

  /**
   * 详情信息更新
   * 1. 更新分配方式
   * 2. 把accounts信息写入selectedAllBotDataSource
   * 3. 查询优先级列表
   */
  useEffect(() => {
    setDistributionType(detail?.distributionType || DistributionModeEnum.AUTO);
    detail?.accounts && setSelectedAllBotDataSource([...detail.accounts]);
    fetchPriorityList();
  }, [detail]);

  /**
   * 分配方式更新
   * 更改页面分配方式展示值
   */
  useEffect(() => {
    form.setFieldsValue({ distributionType });
  }, [distributionType]);

  /**
   * 优先级列表或者分配方式更新
   * 非指定机器人方式-设置全局默认机器人优先级，默认为5
   * 指定机器人方式-设置accounts每个机器人优先级，默认为5
   */
  useEffect(() => {
    if (priorityList?.length > 0) {
      if (distributionType !== DistributionModeEnum.ASSASSIGN) {
        // 分配方式非指定机器人
        setCurrentPriorty(detail?.priority || defaultPriority);
      } else {
        // 组装表格所有的数据
        setSelectedAllBotDataSource([...(detail?.accounts || [])]);
      }
    }
  }, [priorityList, distributionType]);

  /**
   * 选中机器人列表更新
   * 更新选中的机器人总数
   */
  useEffect(() => {
    form.setFieldsValue({
      selectedAllBotDataSource: [...selectedAllBotDataSource],
    });
    if (selectedAllBotDataSource?.length !== botPaginationInfo.total) {
      setBotPaginationInfo({
        ...botPaginationInfo,
        total: selectedAllBotDataSource?.length || 0,
      });
    }
  }, [selectedAllBotDataSource]);

  /**
   * 分页信息更新或者选中机器人列表更新
   * 1. 列表总数为0，表格数据为空
   * 2. 列表总数大于所取页首个数据索引，则表示存在该页数据，更新表格显示数据
   * 3. 列表总数小于等于索取页首个数据索引，则表示不存在该页数据，取最后一页数据
   */
  useEffect(() => {
    if (botPaginationInfo?.total === 0) {
      setSelectedCurPageBotDataSource([]);
    } else if (
      botPaginationInfo?.total >
      (botPaginationInfo?.current - 1) * botPaginationInfo?.size
    ) {
      setSelectedCurPageBotDataSource(
        selectedAllBotDataSource.slice(
          (botPaginationInfo?.current - 1) * botPaginationInfo?.size,
          botPaginationInfo?.current * botPaginationInfo?.size,
        ),
      );
    } else if (
      botPaginationInfo?.total ===
      (botPaginationInfo?.current - 1) * botPaginationInfo?.size
    ) {
      setBotPaginationInfo({
        ...botPaginationInfo,
        current: botPaginationInfo?.current - 1,
      });
    } else {
      setBotPaginationInfo({
        ...botPaginationInfo,
        current: Math.ceil(
          botPaginationInfo?.total / botPaginationInfo?.current,
        ),
      });
    }
  }, [
    botPaginationInfo?.current,
    botPaginationInfo?.size,
    botPaginationInfo?.total,
    selectedAllBotDataSource,
  ]);

  useEffect(() => {
    currentPriorty && form.setFieldsValue({ priority: currentPriorty });
  }, [currentPriorty]);

  useEffect(() => {
    if (
      stepData?.executeType === ExecuteTypeEnum.MANUAL &&
      distributionType !== DistributionModeEnum.ASSASSIGN
    ) {
      setDistributionType(DistributionModeEnum.ASSASSIGN);
      form.setFieldsValue({
        ...form.getFieldsValue(),
        distributionType: DistributionModeEnum.ASSASSIGN,
      });
    }
  }, [stepData?.executeType]);

  /** 删除某一行 */
  const deleteBotHandle = (record: TaskAccountsType) => {
    SzConfirm({
      title: '确认删除？',
      content: '',
      onOk() {
        // 从本地数据中把该条数据删除
        const idx = selectedAllBotDataSource.findIndex(
          (item: TaskAccountsType) => item.accountUuid === record.accountUuid,
        );
        if (idx >= 0) {
          const newSelectedAllBotDataSource = [...selectedAllBotDataSource];

          newSelectedAllBotDataSource.splice(idx, 1);
          setSelectedAllBotDataSource([...newSelectedAllBotDataSource]);
        }
      },
    });
  };

  // 处理bot列表中的优先级
  const handleSavePriority = (val: number, uuid: string) => {
    // 改变已选择数据的优先级
    if (selectedAllBotDataSource && selectedAllBotDataSource?.length > 0) {
      const newSelectedAllBotDataSource: TaskAccountsType[] = _.cloneDeep(
        selectedAllBotDataSource,
      );
      const selectedBotIdx = selectedAllBotDataSource.findIndex(
        (item: TaskAccountsType) => item.accountUuid === uuid,
      );
      if (
        selectedBotIdx > -1 &&
        !!newSelectedAllBotDataSource[selectedBotIdx]
      ) {
        newSelectedAllBotDataSource[selectedBotIdx].priority = val;
        setSelectedAllBotDataSource([...newSelectedAllBotDataSource]);
      }
    }
  };

  // 渲染优先级列表
  const renderPriorityList = () => {
    return priorityList?.map((item) => {
      return (
        <Option value={item} key={item}>
          {item}
        </Option>
      );
    });
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'accountName',
      key: 'accountName',
      width: '30%',
      ellipsis: true,
    },
    {
      title: '用户姓名',
      dataIndex: 'realName',
      key: 'realName',
      width: '30%',
      ellipsis: true,
      render: (text: string) => text || NO_DATA_IN_TABLE_COLUMN_DISPLY,
    },
    // {
    //   title: (
    //     <Space>
    //       <>优先级</>
    //       <HelpToolTip
    //         getPopupContainer={() => document.body}
    //         title={TASKCENTER_TEXT.TASK_TIP_PRIORITY}
    //       />
    //     </Space>
    //   ),
    //   dataIndex: 'priority',
    //   key: 'priority',
    //   width: '25%',
    //   ellipsis: true,
    //   render: (val: any, row: any) => {
    //     return (
    //       <SzSelect
    //         popupContainerLayer={GetPopupContainerLayerType.SIXPARENT}
    //         defaultValue={val || defaultPriority}
    //         style={{ width: '80px' }}
    //         onChange={(value: number) =>
    //           handleSavePriority(value, row.accountUuid)
    //         }
    //       >
    //         {renderPriorityList()}
    //       </SzSelect>
    //     );
    //   },
    // },
    {
      dataIndex: 'accountUuid',
      title: '操作',
      width: '15%',
      render: (text: string, record: TaskAccountsType) => {
        return (
          <a
            shizai-key="step3-deletebot"
            onClick={() => deleteBotHandle(record)}
          >
            {'删除'}
          </a>
        );
      },
    },
  ];

  const handleData = {
    /** 校验form表单数据 */
    validateData: () => formValidateDataApi(form),

    /**
     * 新建时候点击上一步的操作
     * 1. 把本步骤的数据存储到model
     * 2. 切换步骤类型到上一步
     */
    preData: async () => {
      const formData = await handleData?.setupData();

      if (formData) {
        saveStepFormData({
          ...stepData,
          ...formData,
        });
        saveCurrentStep(StepNumberEnum.TWO);
      }
    },

    /**
     * 组装要提交的数据: formValues
     */
    setupData: async () => {
      const saveData = {
        distributionType,
        accounts:
          distributionType === DistributionModeEnum.ASSASSIGN
            ? selectedAllBotDataSource || []
            : null,
        priority:
          distributionType === DistributionModeEnum.AUTO
            ? form.getFieldsValue().priority
            : null,
      };

      return { ...saveData };
    },
  };

  useEffect(() => {
    handleFns && handleFns(handleData);
  }, [distributionType, detail, currentPriorty, selectedAllBotDataSource]);

  return (
    <div
      className={`${styles['task-step3']} ${
        detail ? styles['task-edit-steps'] : styles['task-add-steps']
      }`}
      style={
        detail
          ? {}
          : { opacity, zIndex: opacity === OpacityEnum.DISPLAY ? 999 : -1 }
      }
    >
      <div>
        <Form
          form={form}
          {...taskUtil.formItemLayout}
          layout="horizontal"
          className={`${styles['step-form']}`}
        >
          <Form.Item
            label={'分配方式'}
            name="distributionType"
            style={{ marginBottom: 10 }}
          >
            {!detail ? (
              <Radio.Group
                onChange={(e) => setDistributionType(e.target.value)}
              >
                {stepData?.executeType !== ExecuteTypeEnum.MANUAL && (
                  <Radio
                    value={DistributionModeEnum.AUTO}
                    key={DistributionModeEnum.AUTO}
                  >
                    {DistributionModeMap[DistributionModeEnum.AUTO]}
                  </Radio>
                )}
                <Radio
                  value={DistributionModeEnum.ASSASSIGN}
                  key={DistributionModeEnum.ASSASSIGN}
                >
                  {DistributionModeMap[DistributionModeEnum.ASSASSIGN]}
                </Radio>
              </Radio.Group>
            ) : (
              <div>
                {detail?.distributionType &&
                  DistributionModeMap[detail.distributionType]}
              </div>
            )}
          </Form.Item>
          {/* {distributionType === DistributionModeEnum.AUTO && (
            <div>
              <Spin spinning={priorityListLoading}>
                <Form.Item
                  label={'优先级:'}
                  name="priority"
                  tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_TIP_PRIORITY)}
                >
                  <SzSelect style={{ width: 280 }}>
                    {renderPriorityList()}
                  </SzSelect>
                </Form.Item>
              </Spin>
            </div>
          )} */}
          <Form.Item
            label={'优先级:'}
            name="priority"
            tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_TIP_PRIORITY)}
          >
            <SzSelect style={{ width: 280 }}>{renderPriorityList()}</SzSelect>
          </Form.Item>
          {distributionType === DistributionModeEnum.ASSASSIGN && (
            <Form.Item
              label={'已选机器人'}
              name="selectedAllBotDataSource"
              rules={[
                {
                  required: true,
                  message: '请选择机器人',
                },
              ]}
            >
              <div>
                <Button
                  type="link"
                  style={{ padding: 0 }}
                  shizai-key="step3-selectbot"
                  onClick={() => {
                    setSelectBotVisible(true);
                  }}
                >
                  <PlusOutlined rev={undefined} />
                  <span style={{ marginLeft: 3 }}>{'选择机器人'}</span>
                </Button>
                <Table
                  rowKey={'accountUuid'}
                  size={'small'}
                  className={`drawer-table have-select-input`}
                  loading={priorityListLoading}
                  dataSource={selectedCurPageBotDataSource}
                  columns={columns}
                  pagination={
                    botPaginationInfo.total > botPaginationInfo.size
                      ? {
                          current: botPaginationInfo.current,
                          pageSize: botPaginationInfo.size,
                          total: botPaginationInfo.total,
                          showSizeChanger: false,
                          showQuickJumper: true,
                          showTotal: (val: number) => `共${val}条`,
                          onChange: (pageNum?: number, pageSize?: number) => {
                            // 截取当前列表的第pageNum页数据
                            setBotPaginationInfo({
                              ...botPaginationInfo,
                              current: pageNum || botPaginationInfo.current,
                              size: pageSize || botPaginationInfo.size,
                            });
                          },
                        }
                      : false
                  }
                />
                <Input type="hidden" />
              </div>
            </Form.Item>
          )}
        </Form>
        {!detail && (
          <div className={`${styles['steps-btn']}`}>
            <Button
              shizai-key="step3-pre"
              className="mr-[10px]"
              onClick={handleData.preData}
            >
              {'上一步'}
            </Button>
            <Button
              shizai-key="step3-submit"
              type="primary"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                try {
                  const validateResult = await handleData?.validateData();
                  if (validateResult) {
                    const formData = await handleData?.setupData();
                    const newNoticeList = stepData?.noticeList
                      ?.filter(
                        (item: any) =>
                          item.noticeStatus === 0 &&
                          item.channelList?.some((el: any) => el.status === 0),
                      )
                      .map((item: any) => {
                        return {
                          ...item,
                          channelList: item?.channelList?.filter(
                            (el: any) => el.status === 0,
                          ),
                          busiType: 3,
                          eventUuid: item.uuid,
                        };
                      });
                    const saveData = {
                      ...stepData,
                      ...formData,
                      noticeList: newNoticeList,
                      noticeUserList: stepData?.noticeUserList?.map(
                        (user: any) => {
                          return {
                            busiType: 3,
                            receiverId: user.id,
                          };
                        },
                      ),
                      // compatibleParam: { ...stepData, ...formData },
                    } as any;
                    // debugger;
                    await getTaskmanageSetup({
                      ...saveData,
                    });

                    setLoading(false);
                    onClose && onClose();
                    save({ taskListReload: Math.random() }),
                      message.success('提交成功');
                  }
                } catch (e) {
                  setLoading(false);
                }
              }}
              className="ml-10px"
            >
              {'提交'}
            </Button>
          </div>
        )}
        <BotListModal
          visible={selectBotVisible}
          hadSelectedBotIds={getBotIds()}
          okCb={updateSelectedAllDataSource}
          onClose={() => setSelectBotVisible(false)}
        />
      </div>
    </div>
  );
};

export default Step3;

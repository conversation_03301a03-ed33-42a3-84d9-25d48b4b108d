@import '~@/assets/styles/variable.less';
@import '~antd/es/style/themes/default.less';

.notification {
  width: 100%;
  border-radius: 4px;
  opacity: 1;
  background: #fff;
  box-sizing: border-box;
  border: 1px solid #e7e7e7;
  display: flex;

  .flex {
    display: flex;
    align-items: center;
  }

  .title {
    width: 106px;
    margin-right: 5px;
  }

  .left {
    flex: 5;
    border-right: 1px solid #e7e7e7;
    padding: 5px 6px;
    font-size: 14px;
    font-weight: normal;
    line-height: 18px;

    :global(.ant-col) {
      line-height: 18px;
      display: flex;
      align-items: center;
      letter-spacing: 0;
      color: #3b3d3d;
    }
  }

  .header {
    width: 100%;
    padding: 8px 4px;
    margin-bottom: 2px;
  }

  .row {
    width: 100%;
    border-radius: 4px;
    padding: 17px 8px;
    margin-bottom: 4px;
    cursor: pointer;
  }

  .row1 {
    width: 100%;
    border-radius: 4px;
    padding: 10px 8px;
  }

  .row:hover {
    background-color: #f8f8f8;
  }

  .active {
    background-color: #f8f8f8;
  }

  .right {
    flex: 2;
    padding: 5px 7px;
  }
}

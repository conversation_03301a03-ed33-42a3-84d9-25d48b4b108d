import { useState, useEffect } from 'react';
import { history } from 'umi';
import _, { set } from 'lodash';
import { Row, Switch, InputNumber, Checkbox, Typography, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { getNoticeConfig } from '@/services/notification';
import { useRequest } from 'ahooks';
import { getBizUnitPathCodeInTrees } from '@/utils/bizUtils';
import { useFrameWorkContext } from '@/exposes/deopCmdEntry/context';
import './index.less';

const { Paragraph } = Typography;

const NotificationSetting = (props: any) => {
  const { onChange, value } = props;
  const frameWorkContextInfo = useFrameWorkContext();
  const { treeMenu } = frameWorkContextInfo;
  const [activeUuid, setActiveUuid] = useState();
  const [channelList, setChannelList] = useState<any>([]);

  const [refreshKey, setRefreshKey] = useState(0);

  // 强制刷新的函数
  const forceRefresh = () => {
    setRefreshKey(refreshKey + 1);
  };

  const [isShowRight, setShowRight] = useState<boolean>(false);
  useRequest(getNoticeConfig, {
    onSuccess: (data) => {
      onChange(data);
      if (data && data.length > 0) {
        const firstConfigObj = data.find((item: any) => item.eventStatus === 0);
        if (firstConfigObj) {
          setActiveUuid(firstConfigObj.uuid);
          setChannelList(firstConfigObj.channelList);
          setShowRight(true);
        }
      }
    },
  });
  const checkboxValue = channelList
    ?.filter((item: any) => item.status === 0)
    .map((item: any) => item.uuid);

  return (
    <div className="notification">
      <div className="left">
        <div className="header flex">
          <div>通知事件</div>
          <div></div>
        </div>
        {value &&
          value.map((item: any) => {
            return (
              <div
                key={item.uuid}
                className={`row flex ${
                  activeUuid === item.uuid ? 'active' : ''
                }`}
                onClick={() => {
                  if (item.eventStatus === 0) {
                    setShowRight(true);
                    setActiveUuid(item.uuid);
                    setChannelList(item.channelList);
                  }
                }}
              >
                <div className="title">
                  {item.noticeName}
                  <Tooltip title={item.rule}>
                    <InfoCircleOutlined
                      rev={undefined}
                      style={{ marginLeft: '3px', color: '#0085FF' }}
                    />
                  </Tooltip>
                </div>
                {item.eventStatus === 0 ? (
                  <Switch
                    checked={item.noticeStatus === 0}
                    onChange={(val) => {
                      const newValue = value.map((ite: any) => {
                        if (ite.uuid === item.uuid) {
                          return {
                            ...ite,
                            noticeStatus: val ? 0 : 1,
                          };
                        } else {
                          return ite;
                        }
                      });
                      onChange(newValue);
                    }}
                  />
                ) : (
                  <div>
                    前往通知事件设置页，
                    <Typography.Link
                      onClick={() => {
                        const linkUrl = getBizUnitPathCodeInTrees(
                          treeMenu,
                          '/notificationSettings',
                        );
                        console.log(linkUrl);
                        history.push(linkUrl + '&key=EVENT');
                      }}
                    >
                      去设置
                    </Typography.Link>
                  </div>
                )}
                {item.itemType === 1 && item.eventStatus === 0 && (
                  <>
                    {item.noticeSign === 'job_queue_up' && (
                      <div
                        style={{ margin: '0 10px 0 10px', minWidth: '60px' }}
                      >
                        {'任务排队'}
                      </div>
                    )}
                    {item.noticeSign === 'job_timeout' && (
                      <div
                        style={{ margin: '0 10px 0 10px', minWidth: '60px' }}
                      >
                        {'任务运行'}
                      </div>
                    )}

                    <InputNumber
                      width={62}
                      min={0}
                      max={3000}
                      status={
                        item.itemValue > 3000 || item.itemValue < 0
                          ? 'error'
                          : ''
                      }
                      value={item.itemValue}
                      placeholder="请输入时间"
                      onChange={(val) => {
                        const newValue = value.map((ite: any) => {
                          if (ite.uuid === item.uuid) {
                            return {
                              ...ite,
                              itemValue: val,
                            };
                          } else {
                            return ite;
                          }
                        });
                        onChange(newValue);
                      }}
                    />
                    <div style={{ marginLeft: '10px' }}>分钟</div>
                  </>
                )}
              </div>
            );
          })}
      </div>
      {isShowRight && channelList && channelList.length > 0 && (
        <div className="right">
          <div className="header flex">渠道名称</div>
          <Checkbox.Group
            style={{ maxHeight: '420px', overflow: 'scroll' }}
            value={checkboxValue}
            onChange={(val) => {
              const converChanneList = (list: any[], value: any) => {
                return list.map((item: any) => {
                  if (_.includes(value, item.uuid)) {
                    return {
                      ...item,
                      status: 0,
                    };
                  } else {
                    return {
                      ...item,
                      status: 1,
                    };
                  }
                });
              };
              const newValue = value.map((item: any) => {
                if (item.uuid === activeUuid) {
                  const newChannelList = converChanneList(
                    item.channelList,
                    val,
                  );
                  setChannelList(newChannelList);
                  return {
                    ...item,
                    channelList: newChannelList,
                  };
                } else {
                  return item;
                }
              });

              onChange(newValue);
              forceRefresh();
            }}
          >
            {channelList.map((child: any) => {
              return (
                <Row
                  key={child.uuid}
                  className="row1"
                  style={{ width: '200px' }}
                >
                  <Checkbox value={child.uuid}>
                    <Paragraph>{child.name}</Paragraph>
                  </Checkbox>
                </Row>
              );
            })}
          </Checkbox.Group>
        </div>
      )}
    </div>
  );
};

export default NotificationSetting;

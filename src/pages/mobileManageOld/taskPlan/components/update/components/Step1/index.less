@import '~@/assets/styles/variable.less';
@import '~antd/es/style/themes/default.less';

.task-step1 {
  min-height: 100%;

  /** 新增 */
  &.task-add-steps {
    .step-form {
      max-width: 800px;
      margin: 20px auto 80px;
    }

    .steps-btn {
      position: fixed;
      bottom: 0;
      z-index: 1000;
      width: calc(100vw - @menu-width - 96px);
      padding: 10px 0;
      text-align: center;
      background: #fff;
      border-top: 1px solid #dee6ee;
    }
  }

  /** 编辑 */
  &.task-edit-steps {
    display: flex;
    justify-content: center;
    padding-bottom: 50px;
    overflow-y: auto;

    .step-form {
      width: 750px;
    }
  }

  /** 公共 */
  .select-option-item {
    display: flex;
    justify-content: space-between;

    .select-option-item-label {
      width: 68%;
    }

    .select-option-item-extra {
      width: 28%;
      color: rgb(51 67 85 / 30%);
      text-align: right;
    }
  }

  .text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
  }

  .ant-select {
    width: 100%;
  }

  .notification-wrap {
    display: flex;
  }

  .notification {
    width: 100%;
    border-radius: 4px;
    opacity: 1;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #e7e7e7;
    display: flex;

    .left {
      flex: 5;
      border-right: 1px solid #e7e7e7;
      padding: 5px 6px;

      :global(.ant-col) {
        line-height: 18px;
        display: flex;
        align-items: center;
        letter-spacing: 0;
        color: #3b3d3d;
      }
    }

    .header {
      width: 100%;
      padding: 7px 8px;
      margin-bottom: 4px;
    }

    .row {
      width: 100%;
      border-radius: 4px;
      padding: 14px 8px;
      margin-bottom: 4px;
    }

    .row:hover {
      background-color: #f8f8f8;
    }

    .right {
      flex: 2;
    }
  }
}

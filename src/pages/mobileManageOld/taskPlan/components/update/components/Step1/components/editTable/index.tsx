import { But<PERSON>, Popconfirm as PopConfirm, Table, Typography } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { uniqBy } from 'lodash';
import React, { useState } from 'react';
import AddUserModal from '@/bizComponents/AddUserModal';
import { getUser } from '@/services/notification';
import { userInfo } from '@/types/notification';

const Editable = (props: {
  value?: userInfo[];
  onChange?: (value: userInfo[]) => void;
}) => {
  const { value = [], onChange } = props;
  const [current, setCurrent] = useState(1);
  const [isAddUserModalShow, setIsAddUserModalShow] = useState(false);
  const handleDelete = (id: React.Key) => {
    const newData = value?.filter((item: userInfo) => item.id !== id);
    if (onChange) {
      onChange(newData);
    }
  };

  // 点击添加用户的确定按钮
  const addUser = async (realCheckedList: string[]) => {
    const res = await getUser(realCheckedList);
    const users: any = res;
    const newData = uniqBy([...users, ...value], 'id');
    if (onChange) {
      onChange(newData);
    }

    setIsAddUserModalShow(false);
  };

  const columns: ColumnsType<userInfo> = [
    {
      title: '用户',
      dataIndex: 'name',
      width: '30%',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: '30%',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      width: '30%',
    },

    {
      title: '操作',
      dataIndex: 'operation',

      render: (_, record: any) =>
        value && value.length >= 1 ? (
          <PopConfirm
            title="确定删除？"
            onConfirm={() => handleDelete(record.id)}
          >
            <Typography.Link type="danger">删除</Typography.Link>
          </PopConfirm>
        ) : null,
    },
  ];

  return (
    <div style={{ marginTop: '50px' }}>
      <Button
        icon={<PlusOutlined rev={undefined} />}
        type="link"
        onClick={() => setIsAddUserModalShow(true)}
        style={{ position: 'absolute', top: 0, right: 0 }}
      >
        添加
      </Button>
      <Table
        rowClassName={() => 'editable-row'}
        rowKey={`id`}
        bordered
        dataSource={value}
        columns={columns}
        pagination={{
          current: current,
          pageSize: 10,
          total: value.length,
          showTotal: (val: number) => `共${val}条`,
          onChange: (pageNum: number) => {
            setCurrent(pageNum);
          },
        }}
      />
      {isAddUserModalShow && (
        <AddUserModal
          isModalOpen={isAddUserModalShow}
          handleOk={addUser}
          handleCancel={() => {
            setIsAddUserModalShow(false);
          }}
          /**  */
          defaultCheckedList={value.map((item) => {
            return {
              ...item,
              userId: item.id,
              key: item.id,
              account: item.account,
              name: item.name,
            };
          })}
        />
      )}
    </div>
  );
};

export default Editable;

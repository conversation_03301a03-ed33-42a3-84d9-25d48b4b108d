/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useMemo } from 'react';
import type { RadioChangeEvent } from 'antd';
import { Form, Radio, Button, Input, Select, Typography } from 'antd';
import { RenderTipsIcon } from 'main/HelpToolTip';
import { TASKCENTER_TEXT } from '../../../../const';
import type { AppDetailType, TaskUpdateDetailType } from '@/types/taskcenter/interface';
import taskUtil from '../../const';
import TaskGroupStep1 from './components/TaskGroup';
import ApplicationForm from '../../../ApplicationSelectForm';
import { ApplicationFormTypeEnum } from '../../../ApplicationSelectForm/types';
import { formValidateDataApi } from '@/utils/utils';
import TaskManageServiceApi from '../../../../../services';
import { StepNumberEnum, OpacityEnum, TaskPlanTypeEnum } from '@/types/taskcenter/type';
import { useTaskUpdateModel } from '../../../store/update';
import { SzSelect, SzTreeSelect } from 'main/SzComponents';
import styles from './index.less';
import { useRequest } from 'ahooks';
import * as GlobalServiceApi from '@/services/global';
import { loopUpdateTreeData } from '@deopCmd/constants/formatDepartmentTree';
import { getTaskManageUserSelectList } from '@/services//taskManageService';
import NotificationSetting from './components/NotificationSetting';
import EditTable from './components/editTable';
const { Text } = Typography;
const { Option } = Select;
interface Props {
  /** 通过流程快速创建时的流程信息 */
  curProcess?: any;
  /** 编辑计划-详情信息 */
  detail?: TaskUpdateDetailType;
  /** 新增计划-是否展示该步骤 */
  opacity?: OpacityEnum;
  /** 是否是计划组 */
  isGroup: boolean;
  taskPlanType: TaskPlanTypeEnum;
  /** 默认流程 */
  processDefault?: string[];
  /** 流程默认渠道 */
  defaultChannel?: number;
  /** 数据处理对象 */
  handleFns?: (handle: any) => void;
}

// const generateTree = (treeNodes: GetDepartmentTreeRes = []) => {
//   return treeNodes.map((item: { name: string; structId: string; children: any }) => (
//     <TreeSelect.TreeNode title={item.name} value={String(item.structId)} key={item.structId}>
//       {item.children.length > 0 && generateTree(item.children)}
//     </TreeSelect.TreeNode>
//   ));
// };

const Step1 = (props: Props) => {
  const {
    detail,
    isGroup,
    taskPlanType,
    opacity,
    handleFns,
    curProcess,
    defaultChannel = 0,
    processDefault = [],
  } = props;
  // const { currentUser } = useSelector(({ user }: ConnectState) => user);
  const { saveStepFormData, saveCurrentStep } = useTaskUpdateModel();
  const [form] = Form.useForm();
  const [humanRobotInteractiveType, setHumanRobotInteractiveType] = useState<boolean>(false);
  const [handlerType, setHandlerType] = useState<number>(1);
  const [userList, setUserList] = useState<any>([]);
  /** 编辑计划（组）时候当前更新的流程数据 */
  const [curAppDetail, setCurAppDetail] = useState<AppDetailType>();

  /**
   * 流程组件数据处理
   * 新建/编辑计划：第一步的数据处理
   * 新建/编辑计划组：弹窗的数据处理
   */
  const [appDataHandle, setAppDataHandle] = useState<any>({});
  const getUserList = async () => {
    const res: any[] = await getTaskManageUserSelectList();

    const newData = res.map((item: any) => {
      return {
        key: item.userId,
        name: item.account,
      };
    });
    setUserList(newData);
  };
  const handleData = {
    /** 校验form表单数据 */
    validateData: async () => {
      /** 先验证当前模块 */
      try {
        const formValidateDataResult = await formValidateDataApi(form);
        const appValidateDataResult = await appDataHandle?.validateData();
        if (!formValidateDataResult || !appValidateDataResult) {
          return false;
        }
        return true;
      } catch (error) {
        console.log(error);
      }
    },

    /**
     * 新建计划-下一步：点击操作
     */
    nextData: async () => {
      let saveData: any = await handleData.setupData();
      const normalizeData = saveData?.formProcessDetails?.map((item: any) => {
        if (item.handler) {
          item.handler = item?.handler === 'ALL' ? '' : item?.handler;
        }

        return item;
      });

      const checkJobCreateFirstData = {
        jobType: taskPlanType,
        jobName: saveData?.jobName || '',
        // handlerType: saveData?.handlerType || undefined,
        // handler: saveData?.handler || undefined,
        // formProcessDetails: [...(normalizeData || [])],
        // arrangeId: saveData?.arrangeParam?.arrangeId,
        // arrangeVersionId: saveData?.arrangeParam?.arrangeVersionId,
      };
      // debugger;
      // saveStepFormData({ ...saveData });
      // saveCurrentStep(StepNumberEnum.TWO);

      // 新建计划时需要后端校验数据
      TaskManageServiceApi.getCheckJobCreateFirst(checkJobCreateFirstData).then(() => {
        saveStepFormData({ ...saveData });
        saveCurrentStep(StepNumberEnum.TWO);
      });
    },

    /**
     * 编辑时候点击提交，组装要提交的数据: formValues
     */
    setupData: async () => {
      const formData = form.getFieldsValue();

      // debugger;
      let appData = await appDataHandle?.setupData();
      if (!isGroup) {
        appData = [].concat(appData);
      }

      // 编辑
      if (detail?.jobId) {
        return { ...formData, jobToProcessAddList: [...appData] };
      }

      // 新增
      // debugger;
      return {
        ...formData,
        jobType: taskPlanType,
        // arrangeParam: appData[0]?.arrangeParam,
        isGroup: false,
        jobToProcessAddList: [...appData],
      };
    },

    /** 清除数据 */
    clearData: () => {},
  };

  // 绑定数据通信
  const bindHandle = () => {
    if (handleFns) {
      handleFns(handleData);
    }
  };

  const { data: departmentTreeData } = useRequest(GlobalServiceApi.getDepartmentTrees, {});

  const departmentTree = useMemo(() => {
    if (departmentTreeData?.bizData.id) {
      return loopUpdateTreeData([departmentTreeData?.bizData]);
    }
    return [];
  }, [departmentTreeData]);

  useEffect(() => {
    getUserList();
    bindHandle();
  }, []);

  useEffect(() => {
    if (!isGroup && detail?.jobId && detail?.processDetails?.length && detail.processDetails.length > 0) {
      setCurAppDetail({ ...detail?.processDetails[0] });
    }
  }, [detail]);

  useEffect(() => {
    bindHandle();
  }, [appDataHandle]);

  return (
    <div
      className={`${styles['task-step1']} ${detail ? styles['task-edit-steps'] : styles['task-add-steps']}`}
      style={detail ? {} : { opacity, zIndex: opacity === OpacityEnum.DISPLAY ? 999 : -1 }}>
      <Form {...taskUtil.formItemLayout} form={form} layout="horizontal" className={styles['step-form']}>
        {detail ? (
          <>
            <Form.Item
              label={'计划名称'}
              tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_NMAE_HELP_TIP)}
              style={{ marginBottom: '10px' }}>
              <div>{detail?.jobName}</div>
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item
              label={'计划名称'}
              name="jobName"
              tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_NMAE_HELP_TIP)}
              rules={[
                {
                  required: true,
                  message: '必须以中文、日文、英文和数字开头，且不能超过200个字符',
                  pattern: /^[\u4E00-\u9FA5\u0800-\u4e00A-Za-z0-9].{0,200}$/,
                },
              ]}>
              <Input placeholder="请输入计划名称" maxLength={200} />
            </Form.Item>
          </>
        )}
        {isGroup ? (
          <TaskGroupStep1
            taskPlanType={taskPlanType}
            detail={detail?.processDetails}
            handleFns={setAppDataHandle}
            setHumanRobotInteractiveType={setHumanRobotInteractiveType}
          />
        ) : (
          <ApplicationForm
            taskPlanType={taskPlanType}
            detail={curAppDetail}
            formType={ApplicationFormTypeEnum.NORMAL}
            handleFns={setAppDataHandle}
            processDefault={processDefault}
            defaultChannel={defaultChannel}
            setHumanRobotInteractiveType={setHumanRobotInteractiveType}
          />
        )}
        {humanRobotInteractiveType && taskPlanType !== TaskPlanTypeEnum.ARRANGE && (
          <Form.Item
            label={'处理人'}
            name="handlerType"
            rules={[{ required: true, message: '请选择处理人' }]}
            initialValue={1}>
            <Radio.Group
              onChange={({ target: { value } }: RadioChangeEvent) => {
                form.resetFields(['handler']);
                setHandlerType(value);
              }}>
              <Radio key={'myself'} value={1}>
                {'创建给自己'}
              </Radio>
              <Radio key={'otherUser'} value={2}>
                {'创建给其他用户'}
              </Radio>
              <Radio key={'otherDepartment'} value={3}>
                {'创建给部门'}
              </Radio>
            </Radio.Group>
          </Form.Item>
        )}
        {handlerType === 2 && (
          <Form.Item label={'选择用户'} name="handler" rules={[{ required: true, message: '请选择用户' }]}>
            <SzSelect
              placeholder={'请选择用户'}
              showSearch
              style={{ width: '100%' }}
              optionFilterProp={'children'}
              mode="multiple">
              {userList.map((item: { key: string; name: string }) => {
                return (
                  <Option value={item.key} key={item.key}>
                    {item.name}
                  </Option>
                );
              })}
            </SzSelect>
          </Form.Item>
        )}

        <Form.Item label={'所属部门'} name={'departmentId'} rules={[{ required: true }]}>
          <SzTreeSelect style={{ width: '100%' }} allowClear placeholder="请选择所属部门" treeData={departmentTree} />
        </Form.Item>

        {/* <Text type="secondary" style={{ width: '100px' }}>
          通知设置：
        </Text> */}
        {/* <Form.Item label={'通知设置'} name={'noticeList'}>
          <NotificationSetting />
        </Form.Item>
        <Form.Item label={'通知接收用户'} name={'noticeUserList'}>
          <EditTable />
        </Form.Item> */}
      </Form>
      <>
        {!detail && (
          <div className={`${styles['steps-btn']}`}>
            <Button
              shizai-key="step1-next"
              type="primary"
              htmlType="submit"
              onClick={async () => {
                const validateResult = await handleData?.validateData();
                if (validateResult) {
                  await handleData?.nextData();
                }
              }}>
              下一步
            </Button>
          </div>
        )}
      </>
    </div>
  );
};

export default Step1;

/* eslint-disable react/no-unknown-property */
import React, { useState, useEffect, useRef } from 'react';
import { Form, Button, Table, Space, Input, Modal, message } from 'antd';
import type {
  AppDetailType,
  TaskGroupTableItemType,
} from '@/types/taskcenter/interface';
import { formValidateDataApi } from '@/utils/utils';
import taskUtil from '../../../../const';
import { PlusOutlined, MenuOutlined } from '@ant-design/icons';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { SzConfirm, SzTooltip } from 'main/SzComponents';
import ApplicationForm from '../../../../../../components/ApplicationSelectForm';
import { ApplicationFormTypeEnum } from '../../../../../../components/ApplicationSelectForm/types';
import JsonForm from '@/bizComponents/InputParamsForm';
import { ReferanceParameterUpdateTypeEnum } from '@/bizComponents/InputParamsForm/types';
import type { AppChannelEnum } from '@/types/application/type';
import { AppChannelMap } from '@/types/application/type';

import { StatusLableMap, StatusLableColorMap } from '@/types/type';
import { cloneDeep } from 'lodash';
import { hexToOneRgba } from '@/utils/calColorRgba';
import { GetPopupContainerLayerType } from '../../../../../../const';

import styles from './index.less';
import RunSettingForm from '../../../../../RunSettingForm';
import { TaskPlanTypeEnum } from '@/types/taskcenter/type';

interface Props {
  /** 初始化流程列表 */
  taskPlanType: TaskPlanTypeEnum;
  detail?: AppDetailType[];
  handleFns?: (handle: any) => void;
  setHumanRobotInteractiveType?: (val: boolean) => void;
}

const DragHandle = SortableHandle(() => (
  <MenuOutlined rev={undefined} style={{ cursor: 'grab', color: '#999' }} />
));
const SortableItem = SortableElement((config: any) => <tr {...config} />);
const Sortable = SortableContainer((config: any) => <tbody {...config} />);

const TaskGroupStep1 = (props: Props) => {
  const { taskPlanType, detail, handleFns, setHumanRobotInteractiveType } =
    props;
  const [form] = Form.useForm();

  const relateAppDataSourceMaxIdx = useRef<number>(-1);

  /** 渲染/更新后的流程列表 */
  const [relateAppDataSource, setRelateAppDataSource] = useState<
    TaskGroupTableItemType[]
  >([]);
  /** 选择流程是否显示 */
  const [selectAppVisible, setSelectAppVisible] = useState<boolean>(false);
  /** 组合流程数据 */
  const [appDataHandle, setAppDataHandle] = useState<any>({});
  /** 运行设置是否显示 */
  const [runSetting, setRunSetting] = useState<boolean>(false);
  /** 运行设置参数数据 */
  const [runSettingDataHandle, setRunSettingDataHandle] = useState<any>({});
  /** 引用参数是否显示 */
  const [inputParamsVisible, setInputParamsVisible] = useState<boolean>(false);
  /** 组合引用参数数据 */
  const [inputParamsDataHandle, setInputParamsDataHandle] = useState<any>({});
  /** 目前正在操作的流程信息 */
  const [curAppDetail, setCurAppDetail] =
    useState<TaskGroupTableItemType | null>(null);
  /** 删除某一行 */
  const deleteAppHandle = (record: TaskGroupTableItemType) => {
    SzConfirm({
      title: '确认删除？',
      content: '',
      onOk: () => {
        // 从本地数据中把该条数据删除
        const index = relateAppDataSource.findIndex(
          (item: TaskGroupTableItemType) => item.idx === record.idx,
        );

        if (index > -1) {
          const appRenderList = [...relateAppDataSource];
          appRenderList.splice(index, 1);
          setRelateAppDataSource(appRenderList);
        }
      },
    });
  };
  /** 流程列表 */
  const columns = [
    {
      title: '排序',
      dataIndex: 'idx',
      className: `${styles['itemSort']}`,
      width: 50,
      render: () => <DragHandle />,
    },
    {
      title: '流程名称',
      dataIndex: 'processName',
      className: `${styles['itemProcessName']}`,
      ellipsis: true,
      render: (text: string) => (
        <SzTooltip
          title={text}
          placement="topLeft"
          popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}
        >
          {text}
        </SzTooltip>
      ),
      width: '25%',
    },
    {
      title: '版本：',
      dataIndex: 'processVersion',
      className: `${styles['itemProcessVersion']}`,
      ellipsis: true,
      width: '10%',
    },
    {
      title: '渠道',
      dataIndex: 'processChannel',
      render: (value: AppChannelEnum) => {
        return AppChannelMap?.[value] || '未知';
      },
      width: '14%',
    },
    {
      title: '标签',
      dataIndex: 'processVersionLabel',
      render: (
        value: keyof typeof StatusLableColorMap & keyof typeof StatusLableMap,
      ) => {
        return (
          <span
            style={{
              color: `${StatusLableColorMap?.[value]}`,
              backgroundColor: `${hexToOneRgba(
                StatusLableColorMap?.[value],
                0.1,
              )}`,
              padding: '2px 6px',
              borderRadius: '9px',
            }}
          >
            {StatusLableMap?.[value]}
          </span>
        );
      },
      width: '16%',
    },
    {
      title: '操作',
      key: 'handle',
      // className: `${styles['drag-hidden']}`,
      width: '25%',
      render: (text: any, record: TaskGroupTableItemType) => {
        return (
          <Space>
            <a
              shizai-key="step1-processedit"
              onClick={() => {
                setCurAppDetail({ ...record });
                setRunSetting(true);
              }}
            >
              {'编辑'}
            </a>
            <a
              shizai-key="step1-processdelete"
              onClick={() => deleteAppHandle(record)}
            >
              {'删除'}
            </a>
            {!record?.isReference ? null : (
              <a
                shizai-key="step1-processreferenceparameter"
                onClick={() => {
                  setCurAppDetail({ ...record });
                  setInputParamsVisible(true);
                }}
              >
                {'引用参数'}
              </a>
            )}
          </Space>
        );
      },
    },
  ];

  /** 关闭选择流程弹窗 */
  const onCloseAppModal = () => {
    // appDataHandle.clearData();
    setSelectAppVisible(false);
    setCurAppDetail(null);
  };

  /** 关闭运行设置弹窗 */
  const onCloseRunSetting = () => {
    setRunSetting(false);
    setCurAppDetail(null);
  };

  /** 关闭引用参数弹窗 */
  const onCloseInputParamsModal = () => {
    // inputParamsDataHandle.clearData();
    setInputParamsVisible(false);
    setCurAppDetail(null);
  };

  /** 拖拽结束时候的回调 */
  const onSortEnd = ({
    oldIndex,
    newIndex,
  }: {
    oldIndex: number;
    newIndex: number;
  }) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(
        relateAppDataSource,
        oldIndex,
        newIndex,
      ).filter((el: any) => !!el);
      setRelateAppDataSource(newData);
    }
  };

  /** draggableContainer: 可被拖拽的容器 */
  const draggableContainer = (item: any) => {
    return (
      <Sortable
        useDragHandle
        disableAutoscroll
        helperClass={`${styles['rowDragging']}`}
        onSortEnd={onSortEnd}
        {...item}
      />
    );
  };

  /** DraggableBodyRow：可被推拽的行 */
  const draggableBodyRow = ({ style, ...rest }: any) => {
    const index = relateAppDataSource.findIndex(
      (item: TaskGroupTableItemType) => {
        return item.idx === rest['data-row-key'];
      },
    );

    return <SortableItem index={index} {...rest} />;
  };

  const handleData = {
    /** 校验form表单数据 */
    validateData: () => formValidateDataApi(form),

    /**
     * 组装要提交的数据: formValues
     */
    setupData: async () => {
      const saveData = [...relateAppDataSource];

      return saveData;
    },

    /** 清除数据 */
    clearData: () => {},
  };

  // 绑定数据通信
  const bindHandle = () => {
    if (handleFns) handleFns(handleData);
  };

  useEffect(() => {
    bindHandle();
  }, []);

  useEffect(() => {
    bindHandle();
  }, [relateAppDataSource, appDataHandle, inputParamsDataHandle]);

  useEffect(() => {
    const relateAppList: TaskGroupTableItemType[] = [];
    (detail || []).map((item: AppDetailType, index: number) => {
      if (item?.processDetailUuid) {
        relateAppList.push({
          ...item,
          idx: index,
          jobProcessUuid: item?.uuid,
          processName: item?.processName || '',
          processVersion: item?.processVersion || '',
          processDetailUuid: item?.processDetailUuid,
          processChannel: item.processChannel!,
          inputParam: !item?.inputParam ? null : { ...item?.inputParam },
          isReference: item.isReference,
          isNewVersion: item.isNewVersion,
          template: !item?.template ? null : [...item?.template],
          processHumanMinute: item?.processHumanMinute || 0,
          processVersionLabel: item?.processVersionLabel,
          screenRecordingFlag: item?.screenRecordingFlag,
          silentOperationFlag: item?.silentOperationFlag,
          uploadControllerFlag: item?.uploadControllerFlag,
        });
        relateAppDataSourceMaxIdx.current = index;
      }
      return item;
    });

    setRelateAppDataSource(relateAppList);
  }, [detail]);

  useEffect(() => {
    form.setFieldsValue({
      relateAppDataSource: relateAppDataSource || null,
    });
    const isRobert = relateAppDataSource.find((item: AppDetailType) => {
      return item.humanRobotInteractiveType;
    });

    if (setHumanRobotInteractiveType)
      setHumanRobotInteractiveType(Boolean(isRobert));
  }, [relateAppDataSource]);

  return (
    <>
      <Form {...taskUtil.formItemLayout} form={form}>
        <Form.Item
          label={'包含流程'}
          name="relateAppDataSource"
          rules={[
            {
              required: true,
              message: '请选择流程版本',
            },
          ]}
        >
          <div>
            <Button
              shizai-key="step1-selectprocess"
              type="link"
              style={{ padding: 0 }}
              onClick={() => {
                if (relateAppDataSource?.length >= 20) {
                  message.warn('最多只可以选择20个流程～');
                  return;
                }
                setSelectAppVisible(true);
              }}
            >
              <PlusOutlined rev={undefined} />
              <span style={{ marginLeft: 3 }}>{'选择流程'}</span>
            </Button>
            <div className={styles['processTips']}>
              {'任务组内流程按序执行，前序流程执行失败，后面流程将不再执行'}
            </div>
            <Table
              size={'small'}
              className={`drawer-table`}
              pagination={false}
              dataSource={relateAppDataSource}
              columns={columns}
              rowKey={'idx'}
              components={{
                body: {
                  wrapper: draggableContainer,
                  row: draggableBodyRow,
                },
              }}
            />
            <Input type="hidden" />
          </div>
        </Form.Item>

        {/* 选择流程 */}
        <Modal
          title={<div style={{ textAlign: 'center' }}>{'选择流程'}</div>}
          width={600}
          open={selectAppVisible}
          onOk={async () => {
            // 组合数据
            const appValidateDataResult = await appDataHandle?.validateData();
            if (!appValidateDataResult) {
              return;
            }

            const appData: AppDetailType = await appDataHandle?.setupData();
            const relateAppData = [...relateAppDataSource];

            if (appData) {
              relateAppDataSourceMaxIdx.current += 1;

              relateAppData.push({
                idx: relateAppDataSourceMaxIdx.current,
                ...appData,
              });
              setRelateAppDataSource(relateAppData);
            }

            onCloseAppModal();
            if (appData && relateAppData.length > 0) {
              const curHandleRecord = relateAppData[relateAppData.length - 1];
              if (curHandleRecord?.isReference) {
                setCurAppDetail({ ...curHandleRecord });
                setInputParamsVisible(true);
              }
            }
          }}
          onCancel={() => {
            onCloseAppModal();
          }}
          destroyOnClose
        >
          {selectAppVisible && (
            <ApplicationForm
              taskPlanType={taskPlanType}
              isBackOptionLabel={true}
              formType={ApplicationFormTypeEnum.NOJSONFORM}
              handleFns={setAppDataHandle}
            />
          )}
        </Modal>

        {/* 编辑流程 */}
        <Modal
          title={<div style={{ textAlign: 'center' }}>{'运行设置'}</div>}
          width={600}
          open={runSetting}
          onOk={async () => {
            // 组合数据
            const runSettingData = await runSettingDataHandle?.setupData();
            const index = relateAppDataSource.findIndex(
              (item: TaskGroupTableItemType) => item.idx === curAppDetail?.idx,
            );
            if (runSettingData && index > -1) {
              const newRelateAppDataSource = cloneDeep(relateAppDataSource);
              newRelateAppDataSource[index] = {
                ...newRelateAppDataSource[index],
                ...runSettingData,
              };

              setRelateAppDataSource(newRelateAppDataSource);
            }
            onCloseRunSetting();
          }}
          onCancel={() => {
            onCloseRunSetting();
          }}
          destroyOnClose
        >
          {runSetting && !!curAppDetail && (
            <RunSettingForm
              handleFns={setRunSettingDataHandle}
              curAppDetail={curAppDetail}
            />
          )}
        </Modal>

        {/* 引用参数 */}
        <Modal
          className="popup-center-modal normal-min-height"
          title={<div style={{ textAlign: 'center' }}>{'填写引用参数'}</div>}
          width={600}
          open={inputParamsVisible}
          onOk={async () => {
            // 组合数据
            const inputParamsData = await inputParamsDataHandle?.setupData();
            const index = relateAppDataSource.findIndex(
              (item: TaskGroupTableItemType) => item.idx === curAppDetail?.idx,
            );
            if (inputParamsData && index > -1) {
              const newRelateAppDataSource = cloneDeep(relateAppDataSource);
              newRelateAppDataSource[index].inputParam = { ...inputParamsData };

              setRelateAppDataSource(newRelateAppDataSource);
            }
            onCloseInputParamsModal();
          }}
          onCancel={() => {
            onCloseInputParamsModal();
          }}
          destroyOnClose
        >
          {curAppDetail && (
            <JsonForm
              formId="taskGroup-jsonForm"
              jsonformInfo={{
                isNewVersion: curAppDetail?.isNewVersion,
                isReference: curAppDetail?.isReference,
                template: curAppDetail?.template,
                inputParam: curAppDetail?.inputParam,
              }}
              handleFns={setInputParamsDataHandle}
              updateType={ReferanceParameterUpdateTypeEnum.EDIT}
            />
          )}
        </Modal>
      </Form>
    </>
  );
};

export default TaskGroupStep1;

/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useEffect } from 'react';
import { Form, Radio, Input, InputNumber, Button, Switch, Select, Space, Col, Row, message } from 'antd';
import { ModalTiming } from './components/modalTiming';
import { RenderTipsIcon } from 'main/HelpToolTip';
import taskUtil from '../../const';
import {
  ScheduleTypeEnum,
  TimingTimeTypeEnum,
  TimingTimeTypeMap,
  ExecuteTypeEnum,
  ExecuteTypeMap,
  StepNumberEnum,
  OpacityEnum,
  TaskPlanTypeEnum,
} from '@/types/taskcenter/type';
import moment from 'moment';

import { getCheckJobCreateSecond, getTaskConfig, getTaskmanageSetup } from '../../../../../services';
import type { TaskUpdateDetailType } from '@/types/taskcenter/interface';
import { SzDatePicker, SZDatePickerType } from 'main/SzComponents';
import * as Service from '../../../../services';
import { TASKCENTER_TEXT } from '../../../../const';
import { formValidateDataApi } from '@/utils/utils';
import styles from './index.less';
import StyleText, { Colors } from '@/components/StyleText';
import CalendarDetailDrawer from '@/pages/calendarManage/components/CalendarDetailDrawer';
import { useReactive } from 'ahooks';
import EditCalendarDrawer from '@/pages/calendarManage/components/EditCalendarDrawer';
import { useTaskUpdateModel } from '../../../store/update';
interface Props {
  /** 编辑任务-详情信息 */
  detail?: TaskUpdateDetailType;
  taskPlanType: TaskPlanTypeEnum;
  /** 新增任务-是否展示该步骤 */
  opacity?: OpacityEnum;
  handleFns?: (handle: any) => void;
  stepData?: Record<string, unknown>;
  /** 关闭弹窗 */
  onClose?: () => void;
}
const Step2 = (props: Props) => {
  const { detail, taskPlanType, handleFns, opacity, onClose } = props;
  const { saveStepFormData, saveCurrentStep, save, stepData } = useTaskUpdateModel();
  const [form] = Form.useForm();
  const [warnCheck, setWarnCheck] = useState<boolean>(false);
  const [warnTime, setWarnTime] = useState<number | undefined>(30);
  const state = useReactive({
    calendarId: '',
    detailOpen: false,
    createOpen: false,
  });

  // 执行方式
  const [executeType, setExecuteType] = useState<ExecuteTypeEnum>(ExecuteTypeEnum.IMMEDIATE);
  // 定时类型
  const [scheduleType, setScheduleType] = useState<ScheduleTypeEnum>(ScheduleTypeEnum.TIMING);
  // 定时时效类型
  const [timingTimeType, setTimingTimeType] = useState<TimingTimeTypeEnum>(TimingTimeTypeEnum.LONGTERM);
  // 定时任务具体的值(分钟是数字，其他是cron表达式)
  const [timeCron, setTimeCron] = useState<number | string>(5);
  // 等待超时时间和最大排列数，新增初始化从系统设置中读
  const [timeoutWaitTime, setTimeoutWaitingTime] = useState<number>(30);
  const [maxQueueNumber, setMaxQueueNumber] = useState<number>(30);

  const [loading, setLoading] = useState(false);

  const [calendarOptions, setCalendarOptions] = useState<Service.CalendarListRes>([]);

  // 查询系统设置数据
  const queryTaskWaitingTime = async () => {
    const res = await getTaskConfig();
    const systemSettingsData = res;
    if (systemSettingsData) {
      systemSettingsData?.timeoutWaitTime && setTimeoutWaitingTime(systemSettingsData?.timeoutWaitTime);
      systemSettingsData?.maxQueueNumber && setMaxQueueNumber(systemSettingsData.maxQueueNumber);
      systemSettingsData?.timeoutFlag && setWarnCheck(systemSettingsData?.timeoutFlag);
      systemSettingsData?.timeout && setWarnTime(systemSettingsData?.warnTime);
    }
  };

  //查询日历列表
  useEffect(() => {
    if (timingTimeType !== TimingTimeTypeEnum.CALENDAR) return;
    if (state.createOpen) return;
    Service.getCalendarList({ name: '' }).then((res) => {
      setCalendarOptions(res);
    });
  }, [timingTimeType, state.createOpen]);

  /** 新增需要查询等待超时时间和最大排列数量的默认值 */
  useEffect(() => {
    !detail && queryTaskWaitingTime();
  }, []);

  useEffect(() => {
    form.setFieldsValue({
      maxQueueWait: timeoutWaitTime,
      maxQueueNumber,
    });
  }, [timeoutWaitTime, maxQueueNumber]);

  useEffect(() => {
    form.setFieldsValue({
      timeoutFlag: warnCheck,
      timeout: warnTime,
    });
  }, [warnCheck, warnTime]);

  /**
   * 切换执行方式
   */
  const changeExecuteType = (e: any) => {
    setExecuteType(e.target.value);
  };

  /**
   * 切换定时时效
   */
  const changeTimingTimeType = (e: any) => {
    setTimingTimeType(e.target.value);
  };

  /**
   * 获取调度组件的调度类型
   */
  const getScheduleType = (prop: any) => {
    setScheduleType(prop);
  };

  /**
   * 获取调度组件的调度值
   * scheduleType为ScheduleTypeEnum.MINUTES(分钟)是直接传值，对应后端fixTime字段
   * 其他类型的scheduleType为cron表达式，对应后端cronExpression表达式
   */
  const getTimeCron = (prop: any) => {
    setTimeCron(prop);
  };

  const warnOnChange = () => {
    setWarnCheck(!warnCheck);
  };

  useEffect(() => {
    if (executeType === ExecuteTypeEnum.TIMING) {
      form.setFieldsValue({
        cronExpression: timeCron,
      });
    }
  }, [timeCron, executeType]);

  const handleData = {
    /** 校验form表单数据 */
    validateData: () => formValidateDataApi(form),

    /**
     * 新建时候点击上一步的操作
     * 1. 把本步骤的数据存储到model
     * 2. 切换步骤类型到上一步
     */
    preData: async () => {
      let formData = form.getFieldsValue();
      if (warnCheck) {
        formData = { ...formData, timeoutFlag: 1, timeout: warnTime };
      } else {
        formData = { ...formData, timeoutFlag: 0 };
      }
      saveStepFormData({
        ...stepData,
        ...formData,
      });
      saveCurrentStep(StepNumberEnum.ONE);
    },

    /**
     * 新建时候点击下一步的操作
     * 1. 前端校验数据的合理性
     * 2. 请求后端接口进行本步骤的数据校验
     * 3. 切换步骤类型到下一步
     */
    nextData: async () => {
      const saveData = await handleData.setupData();

      // 新建任务时需要后端校验数据
      const saveStepData = () => {
        saveStepFormData(saveData);
        if (taskPlanType !== TaskPlanTypeEnum.ARRANGE) {
          saveCurrentStep(StepNumberEnum.THREE);
        }
      };

      switch (executeType) {
        case ExecuteTypeEnum.IMMEDIATE:
          getCheckJobCreateSecond({
            executeType,
            executeTimes: saveData.executeTimes,
            maxQueueWait: saveData.maxQueueWait,
            maxQueueNumber: saveData.maxQueueNumber,
            timeoutFlag: saveData?.timeoutFlag,
            timeout: saveData?.timeout,
          }).then(() => {
            saveStepData();
          });
          break;
        case ExecuteTypeEnum.TIMING:
          getCheckJobCreateSecond({
            executeType,
            executeTimes: saveData.executeTimes,
            maxQueueWait: saveData.maxQueueWait,
            maxQueueNumber: saveData.maxQueueNumber,
            scheduleType: saveData.scheduleType,
            fixTime: saveData?.fixTime,
            cronExpression: saveData?.cronExpression,
            isCheckActive: saveData?.isCheckActive,
            isAssignedCalendar: saveData?.isAssignedCalendar,
            calendarId: saveData.calendarId,
            scheduleStart: saveData?.scheduleStart,
            scheduleEnd: saveData?.scheduleEnd,
            timeoutFlag: saveData?.timeoutFlag,
            timeout: saveData?.timeout,
          }).then(() => {
            saveStepData();
          });
          break;
        case ExecuteTypeEnum.MANUAL:
          getCheckJobCreateSecond({
            executeType,
            maxQueueWait: saveData.maxQueueWait,
            maxQueueNumber: saveData.maxQueueNumber,
            timeoutFlag: saveData?.timeoutFlag,
            timeout: saveData?.timeout,
          }).then(() => {
            saveStepData();
          });
          break;
        default:
          saveStepData();
      }
    },

    /**
     * 组装要提交的数据: formValues
     */
    setupData: async () => {
      const formData = form.getFieldsValue();
      let saveData;
      if (warnCheck) {
        saveData = { ...formData, timeoutFlag: 1, timeout: warnTime };
      } else {
        saveData = { ...formData, timeoutFlag: 0 };
      }
      if (executeType === ExecuteTypeEnum.TIMING) {
        const isCheckActive = timingTimeType === TimingTimeTypeEnum.SPECIFIEDTIME;
        const isAssignedCalendar = timingTimeType === TimingTimeTypeEnum.CALENDAR;

        // 非定时并且选择了指定有效时间才传具体的值
        const isSetTimingTerm = scheduleType !== ScheduleTypeEnum.TIMING && isCheckActive;
        saveData.scheduleType = scheduleType;
        saveData.isCheckActive = scheduleType === ScheduleTypeEnum.TIMING ? false : isCheckActive;
        saveData.isAssignedCalendar = isAssignedCalendar;

        // 如果选择了非长期有效，则需要传递时效
        saveData.scheduleStart = isSetTimingTerm
          ? moment(formData.timingTimeDate[0]).format(taskUtil.TIMING_TIME_FORMAT_STRING)
          : null;
        saveData.scheduleEnd = isSetTimingTerm
          ? moment(formData.timingTimeDate[1]).format(taskUtil.TIMING_TIME_FORMAT_STRING)
          : null;

        // 调度类型为分钟则区分fixTime和cronExpression字段
        if (scheduleType === ScheduleTypeEnum.MINUTES) {
          saveData.fixTime = timeCron;
          saveData.cronExpression = null;
        } else {
          saveData.cronExpression = timeCron;
          saveData.fixTime = null;
        }
      }

      return saveData;
    },
  };

  useEffect(() => {
    handleFns && handleFns(handleData);
  }, [executeType, scheduleType, timeCron, timingTimeType, detail, warnCheck, warnTime]);

  useEffect(() => {
    if (detail) {
      state.calendarId = detail.calendarId;

      const executeTypeFormDetail = detail.executeType;
      const normalValues = {
        executeType: executeTypeFormDetail,
        maxQueueWait: Number(detail.maxQueueWait),
        maxQueueNumber: Number(detail.maxQueueNumber),
      };
      executeTypeFormDetail && setExecuteType(executeTypeFormDetail);
      setWarnCheck(detail?.timeoutFlag ? true : false);
      setWarnTime(detail?.timeout);
      switch (executeTypeFormDetail) {
        case ExecuteTypeEnum.IMMEDIATE:
        default:
          form.setFieldsValue({
            ...normalValues,
            executeTimes: detail?.executeTimes,
          });
          break;

        case ExecuteTypeEnum.TIMING:
          detail.cronExpression && setTimeCron(detail.cronExpression);
          if (detail.scheduleType !== ScheduleTypeEnum.TIMING) {
            if (detail.isAssignedCalendar) setTimingTimeType(TimingTimeTypeEnum.CALENDAR);
            else setTimingTimeType(Number(detail?.isCheckActive));
          }
          if (!detail?.scheduleType) {
            setScheduleType(ScheduleTypeEnum.CRON);
          }
          if (detail.scheduleType === ScheduleTypeEnum.TIMING) {
            form.setFieldsValue({
              ...normalValues,
              executeTimes: detail?.executeTimes,
            });
          } else if (Number(detail.isCheckActive) === TimingTimeTypeEnum.SPECIFIEDTIME) {
            form.setFieldsValue({
              ...normalValues,
              executeTimes: detail?.executeTimes,
              isCheckActive: detail.isAssignedCalendar
                ? TimingTimeTypeEnum.CALENDAR
                : detail.isCheckActive
                  ? TimingTimeTypeEnum.SPECIFIEDTIME
                  : TimingTimeTypeEnum.LONGTERM,
              // eslint-disable-next-line no-extra-boolean-cast
              timingTimeDate: !!detail.isCheckActive
                ? [moment(detail.scheduleStart), moment(detail.scheduleEnd)]
                : null,
            });
          } else {
            form.setFieldsValue({
              ...normalValues,
              executeTimes: detail?.executeTimes,
              isCheckActive: detail.isAssignedCalendar
                ? TimingTimeTypeEnum.CALENDAR
                : detail.isCheckActive
                  ? TimingTimeTypeEnum.SPECIFIEDTIME
                  : TimingTimeTypeEnum.LONGTERM,
            });
          }
          break;

        case ExecuteTypeEnum.MANUAL:
          form.setFieldsValue({
            ...normalValues,
          });
          break;
      }
    }
  }, [detail]);

  return (
    <div
      className={`${styles['task-step2']} ${detail ? styles['task-edit-steps'] : styles['task-add-steps']}`}
      style={detail ? {} : { opacity, zIndex: opacity === OpacityEnum.DISPLAY ? 999 : -1 }}>
      <Form {...taskUtil.formItemLayout2} form={form} layout="horizontal" className={styles['step-form']}>
        <Form.Item label={'执行方式'} name="executeType" initialValue={executeType}>
          {detail ? (
            <>{detail.executeType && ExecuteTypeMap[detail.executeType]}</>
          ) : (
            <Radio.Group onChange={changeExecuteType} value={executeType}>
              {/* 因为UI排序问题未能直接map */}
              <Radio value={ExecuteTypeEnum.IMMEDIATE}>{ExecuteTypeMap[ExecuteTypeEnum.IMMEDIATE]}</Radio>
              <Radio value={ExecuteTypeEnum.TIMING}>{ExecuteTypeMap[ExecuteTypeEnum.TIMING]}</Radio>
              {taskPlanType !== TaskPlanTypeEnum.ARRANGE && (
                <Radio value={ExecuteTypeEnum.MANUAL}>{ExecuteTypeMap[ExecuteTypeEnum.MANUAL]}</Radio>
              )}
            </Radio.Group>
          )}
        </Form.Item>
        {executeType === ExecuteTypeEnum.TIMING && (
          <>
            <Form.Item name="cronExpression" noStyle>
              <Input type="hidden" value={timeCron}></Input>
              <div>
                <ModalTiming
                  getTimeCron={getTimeCron}
                  getScheduleType={getScheduleType}
                  timingInfo={
                    !detail
                      ? { cronExpression: '', scheduleType }
                      : {
                          cronExpression:
                            detail?.scheduleType === ScheduleTypeEnum.MINUTES
                              ? Number(detail.fixTime)
                              : detail?.cronExpression,
                          scheduleType: detail?.scheduleType || ScheduleTypeEnum.CRON,
                        }
                  }
                />
              </div>
            </Form.Item>

            {scheduleType !== ScheduleTypeEnum.TIMING && (
              <>
                <Form.Item
                  label={TASKCENTER_TEXT.TASK_TIMING_LIMITATION_LABEL}
                  required
                  name="isCheckActive"
                  rules={[{ required: true }]}
                  initialValue={timingTimeType}
                  tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_TIMING_LIMITATION) as any}>
                  <Radio.Group onChange={changeTimingTimeType} value={timingTimeType}>
                    <Radio value={TimingTimeTypeEnum.LONGTERM}>{TimingTimeTypeMap[TimingTimeTypeEnum.LONGTERM]}</Radio>
                    <Radio value={TimingTimeTypeEnum.SPECIFIEDTIME}>
                      {TimingTimeTypeMap[TimingTimeTypeEnum.SPECIFIEDTIME]}
                    </Radio>
                    {/* <Radio value={TimingTimeTypeEnum.CALENDAR}>选择日历</Radio> */}
                  </Radio.Group>
                </Form.Item>
                {timingTimeType === TimingTimeTypeEnum.SPECIFIEDTIME && (
                  <Form.Item
                    name="timingTimeDate"
                    label={'有效时间'}
                    rules={[
                      {
                        required: true,
                        message: '请选择任务有效时间',
                      },
                    ]}>
                    <SzDatePicker dateType={SZDatePickerType.RANGEPICKER} showTime style={{ width: '100%' }} />
                  </Form.Item>
                )}
                {timingTimeType === TimingTimeTypeEnum.CALENDAR && (
                  <Form.Item required label={'选择日历'}>
                    <Row>
                      <Col span={16}>
                        <Form.Item
                          noStyle
                          name="calendarId"
                          rules={[
                            {
                              required: true,
                              message: '请选择日历',
                            },
                            {
                              message: '该日历已禁用',
                              validator(rule, value, callback) {
                                if (state.calendarId === detail?.calendarId && detail?.calendarStatus === 0) {
                                  return Promise.reject(new Error('该日历已禁用'));
                                }
                                return Promise.resolve();
                              },
                            },
                          ]}
                          initialValue={
                            detail?.calendarStatus === 0 && detail?.calendarId === state.calendarId
                              ? detail?.calendarName
                              : state.calendarId
                          }>
                          <Select
                            suffixIcon={
                              detail?.calendarStatus === 0 &&
                              detail?.calendarId === state.calendarId && <StyleText textColor="red">已禁用</StyleText>
                            }
                            onChange={(val) => {
                              state.calendarId = val;
                            }}
                            options={
                              calendarOptions.map((item) => {
                                return {
                                  label: item.name,
                                  value: item.id,
                                };
                              }) || []
                            }
                          />
                        </Form.Item>
                      </Col>
                      <Col
                        style={{
                          marginLeft: '6px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Space align="center">
                          <StyleText
                            type="link"
                            textColor={Colors.blue}
                            onClick={() => {
                              if (!state.calendarId) {
                                message.info('请选择日历');
                                return;
                              }
                              state.detailOpen = true;
                              state.createOpen = false;
                            }}>
                            预览
                          </StyleText>
                          <StyleText
                            type="link"
                            textColor={Colors.blue}
                            onClick={() => {
                              state.createOpen = true;
                              state.detailOpen = false;
                            }}>
                            创建
                          </StyleText>
                        </Space>
                      </Col>
                    </Row>
                  </Form.Item>
                )}
              </>
            )}
          </>
        )}

        {executeType === ExecuteTypeEnum.IMMEDIATE || executeType === ExecuteTypeEnum.TIMING ? (
          <Form.Item
            label={TASKCENTER_TEXT.TASK_NUMBER_OF_TIMES_PER_EXECUTION_LABEL}
            name="executeTimes"
            initialValue={1}
            rules={[
              {
                required: true,
                message: TASKCENTER_TEXT.TASK_TIP_ENTER_POSITIVE_INTEGER,
                type: 'number',
                min: 1,
              },
            ]}
            tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_NUMBER_OF_TIMES_PER_EXECUTION) as any}>
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              placeholder={TASKCENTER_TEXT.TASK_TIP_ENTER_POSITIVE_INTEGER}
            />
          </Form.Item>
        ) : (
          ''
        )}
        <Form.Item
          label={TASKCENTER_TEXT.TASK_MAX_WAITING_TIMEOUT_LABEL}
          name="maxQueueWait"
          rules={[
            {
              required: true,
              message: TASKCENTER_TEXT.TASK_TIP_ENTER_POSITIVE_INTEGER,
              type: 'number',
              min: 1,
            },
          ]}
          tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_MAX_WAITING_TIMEOUT) as any}>
          <InputNumber
            style={{ width: '100%' }}
            placeholder={TASKCENTER_TEXT.TASK_TIP_ENTER_POSITIVE_INTEGER}
            min={1}
          />
        </Form.Item>
        <Form.Item
          label={TASKCENTER_TEXT.TASK_MAX_QUEUE_NUMBER_LABEL}
          name="maxQueueNumber"
          rules={[
            {
              required: true,
              message: TASKCENTER_TEXT.TASK_TIP_ENTER_POSITIVE_INTEGER,
              type: 'number',
              min: 1,
            },
          ]}
          tooltip={RenderTipsIcon(TASKCENTER_TEXT.TASK_MAX_QUEUE_NUMBER) as any}>
          <InputNumber
            style={{ width: '100%' }}
            placeholder={TASKCENTER_TEXT.TASK_TIP_ENTER_POSITIVE_INTEGER}
            min={1}
          />
        </Form.Item>
        {/* <Form.Item
          label={TASKCENTER_TEXT.TASK_MAX_PALING_OVER_TIME}
          name="timeoutFlag"
        >
          <div>
            <Switch checked={warnCheck} onChange={warnOnChange} />
          </div>
        </Form.Item> */}
        {warnCheck && (
          <Form.Item wrapperCol={{ offset: 8 }} label="">
            运行
            <Form.Item
              name="timeout"
              noStyle
              rules={[
                {
                  required: true,
                  message: '请输入运行报警时间且为正整数',
                  type: 'number',
                  min: 1,
                },
              ]}>
              <InputNumber
                value={warnTime}
                style={{ width: '70px' }}
                onChange={(e) => {
                  // @ts-ignore
                  setWarnTime(e);
                }}
              />
            </Form.Item>
            分钟未完成进行告警
          </Form.Item>
        )}
      </Form>
      {!detail && (
        <div className={`${styles['steps-btn']}`}>
          <Button shizai-key="step2-pre" onClick={handleData.preData} className={`${styles['mr-10px']}`}>
            {'上一步'}
          </Button>
          <Button
            shizai-key="step2-next"
            type="primary"
            loading={taskPlanType === TaskPlanTypeEnum.ARRANGE ? loading : false}
            onClick={async () => {
              const validateResult = await handleData?.validateData();
              if (taskPlanType === TaskPlanTypeEnum.ARRANGE) {
                if (validateResult) {
                  setLoading(true);
                  const formData = await handleData?.setupData();
                  const newNoticeList = stepData?.noticeList
                    ?.filter(
                      (item: any) => item.noticeStatus === 0 && item.channelList?.some((el: any) => el.status === 0),
                    )
                    .map((item: any) => {
                      return {
                        ...item,
                        channelList: item?.channelList?.filter((el: any) => el.status === 0),
                        busiType: 3,
                        eventUuid: item.uuid,
                      };
                    });
                  const saveData = {
                    ...stepData,
                    ...formData,
                    noticeList: newNoticeList,
                    noticeUserList: stepData?.noticeUserList?.map((user: any) => {
                      return {
                        busiType: 3,
                        receiverId: user.id,
                      };
                    }),
                    // compatibleParam: { ...stepData, ...formData },
                  } as any;
                  // debugger;
                  await getTaskmanageSetup({
                    ...saveData,
                  });

                  setLoading(false);
                  onClose && onClose();
                  save({ taskListReload: Math.random() }), message.success('提交成功');
                }
              } else {
                validateResult && (await handleData?.nextData());
              }
            }}>
            {taskPlanType === TaskPlanTypeEnum.ARRANGE ? '提交' : '下一步'}
          </Button>
        </div>
      )}
      {/* {state.detailOpen && (
        <CalendarDetailDrawer
          id={state.calendarId}
          onOk={() => {
            state.detailOpen = false;
          }}
          onCancel={() => {
            state.detailOpen = false;
          }}
          open={state.detailOpen}
        />
      )}
      {state.createOpen && (
        <EditCalendarDrawer
          onCancel={() => {
            // state.id = '';
            state.createOpen = false;
          }}
          onOk={() => {
            // state.id = '';
            state.createOpen = false;
          }}
          open={state.createOpen}
        />
      )} */}
    </div>
  );
};

export default Step2;

@import '~antd/es/style/themes/default.less';

.wrapper {
  display: flex;
  height: 320px;
  margin: 0 auto;
  background-color: var(--modal-bg-color);
  border: 1px solid var(--modal-border-color);
  border-radius: 3px;
}

.content {
  display: flex;
  flex-direction: column;
  min-height: 350px;
}

.left {
  width: 111px;
  padding: 20px;
  border: 1px solid rgb(208 213 224 / 60%);
  border-right: 1px solid var(--modal-border-color);

  :global(.ant-radio-wrapper) {
    height: 28px;
    line-height: 28px;
  }

  :global(.ant-radio-group) {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }
}

.right {
  width: 500px;
  // flex: 1;
  padding: 20px 17px;
  border: 1px solid rgb(208 213 224 / 60%);
}

.modaltime-form {
  position: relative;
}

.selectW106 {
  width: 106px !important;
  margin: 0 8px;
}

.selectW72 {
  width: 72px !important;
  margin: 0 8px;
}

.cronCom {
  display: flex;
  align-items: center;

  :global(.ant-input) {
    width: 230px;
  }
}

.cronHelp {
  margin-top: 24px;

  p {
    margin-bottom: 6px;
    color: rgb(51 67 85 / 40%);
  }

  a {
    color: @primary-color;
  }
}

.checkbox {
  width: 100%;
  padding-bottom: 12px;

  :global(.ant-checkbox-wrapper) {
    width: 20%;
    margin-bottom: 12px;
    margin-left: 0;
    line-height: 17px;
  }
}

.title {
  margin-bottom: 12px;
}

.item {
  margin-bottom: 12px;
}

.subTitle {
  margin: 5px 0;
}

.modalBody {
  padding: 0 16px;
}

.switchWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 300px;
  height: 50px;

  span {
    margin-right: 10px;
  }

  :global(.ant-switch) {
    width: 37px;
  }
}

.timeoutWrapper {
  padding: 10px 0 12px;

  span {
    margin-right: 5px;
  }
}

.timeout {
  display: flex;
  align-items: center;
  color: #2e2e2e;
}

.timeoutTips {
  padding-top: 5px;
  color: #666;
}

.nextRunTime {
  margin-bottom: 20px;
  margin-left: 18%;
  padding-top: 12px;
  border-top: 1px solid rgb(208 213 224 / 30%);
}

.cronTips {
  color: #f15721;
}

.nextTime {
  margin-right: 5px;
}

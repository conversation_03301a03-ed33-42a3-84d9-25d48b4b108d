import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { Radio, Select, Input, Checkbox, InputNumber, Space, Form } from 'antd';
import moment from 'moment';
import { getCron } from '../../../../../../../services';
import {
  ScheduleTypeEnum,
  ScheduleTypeMap,
  WeekTypeEnum,
  WeekTypeMap,
} from '@/types/taskcenter/type';
import { TIMING_TIME_FORMAT_STRING } from '../../../../const';
import { getReduxAllStore } from '@/utils/constants/const';
import { SzSelect, SzDatePicker } from 'main/SzComponents';

import styles from './index.less';

const { Option } = Select;
let timer: any = null;

const I18N = {
  taskmanage: {
    Mobile: {
      yue: '12月',
      yue2: '11月',
      yue3: '10月',
      yue4: '9月',
      yue5: '8月',
      yue6: '7月',
      yue7: '6月',
      yue8: '5月',
      yue9: '4月',
      yue10: '3月',
      yue11: '2月',
      yue12: '1月',
    },
  },
};

const options: { label: string; value: ScheduleTypeEnum }[] = [
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.TIMING],
    value: ScheduleTypeEnum.TIMING,
  },
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.MINUTES],
    value: ScheduleTypeEnum.MINUTES,
  },
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.HOUR],
    value: ScheduleTypeEnum.HOUR,
  },
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.DAY],
    value: ScheduleTypeEnum.DAY,
  },
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.WEEK],
    value: ScheduleTypeEnum.WEEK,
  },
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.MONTH],
    value: ScheduleTypeEnum.MONTH,
  },
  {
    label: ScheduleTypeMap[ScheduleTypeEnum.CRON],
    value: ScheduleTypeEnum.CRON,
  },
];

const defaultWeekValue = [WeekTypeEnum.MON];
const defaultMonthValue = ['1'];
const defaultHourValue = 0;
const defaultMinValue = 0;
const maxMinValue = 59;
const defaultFixTime = 5;

// 给定起始值创建一定长度的数组
const createLengthArray = (length: any, startValue: any) => {
  const arr: any = [];
  new Int8Array(length).map((_, index) => {
    arr.push({
      label: `${startValue + index}`,
      value: startValue + index,
    });
    return index;
  });
  return arr;
};

const renderWeekElem = () => {
  return (
    <>
      <Checkbox value={WeekTypeEnum.MON}>
        {WeekTypeMap[WeekTypeEnum.MON]}
      </Checkbox>
      <Checkbox value={WeekTypeEnum.TUES}>
        {WeekTypeMap[WeekTypeEnum.TUES]}
      </Checkbox>
      <Checkbox value={WeekTypeEnum.WED}>
        {WeekTypeMap[WeekTypeEnum.WED]}
      </Checkbox>
      <Checkbox value={WeekTypeEnum.THURS}>
        {WeekTypeMap[WeekTypeEnum.THURS]}
      </Checkbox>
      <Checkbox value={WeekTypeEnum.FRI}>
        {WeekTypeMap[WeekTypeEnum.FRI]}
      </Checkbox>
      <Checkbox value={WeekTypeEnum.SAT}>
        {WeekTypeMap[WeekTypeEnum.SAT]}
      </Checkbox>
      <Checkbox value={WeekTypeEnum.SUN}>
        {WeekTypeMap[WeekTypeEnum.SUN]}
      </Checkbox>
    </>
  );
};
const renderMonthElem = () => {
  return (
    <>
      <Checkbox value={'1'}>{I18N.taskmanage.Mobile.yue12}</Checkbox>
      <Checkbox value={'2'}>{I18N.taskmanage.Mobile.yue11}</Checkbox>
      <Checkbox value={'3'}>{I18N.taskmanage.Mobile.yue10}</Checkbox>
      <Checkbox value={'4'}>{I18N.taskmanage.Mobile.yue9}</Checkbox>
      <Checkbox value={'5'}>{I18N.taskmanage.Mobile.yue8}</Checkbox>
      <Checkbox value={'6'}>{I18N.taskmanage.Mobile.yue7}</Checkbox>
      <Checkbox value={'7'}>{I18N.taskmanage.Mobile.yue6}</Checkbox>
      <Checkbox value={'8'}>{I18N.taskmanage.Mobile.yue5}</Checkbox>
      <Checkbox value={'9'}>{I18N.taskmanage.Mobile.yue4}</Checkbox>
      <Checkbox value={'10'}>{I18N.taskmanage.Mobile.yue3}</Checkbox>
      <Checkbox value={'11'}>{I18N.taskmanage.Mobile.yue2}</Checkbox>
      <Checkbox value={'12'}>{I18N.taskmanage.Mobile.yue}</Checkbox>
    </>
  );
};
interface TParamsType {
  yearValue?: number;
  mValue?: number;
  dayValue?: number;
  hourValue?: number;
  minValue?: number;
  secValue?: number;
  timing?: string;
}

const ModalTiming = (props: any) => {
  const { getTimeCron, getScheduleType, timingInfo } = props;

  const [radioValue, setRadioValue] = useState<ScheduleTypeEnum>(
    timingInfo?.scheduleType,
  );
  // 调度类型-定时
  const [tParams, settParams] = useState<TParamsType>({});
  // 调度类型-分钟
  const [mParams, setmParams] = useState({ exm: defaultFixTime });
  // 调度类型-小时
  const [hParams, sethParams] = useState({ exh: 1, minValue: defaultMinValue });
  // 调度类型-每天
  const [dParams, setdParams] = useState({
    hourValue: defaultHourValue,
    minValue: defaultMinValue,
  });
  // 调度类型-每周
  const [wParams, setwParams] = useState({
    hourValue: defaultHourValue,
    minValue: defaultMinValue,
    wValue: defaultWeekValue,
  });

  // 调度类型-每月
  const [MonParams, setMonParams] = useState({
    mValue: defaultMonthValue,
    hourValue: defaultHourValue,
    minValue: defaultMinValue,
    wValue: defaultWeekValue,
  });

  // 调度类型-高级
  const [cronParams, setcronParams] = useState({ cronValue: '' });

  type MessageType = {
    futureTimes: any[];
    msg: string;
  };
  const initSendMessage: MessageType = { futureTimes: [], msg: '' };
  const [sendMessage, setSendMessage] = useState<MessageType>({
    ...initSendMessage,
  });

  // 当调度值发生变化的时候存储变化的值
  const [valuePm, setValuePm] = useState('');
  const changetParamsFn = useCallback(
    (value: any) => {
      if (!value) {
        settParams({
          timing: undefined,
        });

        return;
      }
      const momentValue = moment(value);
      const yearValue = momentValue.year();
      const mValue = momentValue.month() + 1;
      const dayValue = momentValue.date();
      const hourValue = momentValue.hours();
      const minValue = momentValue.minutes();
      const secValue = momentValue.seconds();
      settParams({
        ...tParams,
        secValue,
        minValue,
        hourValue,
        dayValue,
        mValue,
        yearValue,
        timing: `${yearValue}-${mValue}-${dayValue} ${hourValue}:${minValue}:${secValue}`,
      });
    },
    [tParams],
  );
  const changemParamsFn = useCallback(
    (key: string, value: any) => {
      setmParams({
        ...mParams,
        [key]: value,
      });
    },
    [mParams],
  );

  const changehParamsFn = useCallback(
    (key: string, value: any) => {
      sethParams({
        ...hParams,
        [key]: value,
      });
    },
    [hParams],
  );

  const changedParamsFn = useCallback(
    (key: string, value: any) => {
      setdParams({
        ...dParams,
        [key]: value,
      });
    },
    [dParams],
  );

  const changewParamsFn = useCallback(
    (key: string, value: any) => {
      setwParams({
        ...wParams,
        [key]: value,
      });
    },
    [wParams],
  );

  const changeMParamsFn = useCallback(
    (key: string, value: any) => {
      setMonParams({
        ...MonParams,
        [key]: value,
      });
    },
    [MonParams],
  );

  const changecronParamsFn = useCallback(
    (key: string, value: any) => {
      setcronParams({
        ...cronParams,
        [key]: value,
      });
    },
    [cronParams],
  );

  const [cronExpression, setCronExpression] = useState('');
  const getCronList = async () => {
    const data = await getCron({
      cronText: cronExpression,
    });
    setSendMessage(data);
  };

  const db = (fn: any, delay: any) => {
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(fn, delay);
    };
  };
  useEffect(() => {
    db(getCronList, 1000)();
  }, [cronExpression]);

  const { tips } = useMemo(() => {
    let params: any = null;

    switch (radioValue) {
      case ScheduleTypeEnum.TIMING:
        params = { ...tParams };
        if (JSON.stringify(params) === '{}') {
          params.cron = '';
        } else {
          params.cron = `${params?.secValue} ${params?.minValue} ${params?.hourValue} ${params?.dayValue} ${params?.mValue} ? ${params?.yearValue}-${params?.yearValue}`;
        }
        params.tips = '';
        break;
      case ScheduleTypeEnum.MINUTES:
        params = { ...mParams };
        params.cron = params.exm;
        params.localCron = `0 */${params.exm} * * * ?`;
        params.tips = `每间隔 ${params.exm || defaultFixTime} 分钟循环执行`;
        break;
      case ScheduleTypeEnum.HOUR:
        params = hParams;
        params.cron = `0 ${params.minValue} */${params.exh} * * ?`;
        params.tips = `每间隔 ${params.exh} 小时循环，在每小时的${
          params.minValue || defaultMinValue
        }分开始执行`;
        break;
      case ScheduleTypeEnum.DAY:
        params = dParams;
        params.cron = `0 ${params.minValue} ${params.hourValue} * * ?`;
        params.tips = `每天定时循环，在每天的 ${params.hourValue} 点 ${
          params.minValue || defaultMinValue
        } 分开始执行`;
        break;
      case ScheduleTypeEnum.WEEK:
        params = wParams;
        params.cron = `0 ${params.minValue} ${params.hourValue} ? * ${
          params.wValue?.length > 0 ? params.wValue?.join(',') : ''
        }`;
        params.tips = `每周的 ${params.wValue
          ?.map((item: any) => WeekTypeMap[item as keyof typeof WeekTypeMap])
          ?.join('、')} 循环，在当天的 ${params.hourValue} 点 ${
          params.minValue || defaultMinValue
        } 分开始执行`;
        break;
      case ScheduleTypeEnum.MONTH:
        params = MonParams;
        params.cron = `0 ${params.minValue} ${params.hourValue} ? ${
          params.mValue
        } ${params.wValue?.length > 0 ? params.wValue?.join(',') : '*'}`;

        params.tips = `每年的 ${params.mValue?.map(
          (item: any) => item,
        )}月 循环，当月的每个 ${params.wValue
          ?.map((item: any) => WeekTypeMap[item as keyof typeof WeekTypeMap])
          ?.join('、')} 的 ${params.hourValue} 点 ${
          params.minValue || defaultMinValue
        } 分开始执行`;

        break;
      case ScheduleTypeEnum.CRON:
        params = cronParams;
        params.cron = params.cronValue;
        params.tips = '';
        break;
      default:
        params = mParams;
        params.tips = '';
    }

    if (radioValue === ScheduleTypeEnum.MINUTES) {
      setCronExpression(params.localCron);
    } else {
      setCronExpression(params.cron);
    }
    setValuePm(params.cron);

    return {
      ...params,
    };
  }, [
    radioValue,
    tParams,
    mParams,
    hParams,
    dParams,
    wParams,
    MonParams,
    cronParams,
  ]);

  /** 改变调度方式 */
  const onChange = useCallback(
    (e: any) => {
      if (radioValue !== e.target.value) {
        setRadioValue(e.target.value);
      }
    },
    [radioValue, setRadioValue],
  );

  /** 定时调度类型发生变化时通知父组件 */
  useEffect(() => {
    getScheduleType(radioValue);
    setSendMessage({ ...initSendMessage });
  }, [radioValue]);

  /** 定时调度值发生变化时通知父组件 */
  useEffect(() => {
    getTimeCron(valuePm);
  }, [valuePm]);

  /** 定时调度初始值 */
  const init = useCallback(() => {
    if (timingInfo) {
      const cronType = timingInfo.scheduleType;
      const cronExpressionVal = timingInfo.cronExpression;

      setRadioValue(cronType);
      if (!cronExpressionVal) {
        return;
      }
      if (cronType === ScheduleTypeEnum.MINUTES) {
        setmParams({ exm: Number(cronExpressionVal) });
      } else if (cronType === ScheduleTypeEnum.CRON) {
        setcronParams({ cronValue: cronExpressionVal });
      } else {
        const cronArray = cronExpressionVal?.split(' ');
        if (cronType === ScheduleTypeEnum.TIMING && cronArray.length !== 7)
          return;
        const secondsValue = cronArray[0];
        const minuteValue = cronArray[1];
        let hourValue = cronArray[2];
        const dayValue = cronArray[3];
        let monthValue = cronArray[4];
        let weekValue = cronArray[5];
        let yearValue = cronArray[6];
        switch (cronType) {
          case ScheduleTypeEnum.TIMING:
            // eslint-disable-next-line prefer-destructuring
            yearValue = yearValue?.split('-')[0];
            settParams({
              yearValue,
              mValue: monthValue,
              dayValue,
              hourValue,
              minValue: minuteValue,
              secValue: secondsValue,
              timing: `${yearValue}-${monthValue}-${dayValue} ${hourValue}:${minuteValue}:${secondsValue}`,
            });
            break;
          case ScheduleTypeEnum.HOUR:
            // eslint-disable-next-line prefer-destructuring
            hourValue = hourValue?.split('/')[1];
            sethParams({
              exh: hourValue,
              minValue: minuteValue,
            });
            break;
          case ScheduleTypeEnum.DAY:
            setdParams({
              hourValue,
              minValue: minuteValue,
            });
            break;
          case ScheduleTypeEnum.WEEK:
            weekValue = weekValue?.split(',');
            setwParams({
              hourValue,
              minValue: minuteValue,
              wValue: weekValue,
            });
            break;
          case ScheduleTypeEnum.MONTH:
            weekValue = weekValue?.split(',');
            monthValue = monthValue?.split(',');
            setMonParams({
              mValue: monthValue,
              hourValue,
              minValue: minuteValue,
              wValue: weekValue,
            });
            break;
          default:
            break;
        }
      }
    }
  }, [timingInfo]);

  useEffect(() => init(), []);

  return (
    <div className={styles.content}>
      <div className={styles.wrapper}>
        <div className={styles.left}>
          <Radio.Group
            options={options}
            onChange={onChange}
            value={radioValue}
          />
        </div>
        <div className={styles.right}>
          <Form className={styles['modaltime-form']}>
            {radioValue === ScheduleTypeEnum.TIMING && (
              <Form.Item noStyle>
                <Space>
                  执行时间
                  <SzDatePicker
                    onChange={(val: any) => changetParamsFn(val)}
                    showNow={false}
                    format={TIMING_TIME_FORMAT_STRING}
                    value={
                      radioValue === ScheduleTypeEnum.TIMING && tParams?.timing
                        ? moment(tParams.timing)
                        : undefined
                    }
                    disabledDate={(curDate: any) =>
                      curDate && curDate < moment().subtract(1, 'day')
                    }
                    showTime={{ defaultValue: moment('00:00:00', 'HH:mm:ss') }}
                  />
                </Space>
              </Form.Item>
            )}
            {radioValue === ScheduleTypeEnum.MINUTES && (
              <Form.Item noStyle>
                <div>
                  毎
                  <InputNumber
                    min={1}
                    max={maxMinValue}
                    value={
                      radioValue === ScheduleTypeEnum.MINUTES && mParams?.exm
                        ? mParams.exm
                        : defaultFixTime
                    }
                    className={styles.selectW106}
                    onChange={(val: any) =>
                      changemParamsFn('exm', val || defaultFixTime)
                    }
                  />
                  分钟
                </div>
              </Form.Item>
            )}
          </Form>
          {radioValue === ScheduleTypeEnum.HOUR && (
            <Form.Item noStyle>
              <div>
                每间隔
                <SzSelect
                  value={
                    radioValue === ScheduleTypeEnum.HOUR && hParams?.exh
                      ? hParams.exh
                      : 1
                  }
                  onChange={(val) => changehParamsFn('exh', val || 1)}
                  className={styles.selectW106}
                >
                  {createLengthArray(12, 1).map((item: any) => (
                    <Option key={item.value} value={item.value}>
                      {item.label}
                    </Option>
                  ))}
                </SzSelect>
                小时 在
                {
                  <InputNumber
                    min={defaultMinValue}
                    max={maxMinValue}
                    value={
                      radioValue === ScheduleTypeEnum.HOUR && hParams?.minValue
                        ? hParams.minValue
                        : defaultMinValue
                    }
                    className={styles.selectW106}
                    onChange={(val) => {
                      changehParamsFn('minValue', val || defaultMinValue);
                    }}
                  />
                }
                分钟
              </div>
            </Form.Item>
          )}

          {radioValue === ScheduleTypeEnum.DAY && (
            <Form.Item noStyle>
              <div>
                每天在
                <SzSelect
                  value={
                    radioValue === ScheduleTypeEnum.DAY && dParams?.hourValue
                      ? dParams.hourValue
                      : defaultHourValue
                  }
                  onChange={(val) =>
                    changedParamsFn('hourValue', val || defaultHourValue)
                  }
                  className={styles.selectW106}
                >
                  {createLengthArray(24, 0).map((item: any) => (
                    <Option key={item.value} value={item.value}>
                      {item.label}
                    </Option>
                  ))}
                </SzSelect>
                时
                {
                  <InputNumber
                    min={defaultMinValue}
                    max={maxMinValue}
                    value={
                      radioValue === ScheduleTypeEnum.DAY && dParams?.minValue
                        ? dParams.minValue
                        : 0
                    }
                    className={styles.selectW106}
                    onChange={(val) =>
                      changedParamsFn('minValue', val || defaultMinValue)
                    }
                  />
                }
                分
              </div>
            </Form.Item>
          )}
          {radioValue === ScheduleTypeEnum.WEEK && (
            <Form.Item noStyle>
              <div className={styles.title}>每周的</div>
              {
                <Checkbox.Group
                  style={{ width: '100%' }}
                  onChange={(val) =>
                    changewParamsFn('wValue', val || defaultWeekValue)
                  }
                  value={
                    radioValue === ScheduleTypeEnum.WEEK && wParams?.wValue
                      ? wParams.wValue
                      : defaultWeekValue
                  }
                  className={styles.checkbox}
                >
                  {renderWeekElem()}
                </Checkbox.Group>
              }
              <div>
                在当天
                {
                  <SzSelect
                    value={
                      radioValue === ScheduleTypeEnum.WEEK && wParams?.hourValue
                        ? wParams.hourValue
                        : defaultHourValue
                    }
                    onChange={(val) => changewParamsFn('hourValue', val)}
                    className={styles.selectW106}
                  >
                    {createLengthArray(24, 0).map((item: any) => (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </SzSelect>
                }
                {'时'}
                {
                  <InputNumber
                    min={defaultMinValue}
                    max={maxMinValue}
                    value={
                      radioValue === ScheduleTypeEnum.WEEK && wParams?.minValue
                        ? wParams.minValue
                        : defaultMinValue
                    }
                    className={styles.selectW106}
                    onChange={(val) => {
                      changewParamsFn('minValue', val || defaultMinValue);
                    }}
                  />
                }
                {'分'}
              </div>
            </Form.Item>
          )}
          {radioValue === ScheduleTypeEnum.MONTH && (
            <Form.Item noStyle>
              <div className={styles.title}>{'毎年'}</div>
              <Checkbox.Group
                style={{ width: '100%' }}
                onChange={(val) =>
                  changeMParamsFn('mValue', val || defaultMonthValue)
                }
                value={
                  radioValue === ScheduleTypeEnum.MONTH && MonParams?.mValue
                    ? MonParams.mValue
                    : defaultMonthValue
                }
                className={styles.checkbox}
              >
                {renderMonthElem()}
              </Checkbox.Group>
              <div className={styles.title}>{'每周的'}</div>
              <Checkbox.Group
                style={{ width: '100%' }}
                onChange={(val) =>
                  changeMParamsFn('wValue', val || defaultWeekValue)
                }
                value={
                  radioValue === ScheduleTypeEnum.MONTH && MonParams?.wValue
                    ? MonParams.wValue
                    : defaultWeekValue
                }
                className={styles.checkbox}
              >
                {renderWeekElem()}
              </Checkbox.Group>
              <div>
                {'在当天'}
                {
                  <SzSelect
                    value={
                      radioValue === ScheduleTypeEnum.MONTH &&
                      MonParams?.hourValue
                        ? MonParams.hourValue
                        : defaultHourValue
                    }
                    onChange={(val) =>
                      changeMParamsFn('hourValue', val || defaultHourValue)
                    }
                    className={styles.selectW106}
                  >
                    {createLengthArray(24, 0).map((item: any) => (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </SzSelect>
                }
                {'时'}
                {
                  <InputNumber
                    min={defaultMinValue}
                    max={maxMinValue}
                    value={
                      radioValue === ScheduleTypeEnum.MONTH &&
                      MonParams?.minValue
                        ? MonParams.minValue
                        : defaultMinValue
                    }
                    className={styles.selectW106}
                    onChange={(val) =>
                      changeMParamsFn('minValue', val || defaultMinValue)
                    }
                  />
                }
                {'分'}
              </div>
            </Form.Item>
          )}
          {radioValue === ScheduleTypeEnum.CRON && (
            <Form.Item noStyle>
              <div className={styles.cronCom}>
                <Space>
                  <div>{'Cron表达式'}</div>
                  <Input
                    type="text"
                    placeholder={'请输入cron表达式'}
                    value={
                      radioValue === ScheduleTypeEnum.CRON &&
                      cronParams?.cronValue
                        ? cronParams.cronValue
                        : ''
                    }
                    onChange={(e: any) =>
                      changecronParamsFn('cronValue', e.target.value)
                    }
                  />
                </Space>
              </div>
              <div className={styles.cronHelp}>
                <p>{'Cron表达式是进行定时设置的一种语法。'}</p>
                {
                  <a
                    target="_blank"
                    href={
                      getReduxAllStore()?.global?.constJson
                        ?.cronHelpDocumentLink ||
                      'https://rpa-college.ai-indeed.com/document/16cd32b0d56211ebb85855a8602d9b4c'
                    }
                    rel="noreferrer"
                  >
                    {'使用说明'}
                  </a>
                }
              </div>
            </Form.Item>
          )}
        </div>
      </div>
      <div className={styles.nextRunTime}>
        {tips && <div className={styles.cronTips}>{tips}</div>}
        {!!sendMessage.msg && radioValue === ScheduleTypeEnum.CRON && (
          <div className={styles.cronTips}>{sendMessage.msg}</div>
        )}
        {radioValue !== ScheduleTypeEnum.TIMING &&
          radioValue !== ScheduleTypeEnum.MINUTES && (
            <div className={styles.nextTimeList}>
              <div>{'预计最近3次执行时间：'}</div>
              <div>
                {sendMessage.futureTimes
                  ? sendMessage.futureTimes?.length > 0 &&
                    sendMessage.futureTimes?.map((item: any) => (
                      <span key={item} className={styles.nextTime}>
                        {item};
                      </span>
                    ))
                  : '无'}
              </div>
            </div>
          )}
      </div>
    </div>
  );
};

export { ModalTiming };

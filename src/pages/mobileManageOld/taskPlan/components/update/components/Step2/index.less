@import '~@/assets/styles/variable.less';

.task-step2 {
  min-height: 100%;

  /** 新增 */
  &.task-add-steps {
    .step-form {
      max-width: 750px;
      margin: 20px auto 100px;
    }

    .steps-btn {
      position: fixed;
      bottom: 0;
      width: calc(100vw - @menu-width - 96px);
      padding: 10px 0;
      text-align: center;
      background: #fff;
      border-top: 1px solid #dee6ee;

      .mr-10px {
        margin-right: 10px;
      }
    }
  }

  /** 编辑 */
  &.task-edit-steps {
    display: flex;
    justify-content: center;
    padding-bottom: 100px;
    overflow-y: auto;

    .step-form {
      width: 750px;
    }
  }
}

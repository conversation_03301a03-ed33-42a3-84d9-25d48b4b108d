@import '~antd/es/style/themes/default.less';

/** 公共部分 */
.update-task {
  .title {
    display: flex;
    width: 100%;
    padding: 5px 18px;
    align-items: flex-start;
    line-height: 32px;
    background: #ecfcfd;
    border-radius: 4px;

    .icon {
      margin-right: 5px;
      padding-top: 4px;
      color: @primary-color;
      font-size: 16px;
    }

    h3 {
      margin: 0;
      color: @primary-color;
      font-weight: 400;
      font-size: 13px;
      line-height: 22px;
      white-space: nowrap;
    }
  }

  :global {
    .ant-drawer-body {
      display: flex;
      flex: 1;
      padding-bottom: 0;
    }
  }
}

/** 新增 */
.task-add-card {
  width: 100%;
  height: 100%;

  .steps {
    max-width: 1000px;
    margin: 16px auto;
  }

  .step-container {
    position: relative;
    flex-grow: 1;
    width: 100%;

    & > div {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: scroll;
    }
  }

  .task-divider {
    margin: 20px 0 0;
    border-color: #dee6ee;
  }

  :global {
    .ant-card-body {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 0 24px;
    }
  }
}

/** 编辑 */
.task-edit-drawer {
  .edit-con {
    width: 100%;
    height: 100%;

    .task-edit-card {
      display: flex;
      flex-direction: column;
      height: 100%;

      .content {
        flex: 1;
        height: 100%;
        margin: 10px 0;
        border: 1px solid #dee6ee;
        border-radius: 4px;

        :global {
          .ant-tabs {
            height: 100%;
          }

          .ant-tabs-content-holder {
            padding-bottom: 20px;
            overflow: auto;
          }
        }
      }

      :global {
        .ant-tabs-nav-wrap {
          justify-content: center;
        }

        .ant-tabs-content {
          height: 100%;
        }
      }
    }

    :global {
      .ant-spin-nested-loading {
        height: 100%;
      }

      .ant-spin-container {
        height: 100%;
      }
    }
  }

  .commit-btn-layer {
    text-align: center;
  }

  :global {
    .ant-drawer-body {
      overflow: hidden;
    }
  }
}

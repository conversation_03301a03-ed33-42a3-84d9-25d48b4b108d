import React, { useState, useEffect, useCallback } from 'react';
import { Card, Steps, Divider, Drawer } from 'antd';
import Step1 from './components/Step1';
import Step2 from './components/Step2';
import Step3 from './components/Step3';
import { IconFont } from 'main/MyIcon';
import { StepNumberEnum, OpacityEnum, TaskPlanTypeEnum, TaskPlanTypeEnumMap } from '@/types/taskcenter/type';
import { useTaskUpdateModel } from '../store/update';
import styles from './index.less';

const { Step } = Steps;

type AddStepProps = {
  /** 通过流程快速创建 */
  curProcess?: any;
  /** 是否展示新建弹窗 */
  visible?: boolean;
  /** 是否是任务组， 不传则默认为false */
  isGroup?: boolean;
  taskPlanType: TaskPlanTypeEnum;
  /** 当前正在正在操作的步骤 */
  stepNumber?: StepNumberEnum;
  /** 默认流程 */
  processDefault?: string[];
  /** 流程默认渠道 */
  defaultChannel?: number;
  /** 关闭新建弹窗 */
  onClose: (isUpdate?: boolean) => void;
};

const getCurStep = (current?: StepNumberEnum) => {
  switch (current) {
    case StepNumberEnum.ONE:
    default:
      return { step: StepNumberEnum.ONE };
    case StepNumberEnum.TWO:
      return { step: StepNumberEnum.TWO };
    case StepNumberEnum.THREE:
      return { step: StepNumberEnum.THREE };
  }
};

const AddTaskEntry = (props: AddStepProps) => {
  const {
    visible = false,
    isGroup = false,
    processDefault = [],
    taskPlanType = TaskPlanTypeEnum.SINGLE,
    onClose,
    curProcess,
    defaultChannel = 0,
  } = props;

  // 目前正在操作的步骤
  const [currentStep, setCurrentStep] = useState<StepNumberEnum>(StepNumberEnum.ONE);
  const { stepNumber, resetStepInfo } = useTaskUpdateModel();
  /** 关闭弹窗以后的处理 */
  const onCloseHandle = useCallback(() => {
    onClose();
  }, [onClose]);

  useEffect(() => {
    const { step } = getCurStep(stepNumber);
    setCurrentStep(step);
  }, [stepNumber]);

  useEffect(() => {
    if (visible) {
      resetStepInfo();
    }
  }, [visible]);

  return (
    <Drawer
      className={styles['update-task']}
      title={`新建${TaskPlanTypeEnumMap[taskPlanType]}`}
      width={`calc(100vw - 208px)`}
      open={visible}
      onClose={onCloseHandle}
      footer={null}
      destroyOnClose>
      <Card bordered={false} className={styles['task-add-card']}>
        <>
          <Steps current={currentStep} className={styles.steps}>
            <Step title={'选择流程'} />
            <Step title={'执行方式'} />
            {taskPlanType !== TaskPlanTypeEnum.ARRANGE && <Step title={'分配机器人'} />}
          </Steps>
          <Divider className={styles['task-divider']} />
          <div className={styles['step-container']}>
            <Step1
              taskPlanType={taskPlanType}
              curProcess={curProcess}
              isGroup={isGroup}
              processDefault={processDefault}
              defaultChannel={defaultChannel}
              opacity={currentStep === StepNumberEnum.ONE ? OpacityEnum.DISPLAY : OpacityEnum.HIDDEN}
            />
            <Step2
              taskPlanType={taskPlanType}
              onClose={onCloseHandle}
              opacity={currentStep === StepNumberEnum.TWO ? OpacityEnum.DISPLAY : OpacityEnum.HIDDEN}
            />
            {taskPlanType !== TaskPlanTypeEnum.ARRANGE && (
              <Step3
                onClose={onCloseHandle}
                opacity={currentStep === StepNumberEnum.THREE ? OpacityEnum.DISPLAY : OpacityEnum.HIDDEN}
              />
            )}
          </div>
        </>
      </Card>
    </Drawer>
  );
};

export default AddTaskEntry;

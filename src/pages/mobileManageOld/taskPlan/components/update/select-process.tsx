import { useRequest } from 'ahooks';
import { TreeSelect } from 'antd';
import * as API from '../../../services2';

export const SelectProcess = () => {
  // const { data } = useRequest(() => API.getAppAndAppVersionList({ processChannels: [0], taskPlanType: 0 }), {});

  const mockData = [
    {
      modifyTime: '2025-06-21 14:20:05',
      detailUsedUUID: '1935243635838349313',
      versions: [
        {
          processVersionLabel: 0,
          author: '',
          detailUUID: '1935243635838349313',
          humanRobotInteractiveType: false,
          detailId: '1935243635838349313',
          mainUUID: '1935243635251146753',
          runSystem: '',
          mainId: '1935243635251146753',
          version: '0.0.1',
        },
      ],
      author: '',
      humanRobotInteractiveType: false,
      name: '手机机器人任务测试',
      mainUUID: '1935243635251146753',
      runSystem: '',
      mainId: '1935243635251146753',
    },
  ];
  return (
    <TreeSelect
      showSearch
      style={{ width: '100%' }}
      placeholder={'请选择流程'}
      onChange={(value: any) => {}}
      treeDefaultExpandAll
      treeData={}
    />
  );
};

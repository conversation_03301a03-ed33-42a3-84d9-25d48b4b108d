import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import TaskApi from '../../../services';

import { StepNumberEnum } from '@/types/taskcenter/type';

export const TaskUpdateModelNameSpace = 'TASKUPDATE';

export interface TaskUpdateModelState {
  /** 用来记录创建任务(组)的步骤 */
  stepNumber?: StepNumberEnum;
  /** 用来记录每一个步骤将要发送到后台的数据 */
  stepData?: any;
  /** 编辑任务详情 */
  taskDetailData?: any;
  /** 触发父组件列表状态更新，其实就是一个随机数 */
  taskListReload?: number;
}

export type Actions = {
  saveStepFormData: (payload: any) => void;
  saveCurrentStep: (params: any) => void;
  resetStepInfo: () => void;
  save: (params: any) => void;
  resetTaskDetailData: () => void;
  resetData: () => void;
  queryEditTaskmanageDetail: (params: string) => void;
  saveEditTask: (params: string) => void;
};

const initStepState = {
  stepNumber: StepNumberEnum.ONE,
  stepData: {},
};

const initTaskDetailData = {
  taskDetailData: {},
};
const initState = {
  selectAppInfo: { appId: null, appVersionId: null },
  ...initStepState,
  ...initTaskDetailData,
};

export const useTaskUpdateModel = create<TaskUpdateModelState & Actions>()(
  devtools(
    immer<TaskUpdateModelState & Actions>((set) => ({
      ...initState,
      saveStepFormData(payload) {
        set((state) => ({
          ...state,
          stepData: {
            ...state.stepData,
            ...payload,
          },
        }));
      },
      saveCurrentStep(params: any) {
        set((state) => ({
          ...state,
          stepNumber: params,
        }));
      },
      resetStepInfo() {
        set((state) => ({
          ...state,
          ...initStepState,
        }));
      },
      save(payload) {
        set((state) => ({ ...state, ...payload }));
      },
      resetTaskDetailData() {
        set((state) => ({
          ...state,
          ...initTaskDetailData,
        }));
      },
      resetData() {
        return initState;
      },
      async queryEditTaskmanageDetail(id: string) {
        const result = await TaskApi.getEditTaskmanageDetail(id);
        set((state) => ({ ...state, taskDetailData: result }));

        return result;
      },
      async saveEditTask(params: any) {
        const result = await TaskApi.saveEditTask(params);

        return result;
      },
    })),
    {
      name: 'useTaskUpdateModel',
    },
  ),
);

import { AppChannelEnum } from '@/types/application/type';
import type { CommonListType } from '@/types/interface';
import type { AppDetailType } from '@/types/taskcenter/interface';
import { TaskPlanTypeEnum } from '@/types/taskcenter/type';
import { Tag } from 'antd';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import AppApi from '../../../services2';
import AppMarketApi from '../../services/marketcenter/application';
import AppManageApi from '../../services/resourcecenter/processmanage/index';

export interface ApplicationFormModelState {
  /** 流程列表 */
  appList?: CommonListType[];
  /** 流程版本列表 */
  appVersionList?: CommonListType[];
  /** 流程与版本的映射object */
  appVersionsInApp?: Record<string, unknown>;
  /** 流程详情 */
  appDetail?: AppDetailType;
  /** 选中的流程信息 */
  selectAppInfo?: {
    /** 选中的流程id */
    appId?: string | null;
    /** 选中的流程版本id */
    appVersionId?: string | null;
    /** 手动切换流程时候的记录，主要目的是在前后选择同一个流程的时候也触发向后台发起请求 */
    updateAppIdTime?: string | null;
  };
}

const initialState: ApplicationFormModelState = {
  appList: [],
  appVersionList: [],
  appDetail: {},
};

const initAppState = {
  selectAppInfo: {
    appId: null,
    appVersionId: null,
    updateAppIdTime: null,
    handlerType: 1,
  },
  ...initialState,
};

interface Actions {
  save: (payload: any) => any;
  queryAppAndAppVersionListByProcessChannel: (payload: any) => any;
  queryAppManageDetail: (payload: any) => any;
  queryAppMarketDetail: (payload: any) => any;
  resetAppData: () => void;
  resetData: () => void;
}

export const useAppStore = create<ApplicationFormModelState & Actions>()(
  devtools(
    immer<ApplicationFormModelState & Actions>((set) => ({
      ...initialState,

      save(payload: any) {
        set((state) => ({ ...state, ...payload }));
      },
      resetAppData() {
        set((state) => ({ ...state, ...initialState }));
      },
      resetData() {
        set((state) => ({ ...state, ...initAppState }));
      },
      async queryAppAndAppVersionListByProcessChannel(params) {
        const { taskPlanType } = params;
        let result = [];
        const appVersionsInApp: Record<string, unknown> = {};
        if (taskPlanType && taskPlanType === TaskPlanTypeEnum.ARRANGE) {
          result = await AppApi.getFlowArrangeList();
        } else {
          result = await AppApi.getAppAndAppVersionList(params);

          if (params?.processChannels?.length === 1) {
            if (params.processChannels[0] === AppChannelEnum.APPMANAGEMENT) {
              result = result?.processMainList || [];
            }
            if (params.processChannels[0] === AppChannelEnum.APPMARKET) {
              result = result?.applicationMainList || [];
            }
          }
          if (params?.processChannels?.length === 2) {
            result = [...result?.applicationMainList, ...result?.applicationMainList];
          }
        }

        const appList = (result || []).map((item: any) => {
          return {
            value: item.mainUUID,
            title: item.name,
            disabled: true,
            children: item.versions?.map((child: any) => {
              const title = (
                <>
                  <span>
                    {item.name} / {child.version}
                  </span>
                  {child.humanRobotInteractiveType && (
                    <Tag style={{ marginLeft: '8px' }} color="processing">
                      人机任务
                    </Tag>
                  )}
                  {item.author && (
                    <span
                      style={{
                        marginLeft: '10px',
                        marginBottom: '-6px',
                        color: 'rgb(51 67 85 / 30%)',
                      }}>
                      {' '}
                      {item.author}
                    </span>
                  )}
                </>
              );
              return {
                ...child,
                name: `${item.name}/${child.version}${item.author}${child.humanRobotInteractiveType ? '人机任务' : ''}`,
                value: child.detailUUID,
                title: title,
                humanRobotInteractiveType: child.humanRobotInteractiveType,
              };
            }),
            ...item,
          };
        });
        set((state) => ({ ...state, appList, appVersionsInApp }));

        return { appList, appVersionsInApp };
      },
      async queryAppManageDetail(processDetailUuid: string) {
        const result = await AppManageApi.getAppManageDetail(processDetailUuid);
        set((state) => ({ ...state, appDetail: result }));
        return result;
      },

      // 获取流程共享流程详情
      async queryAppMarketDetail(applicationDetailUUID: string) {
        const result = await AppMarketApi.getAppMarketDetail(applicationDetailUUID);
        set((state) => ({ ...state, appDetail: result })); // 都是appDetails？？？

        return result;
      },
    })),
    {
      name: 'useAppStore',
    },
  ),
);

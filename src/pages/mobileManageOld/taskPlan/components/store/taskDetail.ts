import TaskServiceApi from '../../../services';
import { PAGINATION_DEFAULT_INFO } from '@/utils/constants/const';
import type {
  QueryWorkListType,
  WorkListType,
} from '@/types/taskcenter/interface';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export const TaskDetailModelNameSpace = 'OTHERSTASKDETAIL';
type Reducer = () => void;

export interface TaskWorkListType {
  records?: WorkListType[];
  total?: string;
  current?: number;
  size?: number;
  searchForm?: QueryWorkListType;
}
export interface TaskDetailModelState {
  workList: TaskWorkListType;
}
export interface TaskDetailModelType {
  saveWorkList: (params: any) => any;
  resetWorkListPaginationData: Reducer;
  resetWorkListData: Reducer;
  queryWorkList: (params: any) => Promise<any>;
}

const initPaginationData = {
  ...PAGINATION_DEFAULT_INFO,
};
const initWorkListState = {
  records: [],
  total: '0',
  jobUuid: null,
  ...initPaginationData,
};

const initState = {
  workList: { ...initWorkListState },
};

export const useTaskDetailStore = create<
  TaskDetailModelState & TaskDetailModelType
>()(
  devtools(
    immer<TaskDetailModelState & TaskDetailModelType>((set) => ({
      ...initState,
      resetWorkListData() {
        set((state) => ({ ...state, workList: { ...initWorkListState } }));
      },
      resetWorkListPaginationData() {
        set((state) => ({
          ...state,
          workList: { ...state.workList, ...initPaginationData },
        }));
      },
      saveWorkList(payload) {
        set((state) => ({
          ...state,
          workList: { ...state.workList, ...payload },
        }));
      },
      async queryWorkList(params: any) {
        const result = await TaskServiceApi.getWorkList(params);
        set((state) => ({ ...state, appDetail: result }));

        return result;
      },
    })),
    {
      name: 'useTaskDetailStore',
    },
  ),
);

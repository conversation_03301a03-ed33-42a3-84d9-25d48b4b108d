@import '~antd/es/style/themes/default.less';

.task-drawer {
  .ant-drawer-body {
    padding: 0;
  }

  h4 {
    height: 20px;
    margin-bottom: 20px;
    color: #334355;
  }

  .detail-part1 {
    .icon {
      margin-left: 5px;
      color: @primary-color;
      cursor: pointer;
    }
  }

  .item-info {
    margin-bottom: 11px;
    color: #334355;
    font-weight: 400;
    font-size: 14px;
  }

  .detail-part2 {
    padding: 8px 0 20px;

    .ant-tabs-nav-wrap {
      padding-left: 30px;
    }

    .ant-tabs-content-holder {
      padding: 4px 30px 20px;
    }

    .task-info {
      .application-part {
        margin-bottom: 30px;

        h4 {
          margin-bottom: 12px;
        }
      }

      .ant-table.ant-table-middle .ant-table-title,
      .ant-table.ant-table-middle .ant-table-footer,
      .ant-table.ant-table-middle .ant-table-thead > tr > th,
      .ant-table.ant-table-middle .ant-table-tbody > tr > td,
      .ant-table.ant-table-middle tfoot > tr > th,
      .ant-table.ant-table-middle tfoot > tr > td {
        padding: 7px 8px;
      }

      .execute-part {
        margin-bottom: 30px;
      }
    }
  }

  .tab-statistics {
    .ant-tabs-content-holder {
      padding: 0 !important;
    }
  }
}

.mb20 {
  margin-bottom: 20px;
}

.item-info {
  margin-bottom: 11px;
  color: #334355;
  font-weight: 400;
  font-size: 14px;
}

.d-flex {
  display: flex;
  justify-content: center;
  align-items: center;

  .flex-2 {
    flex: 2;
  }

  .flex-8 {
    flex: 8;
  }
}

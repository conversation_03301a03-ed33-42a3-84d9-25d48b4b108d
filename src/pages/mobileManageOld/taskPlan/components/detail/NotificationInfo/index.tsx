import { useState, useEffect } from 'react';
import { Row, Switch, Checkbox, Typography, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import './index.less';

const { Paragraph } = Typography;

const NotificationInfo = (props: any) => {
  const { value } = props;
  const [activeUuid, setActiveUuid] = useState();
  const [channelList, setChannelList] = useState<any>([]);
  const [isShowRight, setShowRight] = useState<boolean>(false);

  const checkboxValue = (channelList || []).map((item: any) => item.uuid);

  useEffect(() => {
    if (value && value.length > 0) {
      const firstConfigObj = value.find((item: any) => item.eventStatus === 0);
      if (firstConfigObj) {
        setActiveUuid(firstConfigObj.uuid);
        setChannelList(firstConfigObj.channelList);
        setShowRight(true);
      }
    }
  }, [value]);
  return (
    <div>
      {value.length > 0 ? (
        <div className="notification">
          <div className="left">
            <div className="header flex">
              <div>通知事件</div>
              <div></div>
            </div>
            {value &&
              value.map((item: any) => {
                return (
                  <div
                    key={item.uuid}
                    className={`row flex ${
                      activeUuid === item.uuid ? 'active' : ''
                    }`}
                    onClick={() => {
                      if (item.eventStatus === 0) {
                        setShowRight(true);
                        setActiveUuid(item.uuid);
                        setChannelList(item.channelList);
                      }
                    }}
                  >
                    <div className="title">
                      {item.noticeName}
                      <Tooltip title={item.rule}>
                        <InfoCircleOutlined
                          rev={undefined}
                          style={{ marginLeft: '3px', color: '#0085FF' }}
                        />
                      </Tooltip>
                    </div>
                    <Switch checked={item.noticeStatus === 0} />
                  </div>
                );
              })}
          </div>
          {isShowRight && channelList && channelList.length > 0 && (
            <div className="right">
              <div className="header flex">渠道名称</div>
              <Checkbox.Group
                style={{ maxHeight: '420px', overflow: 'scroll' }}
                value={checkboxValue}
              >
                {channelList.map((child: any) => {
                  return (
                    <Row
                      key={child.uuid}
                      className="row1"
                      style={{ width: '200px' }}
                    >
                      <Checkbox value={child.uuid}>
                        <Paragraph>{child.name}</Paragraph>
                      </Checkbox>
                    </Row>
                  );
                })}
              </Checkbox.Group>
            </div>
          )}
        </div>
      ) : (
        <div>未配置通知事件</div>
      )}
    </div>
  );
};

export default NotificationInfo;

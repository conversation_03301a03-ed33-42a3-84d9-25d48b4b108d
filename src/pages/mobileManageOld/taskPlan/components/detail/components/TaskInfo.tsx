import { useState, useMemo, useEffect } from 'react';
import { Table, Modal, Button, Space, Typography, message } from 'antd';
import JsonForm from '@/bizComponents/InputParamsForm';
import { ReferanceParameterUpdateTypeEnum } from '@/bizComponents/InputParamsForm/types';
import TaskServiceApi from '../../../../services';
import { getBtnUniqCodesObj } from '@/utils/constants/const';
import { NO_DATA_IN_TABLE_COLUMN_DISPLY } from '@/utils/constants/const';
import { HelpToolTip } from 'main/HelpToolTip';
import { TASKCENTER_TEXT } from '../../../const';
import type { AppChannelEnum } from '@/types/application/type';
import { AppChannelMap } from '@/types/application/type';
import * as API from '../../../services/index';
import type { TaskDetailType, AppDetailType, TaskAccountsType } from '@/types/taskcenter/interface';
import {
  ExecuteTypeEnum,
  DistributionModeEnum,
  ExecuteTypeMap,
  DistributionModeMap,
  TaskPlanTypeEnum,
} from '@/types/taskcenter/type';
import { StatusLableTypeEnum, StatusLableMap, StatusLableColorMap } from '@/types/type';
import { hexToOneRgba } from '@/utils/calColorRgba';
import _ from 'lodash';
import { SzTooltip } from 'main/SzComponents';
import { GetPopupContainerLayerType } from '../../../const';
import { IconFont } from 'main/MyIcon';
import { useReactive } from 'ahooks';
import CalendarDetailDrawer from '@/pages/calendarManage/components/CalendarDetailDrawer';
import styles from '../index.less';
import NotificationInfo from '../NotificationInfo';

const flexLeft = styles['flex-2'];
const flexRight = styles['flex-8'];
const toolTipTextMap = {
  iconlupingbingshangchuan: '录屏并上传',
  iconlupingbushangchuan: '录屏不上传',
  iconbukaiqiluping: '不开启录屏',
  iconjingmoyunhang: '静默运行',
  iconzhuomianyunhang: '桌面运行',
};

interface Props {
  detail?: TaskDetailType;
  onUpdateDetail?: (detail: TaskDetailType) => void;
  onUpdate?: () => void;
}
const TaskInfo = (props: Props) => {
  const { detail, onUpdateDetail } = props;
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);

  /** 引用参数相关 */
  const [originAppList, setOriginAppList] = useState<AppDetailType[]>([]);

  const [parameterModalVisible, setParameterModalVisible] = useState<boolean>(false);
  // 引用参数组件的编辑态
  const [updateType, setUpdateType] = useState<ReferanceParameterUpdateTypeEnum>(
    ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR,
  );
  // 目前需要展示的流程的引用参数数据
  const [curAppInfo, setCurAppInfo] = useState<AppDetailType | null>(null);
  // 目前需要展示的流程的序号
  const [curUpdateAppIdx, setCurUpdateAppIdx] = useState<number | null>(null);
  // 引用参数组件传递给父组件的数据
  const [paramsInfo, setParamsInfo] = useState<any>({});
  const [current, setCurrent] = useState(1);

  const drawerState = useReactive({
    calendarId: '',
    detailOpen: false,
  });

  const Auth = useMemo(() => {
    const authButton = getBtnUniqCodesObj();
    return {
      // 引用参数按钮权限
      configreference: true,
    };
  }, []);

  /** 获取该任务下该流程的引用参数信息 */
  const getReferenceParamterInfo = async (uuid: string) => {
    if (!uuid) return;
    await TaskServiceApi.queryReferenceParamterByTaskAndProcessUuid(uuid).then((res: any) => {
      setCurAppInfo(res);
      setParameterModalVisible(true);
    });
  };

  /** 列表-流程信息 */
  const commonColumns = [
    {
      title: '排序',
      dataIndex: 'uuid',
      fixed: 'left',
      width: 50,
      render: (val: string, record: AppDetailType, index: number) => {
        return index + 1;
      },
    },
    {
      title: '流程名称',
      dataIndex: 'processName',
      width: '30%',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <SzTooltip title={text} placement="topLeft" popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}>
          {text}
        </SzTooltip>
      ),
    },
    {
      title: '版本',
      dataIndex: 'processVersion',
      // width: '10%',
      width: '20%',
    },
    {
      title: (
        <Space>
          <label>{'渠道'}</label>
          <HelpToolTip title={TASKCENTER_TEXT.APP_CHANNEL_HELP_TIP} getPopupContainer={() => document.body} />
        </Space>
      ),
      dataIndex: 'processChannel',
      render: (value: AppChannelEnum) => {
        return AppChannelMap[value] || NO_DATA_IN_TABLE_COLUMN_DISPLY;
      },
    },
    // {
    //   title: '标签',
    //   dataIndex: 'processVersionLabel',
    //   render: (value: StatusLableTypeEnum) => {
    //     return (
    //       <span
    //         style={{
    //           color: `${
    //             StatusLableColorMap?.[value as keyof typeof StatusLableColorMap]
    //           }`,
    //           backgroundColor:
    //             value &&
    //             `${hexToOneRgba(
    //               StatusLableColorMap?.[
    //                 value as keyof typeof StatusLableColorMap
    //               ],
    //               0.1,
    //             )}`,
    //           padding: '2px 6px',
    //           borderRadius: '9px',
    //         }}
    //       >
    //         {StatusLableMap?.[value]}
    //       </span>
    //     );
    //   },
    // },
    // {
    //   title: '运行配置',
    //   dataIndex: 'processConfigIcons',
    //   render: (value: Array<keyof typeof toolTipTextMap>) => {
    //     return (
    //       <div
    //         style={{
    //           display: 'flex',
    //           width: '100%',
    //           height: '100%',
    //           alignItems: 'center',
    //         }}
    //       >
    //         {value.map((item) => (
    //           <SzTooltip
    //             key={'processConfigIconsTips'}
    //             title={toolTipTextMap[item]}
    //             placement="top"
    //             popupContainerLayer={GetPopupContainerLayerType.FIVEPARENT}
    //           >
    //             <IconFont
    //               type={item}
    //               style={{ fontSize: 18, marginRight: 8 }}
    //             ></IconFont>
    //           </SzTooltip>
    //         ))}
    //       </div>
    //     );
    //   },
    // },
  ];

  /** 列表-流程信息-操作列：存在引用参数的权限并且流程信息中包含引用参数信息则展示 */
  const columns: any =
    originAppList?.length > 0
      ? [
          ...commonColumns,
          // {
          //   title: '操作',
          //   dataIndex: 'detailData',
          //   fixed: 'right',
          //   key: 'detailData',
          //   render: (text: boolean, record: AppDetailType, index: number) => {
          //     return (
          //       <>
          //         {record?.isReference ? (
          //           <a
          //             onClick={() => {
          //               if (Auth.configreference) {
          //                 setUpdateType(ReferanceParameterUpdateTypeEnum.EDIT);
          //               } else {
          //                 setUpdateType(ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR);
          //               }
          //               setCurUpdateAppIdx(index);
          //               getReferenceParamterInfo(record?.uuid || '');
          //             }}>
          //             {'引用参数'}
          //           </a>
          //         ) : (
          //           <>{NO_DATA_IN_TABLE_COLUMN_DISPLY}</>
          //         )}
          //       </>
          //     );
          //   },
          // },
        ]
      : commonColumns;

  /** 列表-分配信息 */
  const distributionColumns = [
    {
      title: '用户名',
      dataIndex: 'accountName',
    },
    {
      title: '用户姓名',
      dataIndex: 'realName',
      render: (text: string) => text || NO_DATA_IN_TABLE_COLUMN_DISPLY,
    },
    // {
    //   title: '优先级',
    //   dataIndex: 'priority',
    // },
  ];

  const handleOk = async () => {
    // 有引用参数
    if (curAppInfo?.isReference) {
      const inputParam = await paramsInfo?.setupData();

      /**
       * 分两个步骤：
       * 1. 向后台提交数据
       * 2. 更新本地的数据（可以再查一次详情或者直接改变本地的数据，这里采用后者）
       */
      setButtonLoading(true);
      await TaskServiceApi.updateInputParam({
        jobUuid: detail!.jobUuid!,
        jobProcessUuid: curAppInfo.uuid!,
        inputParam,
        source: curAppInfo.processSource!,
      })
        .then(() => {
          // 通知父组件更新详情信息
          if (onUpdateDetail && detail && curUpdateAppIdx !== null) {
            const newDetail: TaskDetailType = { ...detail };
            if (newDetail?.processDetails) {
              newDetail.processDetails[curUpdateAppIdx].inputParam = _.cloneDeep(inputParam);
            }
            onUpdateDetail(newDetail);
          }
          setParameterModalVisible(false);
        })
        .finally(() => {
          setButtonLoading(false);
        });
    }
  };

  // 弹框取消
  const handleJsonFormCancel = () => {
    setCurAppInfo(null);
    setCurUpdateAppIdx(null);
    setParameterModalVisible(false);
  };

  useEffect(() => {
    setOriginAppList(
      detail?.processDetails
        ? [
            ...detail.processDetails.map((item) => {
              const screenIcon = item.screenRecordingFlag
                ? item.uploadControllerFlag
                  ? 'iconlupingbingshangchuan'
                  : 'iconlupingbushangchuan'
                : 'iconbukaiqiluping';
              const runIcon = item.silentOperationFlag ? 'iconjingmoyunhang' : 'iconzhuomianyunhang';
              return { ...item, processConfigIcons: [screenIcon, runIcon] };
            }),
          ]
        : [],
    );
  }, [detail]);

  const userColumns = [
    {
      title: '用户',
      dataIndex: 'name',
      width: '30%',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: '30%',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      width: '30%',
    },
  ];

  return (
    <div className="task-info">
      <div className="application-part">
        <h4 className="font-bold">流程信息</h4>

        {detail?.jobType === TaskPlanTypeEnum.ARRANGE && (
          <>
            <div className="flex items-center gap-2 mb-2">
              <IconFont className="text-[16px]" type="iconopqiantailiucheng" />
              <span className="text-gray-400">{detail.arrangeParam.arrangeName}</span>
              <span className="text-gray-300"> | </span>
              <span className="text-gray-400">v{detail.arrangeParam.arrangeVersion}</span>
              <Typography.Link
                onClick={async () => {
                  const res = await API.checkArrangeUpgrade({
                    arrangeVersionId: detail.arrangeParam.arrangeVersionId,
                    arrangeId: detail.arrangeParam.arrangeId,
                  });
                  if (res.needUpgrade) {
                    const r = await API.arrangeUpgradeMax({
                      arrangeId: detail.arrangeParam.arrangeId,
                      jobUuid: detail.jobUuid,
                    });
                    message.success('升级成功');
                    props.onUpdate?.();
                  } else {
                    message.warning('当前版本已经是最新版本');
                  }
                }}>
                更新
              </Typography.Link>
            </div>
          </>
        )}

        <Table
          size={'small'}
          className={'drawer-table'}
          key="taskDetailAppList"
          columns={columns}
          dataSource={originAppList}
          pagination={false}
          rowKey={(record: AppDetailType) => String(record?.uuid)}
          bordered
        />
      </div>
      <div className="mt-4 execute-part">
        <h4 className="font-bold">{'执行信息'}</h4>
        <div>
          <div className={`${styles['d-flex']} ${styles['item-info']}`}>
            <div className={flexLeft}>{'执行方式:'}</div>
            <div className={flexRight}>{detail?.executeType && ExecuteTypeMap[detail?.executeType]}</div>
          </div>
          {detail?.executeType === ExecuteTypeEnum.TIMING && (
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={flexLeft}>{'定时器状态：'}</div>
              <div className={flexRight}>{detail?.isSchedule ? '已开启' : '已停止'}</div>
            </div>
          )}
          {detail?.executeType === ExecuteTypeEnum.TIMING && (
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={flexLeft}>{'定时时效'}</div>
              <div className={flexRight}>
                {/* @since 2.2.0 */}
                {detail.isAssignedCalendar ? (
                  <a
                    onClick={() => {
                      drawerState.calendarId = detail.calendarId || '';
                      drawerState.detailOpen = true;
                    }}>
                    自定义工作日历
                  </a>
                ) : detail?.isCheckActive ? (
                  <span>
                    {detail?.scheduleStart ? (
                      <span>
                        {detail?.scheduleStart}
                        {'至'}
                        {detail?.scheduleEnd}
                      </span>
                    ) : (
                      <span>{NO_DATA_IN_TABLE_COLUMN_DISPLY}</span>
                    )}
                  </span>
                ) : (
                  '长期有效'
                )}
              </div>
            </div>
          )}
          {detail?.executeType === ExecuteTypeEnum.TIMING && (
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={flexLeft}>{'定时规则：'}</div>
              <div className={flexRight}>{detail?.jobDescription}</div>
            </div>
          )}
          {(detail?.executeType === ExecuteTypeEnum.TIMING || detail?.executeType === ExecuteTypeEnum.IMMEDIATE) && (
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={flexLeft}>{'每次执行次数：'}</div>
              <div className={flexRight}>{detail?.executeTimes}</div>
            </div>
          )}
          <div className={`${styles['d-flex']} ${styles['item-info']}`}>
            <div className={flexLeft}>{'超时等待时间：'}</div>
            <div className={flexRight}>
              {detail?.maxQueueWait} {'分'}
            </div>
          </div>
          <div className={`${styles['d-flex']} ${styles['item-info']}`}>
            <div className={flexLeft}>{'最大排队数量：'}</div>
            <div className={flexRight}>{detail?.maxQueueNumber} </div>
          </div>
        </div>
      </div>
      {detail?.jobType !== TaskPlanTypeEnum.ARRANGE && (
        <div className="distribution-part">
          <h4 className="font-bold">{'分配信息'}</h4>
          <div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={flexLeft}>{'分配方式：'}</div>
              <div className={flexRight}>
                {detail?.distributionType && DistributionModeMap[detail?.distributionType]}
              </div>
            </div>
            {detail?.distributionType === DistributionModeEnum.AUTO && (
              <div className={`${styles['d-flex']} ${styles['item-info']}`}>
                <div className={flexLeft}>{'优先级'}</div>
                <div className={flexRight}>{detail?.priority}</div>
              </div>
            )}
            {detail?.distributionType === DistributionModeEnum.ASSASSIGN && (
              <Table
                size={'small'}
                key="taskDetailBotList"
                columns={distributionColumns}
                dataSource={detail?.accounts || []}
                pagination={false}
                rowKey={(record: TaskAccountsType) => record.accountUuid!}
              />
            )}
          </div>
        </div>
      )}

      {/* <div className="distribution-part">
        <h4>{'通知设置'}</h4>
        <div>
          <NotificationInfo value={detail?.noticeDetails || []} />
        </div>
        <div className="mt-[20px] mb-[20px]">通知接收用户 ：</div>
        <Table
          size={'small'}
          bordered
          dataSource={detail?.noticeUsers || []}
          columns={userColumns}
          pagination={{
            current: current,
            pageSize: 10,
            total: detail?.noticeUsers?.length,
            showTotal: (val: number) => `共${val}条`,
            onChange: (pageNum: number) => {
              setCurrent(pageNum);
            },
          }}
        />
      </div> */}

      <Modal
        width={600}
        className="popup-center-modal"
        title={updateType === ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR ? '查看引用参数' : '填写引用参数'}
        open={parameterModalVisible}
        onCancel={handleJsonFormCancel}
        footer={
          updateType === ReferanceParameterUpdateTypeEnum.VIEWANDCOLOR ? null : (
            <>
              <Button onClick={handleJsonFormCancel}>{'取消'}</Button>
              <Button type="primary" onClick={handleOk} loading={buttonLoading}>
                {'确定'}
              </Button>
            </>
          )
        }
        destroyOnClose>
        {curAppInfo && (
          <JsonForm
            formId="json-form-task"
            jsonformInfo={{
              isNewVersion: curAppInfo?.isNewVersion,
              isReference: curAppInfo?.isReference,
              template: curAppInfo?.template,
              inputParam: curAppInfo?.inputParam,
            }}
            handleFns={(v: any) => setParamsInfo(v)}
            updateType={updateType}
          />
        )}
      </Modal>

      {drawerState.detailOpen && (
        <CalendarDetailDrawer
          id={drawerState.calendarId}
          onOk={() => {
            drawerState.detailOpen = false;
          }}
          onCancel={() => {
            drawerState.detailOpen = false;
          }}
          open={drawerState.detailOpen}
        />
      )}
    </div>
  );
};

export default TaskInfo;

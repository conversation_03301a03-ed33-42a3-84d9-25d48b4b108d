import React, { useState } from 'react';
import { IconFont } from 'main/MyIcon';
import { Modal, Empty } from 'antd';
import { CopyIcon } from 'main/MyIcon';
import type { WorkListType } from '@/types/taskcenter/interface';
import {
  TaskExcuteRecordEnum,
  TaskExcuteRecordMap,
  activeStatusColorMap,
  activeStatusIconMap,
} from '@/types/taskcenter/type';
import InputParamsModal from '@/bizComponents/WorkRecord/InputParamsModal';
import LogModal from '@/bizComponents/WorkRecord/WorkLogModal';
import VideoModal from '@/bizComponents/WorkRecord/WorkVideoModal';
// import WorkRecordDetailModal from '@/bizComponents/WorkRecord/WorkRecordSteps';
import { SzTooltip } from 'main/SzComponents';
import RangeWorkResult from '@/components/RangeWorkResult';

import './index.less';

/** 任务列表状态与icon、字体颜色映射关系 */
const activeStatusMap = {
  ...TaskExcuteRecordMap,
};

/** 右侧按钮style */
const actionBtnStyle = (status: boolean) => {
  return status ? {} : { opacity: 0.4, cursor: 'not-allowed' };
};

const SZTaskStatus = (props: any) => {
  const { deleteTaskItem, data } = props;
  const dataList = data;

  const [curWorkItem, setCurWorkItem] = useState<WorkListType | null>();
  const [workRecordDetailVisible, setWorkRecordDetailVisible] =
    useState<boolean>(false);
  const [videoVisible, setVideoVisible] = useState<boolean>(false);
  const [logVisible, setLogVisible] = useState<boolean>(false);
  const [inputParamsVisible, setInputParamsVisible] = useState<boolean>(false);
  const [rangeResultProps, setRangeResultProps] = useState<any>({
    visible: false,
  });

  const timeConversion = (times: number) => {
    if (times >= 86400) {
      const d: any = times / 86400;
      const day = parseInt(d, 10);
      const resDay = times % 86400;
      const h: any = resDay / 3600;
      const hour = parseInt(h, 10);
      const resHour = resDay % 3600;
      const m: any = resHour / 60;
      const minues = parseInt(m, 10);
      const resMinues = resHour % 60;
      return ` 运行${day}天${hour}小时${minues}分${resMinues}秒`;
    }
    if (times >= 3600 && times < 86400) {
      const h: any = times / 3600;
      const hour = parseInt(h, 10);
      const resHour = times % 3600;
      const m: any = resHour / 60;
      const minues = parseInt(m, 10);
      const resMinues = resHour % 60;
      return `运行${hour}小时${minues}分${resMinues}秒`;
    }
    if (times >= 60 && times < 3600) {
      const m: any = times / 60;
      const minues = parseInt(m, 10);
      const resMinues = times % 60;
      return `运行${minues}分${resMinues}秒`;
    }
    return `运行${times}秒`;
  };

  // 渲染左侧状态
  const stepIconAndColor = (status: number | string) => {
    const newStatus = status || TaskExcuteRecordEnum.RUNNABLE;
    return (
      <>
        <IconFont
          type={
            activeStatusIconMap[newStatus as keyof typeof activeStatusIconMap]
          }
          style={{
            color:
              activeStatusColorMap[
                newStatus as keyof typeof activeStatusColorMap
              ],
          }}
        />
        <span className={`status${newStatus}`}>
          {activeStatusMap[newStatus as keyof typeof activeStatusMap]}
        </span>
      </>
    );
  };

  // 渲染任务列表详情icon
  const renderWorkRecordIcon = (item: WorkListType) => (
    <IconFont
      type="iconzuoyejindu"
      className="operationRecord"
      onClick={() => {
        setCurWorkItem(item);
        setWorkRecordDetailVisible(true);
      }}
    />
  );

  const renderWorkResultIcon = (item: WorkListType) => (
    <IconFont
      type="icondaochu"
      className="operationRecord"
      style={actionBtnStyle(!!item?.isResult)}
      onClick={() => {
        if (item.isResult) {
          setRangeResultProps({
            visible: true,
            onCancel: () => {
              setRangeResultProps({ visible: false });
            },
            workUuid: item.workUuid,
          });
        }
      }}
    />
  );

  // 渲染视频icon
  const renderVideoIcon = (item: WorkListType) => {
    const { hasRecord, hasClearVideo } = item;
    let toolTitle = '';
    if (!hasRecord && hasClearVideo) {
      toolTitle = '视频已清除';
    } else if (!hasRecord && !hasClearVideo) {
      toolTitle = '未上传视频';
    } else {
      toolTitle = '查看视频';
    }
    return (
      <SzTooltip title={toolTitle} placement="topLeft">
        <IconFont
          type="iconlupingbofang"
          className="videoPlay"
          style={actionBtnStyle(!!item?.hasRecord)}
          onClick={() => {
            setCurWorkItem(item);
            if (item?.hasRecord) setVideoVisible(true);
          }}
          disabled={!item?.hasRecord}
        />
      </SzTooltip>
    );
  };

  // 渲染日志icon
  const renderLogIcon = (item: WorkListType) => {
    const { hasLog, hasClearLog } = item;
    let toolTitle = '';
    if (!hasLog && hasClearLog) {
      toolTitle = '日志已清除';
    } else if (!hasLog && !hasClearLog) {
      toolTitle = '未上传日志';
    } else {
      toolTitle = '查看日志';
    }
    return (
      <SzTooltip title={toolTitle} placement="topLeft" zIndex={100}>
        <IconFont
          type="iconrizhichakan"
          className="journal"
          style={actionBtnStyle(!!item?.hasLog)}
          onClick={() => {
            setCurWorkItem(item);
            if (item?.hasLog) setLogVisible(true);
          }}
          disabled={!item?.hasLog}
        />
      </SzTooltip>
    );
  };
  // 渲染引用参数icon
  const renderInputParamsIcon = (item: WorkListType) => (
    <IconFont
      title={'引用参数'}
      type="iconchakanyinyongcanshu"
      className="inputParam"
      style={actionBtnStyle(!!item?.isReference)}
      onClick={() => {
        setCurWorkItem(item);
        if (!!item?.isReference) setInputParamsVisible(true);
      }}
      disabled={!item?.isReference}
    />
  );

  // 关闭弹窗时候清空的数据
  const onCancle = () => {
    setCurWorkItem(null);
  };

  return (
    <>
      <div className="work-list">
        {!dataList ||
          (dataList?.length <= 0 && (
            <Empty
              className="work-list-empty"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={<span>{'暂无记录'}</span>}
            />
          ))}
        {dataList &&
          dataList?.length > 0 &&
          dataList.map((item: WorkListType) => {
            return (
              <div key={item.workUuid} className={`work-list-item`}>
                <div>
                  {item?.status && stepIconAndColor(item.status)}
                  <span className="separate">{` | `}</span>
                  <span className="time">
                    {timeConversion(item?.runTime || 0)}
                  </span>
                </div>
                <div className="stepContent">
                  {(item.accountName || item.machineName || item.machineIp) && (
                    <div className="local">
                      {item.accountName ? `【${item.accountName}】` : ''}{' '}
                      {item.machineName}{' '}
                      {item.machineIp ? `IP：${item.machineIp}` : ''}
                    </div>
                  )}

                  <div className="copy-id">
                    {item?.workUuid}
                    {item?.workUuid && <CopyIcon copyStr={item.workUuid} />}
                  </div>
                  <div className="nowTime">{item.createTime}</div>
                </div>
                <div className="right-actions">
                  <SzTooltip title={'任务列表详情'} placement="topLeft">
                    {renderWorkRecordIcon(item)}
                  </SzTooltip>
                  <SzTooltip
                    title={item.isResult ? '下载业务结果' : '无业务结果'}
                  >
                    {renderWorkResultIcon(item)}
                  </SzTooltip>
                  {item?.status !== TaskExcuteRecordEnum.RUNNABLE ? (
                    <>
                      {renderVideoIcon(item)}
                      {renderLogIcon(item)}
                      <SzTooltip
                        placement="topLeft"
                        title={item.isReference ? '引用参数' : '暂无引用参数'}
                      >
                        {renderInputParamsIcon(item)}
                      </SzTooltip>
                    </>
                  ) : (
                    <SzTooltip title={'删除作业'} placement="topLeft">
                      <IconFont
                        title={'删除作业'}
                        type="iconshanchu1"
                        className="delete-task"
                        onClick={() => {
                          Modal.confirm({
                            title: '提示',
                            content: '删除后该作业将不存在，确定删除？',
                            onOk: () => {
                              deleteTaskItem(item.workUuid);
                            },
                          });
                        }}
                      />
                    </SzTooltip>
                  )}
                </div>
              </div>
            );
          })}
      </div>
      {curWorkItem && (
        <>
          {/* <WorkRecordDetailModal
            visible={workRecordDetailVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setWorkRecordDetailVisible(false);
            }}
          /> */}
          <VideoModal
            visible={videoVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setVideoVisible(false);
            }}
          />
          <LogModal
            visible={logVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setLogVisible(false);
            }}
          />
          <InputParamsModal
            visible={inputParamsVisible}
            workDetail={curWorkItem!}
            onClose={() => {
              onCancle();
              setInputParamsVisible(false);
            }}
          />
        </>
      )}
      {rangeResultProps.visible && <RangeWorkResult {...rangeResultProps} />}
    </>
  );
};

export default SZTaskStatus;

@import '~@/assets/styles/variable.less';

.work-list {
  position: relative;
  min-height: 160px;
  margin-top: -10px;

  &-item,
  &-empty {
    width: 100%;
  }

  &-empty {
    position: absolute;
    top: 40px;
    text-align: center;
  }

  &-item {
    position: relative;

    &:last-of-type {
      margin-bottom: 20px;
    }
  }

  .stepContent {
    width: 350px;
    margin-left: 6px;
    border-left: 1px solid rgb(216 220 224);
  }

  .status-1,
  .status-2,
  .status1,
  .status2,
  .status3,
  .status4,
  .status5,
  .status6,
  .status8 {
    margin-left: 8px;
    font-weight: 500;
    line-height: 18px;
    text-align: left;
  }

  .status-1 {
    color: #ffb937;
  }

  .status1 {
    color: #2fc37c;
  }

  .status2 {
    color: #dee2e6;
  }

  .status3,
  .status-2 {
    color: #f96056;
  }

  .status4,
  .status8 {
    color: #f98956;
  }

  .status5 {
    color: #8c9aab;
  }

  .status6 {
    color: #f98956;
  }

  .separate {
    margin: 0 10px;
    color: rgb(216 220 224);
  }

  .time {
    color: #8c9aab;
    font-size: 12px;
  }

  .local {
    margin-left: 3px;
    color: #5c6f88;
    font-size: 12px;
    line-height: 16px;
  }

  .local + .copy-id {
    margin-top: 6px;
  }

  .copy-id {
    margin-left: 10px;
    color: #8c9aab;
    font-size: 12px;
    line-height: 16px;

    .icon {
      margin-left: 5px;
      color: @primary-color;
      cursor: pointer;
    }
  }

  .nowTime {
    margin-top: 6px;
    margin-left: 10px;
    padding-bottom: 10px;
    color: #dee2e6;
    font-size: 12px;
    line-height: 16px;
    text-align: left;
  }

  .right-actions {
    .anticon {
      &:nth-last-of-type(1) {
        right: 0;
      }

      &:nth-last-of-type(2) {
        right: 28px;
      }

      &:nth-last-of-type(3) {
        right: 56px;
      }

      &:nth-last-of-type(4) {
        right: 84px;
      }

      &:nth-last-of-type(5) {
        right: 112px;
      }
    }
  }

  .videoPlay {
    position: absolute;
    top: 37px;
    color: @disabled-color;
    font-size: 18px;
    cursor: pointer;

    &:hover {
      color: @hover-color;
    }
  }

  .journal {
    position: absolute;
    top: 37px;
    color: @disabled-color;
    font-size: 18px;
    cursor: pointer;

    &:hover {
      color: @hover-color;
    }
  }

  .inputParam {
    position: absolute;
    top: 37px;
    color: @disabled-color;
    font-size: 18px;
    cursor: pointer;

    &:hover {
      color: @hover-color;
    }
  }

  .operationRecord {
    position: absolute;
    top: 37px;
    color: @disabled-color;
    font-size: 18px;

    &:hover {
      color: @hover-color;
    }
  }

  .delete-task {
    position: absolute;
    top: 36px;
    color: @disabled-color;
    font-size: 18px;
    cursor: pointer;

    &:hover {
      color: @hover-color;
    }
  }

  .videoPlay[disabled],
  .journal[disabled],
  .inputParam[disabled] {
    color: @disabled-color;
    cursor: default;
  }
}

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type {
  DetailGeneralStatisticCardDataType,
  DetailTrendChartDataType,
  DetailDrillableChartDataType,
} from '@/types/interface';
import WorkServiceApi from '../../../../../services';

export interface statisticsType {
  generalStatisticData: DetailGeneralStatisticCardDataType;
  trendChartData: DetailTrendChartDataType;
  drillableChartData: DetailDrillableChartDataType;
  loading: {
    queryGeneralStatisticData: boolean;
    queryStatisticsUseTrendData: boolean;
    queryStatisticFailedResonData: boolean;
  };
}

const initStatisticsState = {
  generalStatisticData: {},
  trendChartData: {},
  drillableChartData: {},
};

const initState = {
  ...initStatisticsState,
  loading: {
    queryGeneralStatisticData: false,
    queryStatisticsUseTrendData: false,
    queryStatisticFailedResonData: false,
  },
};

interface Actions {
  queryGeneralStatisticData: (payload: any) => any;
  queryStatisticsUseTrendData: (payload: any) => any;
  queryStatisticFailedResonData: (payload: any) => any;
}

export const useStatisticStore = create<statisticsType & Actions>()(
  devtools(
    immer<statisticsType & Actions>((set) => ({
      ...initState,
      async queryGeneralStatisticData(payload) {
        set((state: any) => ({
          ...state,
          loading: {
            queryGeneralStatisticData: true,
            queryStatisticsUseTrendData: false,
            queryStatisticFailedResonData: false,
          },
        }));
        const result: any = await WorkServiceApi.getGeneralStatisticData(
          payload,
        );
        set((state: any) => ({
          ...state,
          generalStatisticData: { ...result },
          loading: {
            queryGeneralStatisticData: false,
            queryStatisticsUseTrendData: false,
            queryStatisticFailedResonData: false,
          },
        }));
      },
      /** 获取机器人运行趋势 */
      async queryStatisticsUseTrendData(payload) {
        set((state: any) => ({
          ...state,
          loading: {
            queryGeneralStatisticData: false,
            queryStatisticsUseTrendData: true,
            queryStatisticFailedResonData: false,
          },
        }));
        const result: any = await WorkServiceApi.getStatisticsUseTrendData(
          payload,
        );

        /* eslint-disable */
        if (result && result?.content && result.content?.length > 0) {
          result.content.map((item: any) => {
            item.value = Number(item.value);
            return item;
          });
        }

        /* eslint-enable */
        set((state: any) => ({
          ...state,
          trendChartData: { ...result },
          loading: {
            queryGeneralStatisticData: false,
            queryStatisticsUseTrendData: false,
            queryStatisticFailedResonData: false,
          },
        }));
        return result;
      },
      async queryStatisticFailedResonData(payload: any) {
        set((state: any) => ({
          ...state,
          loading: {
            queryGeneralStatisticData: false,
            queryStatisticsUseTrendData: false,
            queryStatisticFailedResonData: true,
          },
        }));
        const result: any = WorkServiceApi.getStatisticFailedResonData(payload);

        const total: number = (result?.content || []).reduce(
          (count: number, curItem: any) => count + Number(curItem.value),
          0,
        );

        (result?.content || []).map((item: any) => {
          const newItem = item;
          newItem.value = Number(item.value);
          newItem.percent = Number((item.value / total).toFixed(4));

          (newItem?.fanChartDetailList || []).map((subItem: any) => {
            const newSubItem = subItem;
            newSubItem.value = Number(subItem.value);
            newSubItem.percent = Number(
              (subItem.value / newItem.value).toFixed(4),
            );

            return newSubItem;
          });

          return newItem;
        });

        set((state: any) => ({
          ...state,
          drillableChartData: { ...result },
          loading: {
            queryGeneralStatisticData: false,
            queryStatisticsUseTrendData: false,
            queryStatisticFailedResonData: false,
          },
        }));
        return result;
      },
    })),
    {
      name: 'useStatisticStore',
    },
  ),
);

import React, { useState, useEffect, useRef } from 'react';
import { Row, Col } from 'antd';

import {
  RadioDateMaps,
  RadioDateEnum,
  TaskExcuteRecordStatisticColorMap,
} from '@/types/type';
import { TaskExcuteRecordEnum } from '@/types/taskcenter/type';
import { TaskExcuteRecordMap } from '@/types/taskcenter/type';

import type { statisticParamsType } from '@/types/taskcenter/interface';
import {
  StatisticalFilterSelector,
  GeneralStatisticCard,
  BarChart,
  DrillablePieChart,
} from '@/components/Charts';
import { useStatisticStore } from './store';

import './index.less';

interface Props {
  jobId: string | undefined;
  tenantId?: string | undefined;
}

const PcBotStatistics = (props: Props) => {
  const { jobId, tenantId } = props;
  const CommonParams = useRef<statisticParamsType>();
  const {
    queryGeneralStatisticData,
    queryStatisticsUseTrendData,
    queryStatisticFailedResonData,
    generalStatisticData,
    trendChartData,
    drillableChartData,
    loading,
  } = useStatisticStore();
  /** 当前所选日期/时间 */
  const [curDateId, setCurDateId] = useState<RadioDateEnum>(RadioDateEnum.WEEK);
  /** 获取每个模块的loading状态 */
  const getLodingStateByEffectName = (
    name:
      | 'queryGeneralStatisticData'
      | 'queryStatisticsUseTrendData'
      | 'queryStatisticFailedResonData',
  ) => {
    return (loading && loading[name]) || false;
  };
  const initPage = async (params: statisticParamsType) => {
    queryGeneralStatisticData(params);
    queryStatisticsUseTrendData(params);
    queryStatisticFailedResonData(params);
  };

  useEffect(() => {
    if (!jobId) return;
    CommonParams.current = {
      jobId,
      type: curDateId,
      tenantId,
    };
    initPage(CommonParams.current);
  }, [jobId, tenantId, curDateId]);

  return (
    <div className="statistic-tab">
      {/* 顶部日期搜索框 */}
      <StatisticalFilterSelector
        key="bot-statisticalFilterSelector"
        radioMap={RadioDateMaps}
        radioCheckedVal={curDateId}
        setRadioCheckedVal={setCurDateId}
      ></StatisticalFilterSelector>
      {/* 第一行：概况分析 */}
      <Row gutter={[12, 12]} className="row-block">
        <Col span={24}>
          <GeneralStatisticCard
            key="bot-generalStatisticCard"
            cardData={generalStatisticData}
            statusMap={TaskExcuteRecordMap}
            statusColorMap={TaskExcuteRecordStatisticColorMap}
            loading={getLodingStateByEffectName('queryGeneralStatisticData')}
          />
        </Col>
      </Row>
      {/* 第二行：运行趋势 */}
      <Row gutter={[12, 12]} className="row-block">
        <Col span={24}>
          <BarChart
            key="bot-barchart"
            cardData={trendChartData}
            typeColorArr={[
              TaskExcuteRecordStatisticColorMap[
                TaskExcuteRecordEnum.RUNSUCCESS
              ],
              TaskExcuteRecordStatisticColorMap[TaskExcuteRecordEnum.RUNFAILED],
              TaskExcuteRecordStatisticColorMap[TaskExcuteRecordEnum.STOPPED],
              TaskExcuteRecordStatisticColorMap[
                TaskExcuteRecordEnum.LOSTRESULT
              ],
            ]}
            loading={getLodingStateByEffectName('queryStatisticsUseTrendData')}
          />
        </Col>
      </Row>
      {/* 第三行： 失败原因 */}
      <Row gutter={[12, 12]} className="row-block">
        <Col span={24}>
          <DrillablePieChart
            key="bot-drillablePieChart"
            cardData={drillableChartData}
            loading={getLodingStateByEffectName(
              'queryStatisticFailedResonData',
            )}
            id="botDrillablePieChart"
          />
        </Col>
      </Row>
    </div>
  );
};

export default PcBotStatistics;

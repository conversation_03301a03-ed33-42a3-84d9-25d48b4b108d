import React, { useState, useEffect } from 'react';
import { Drawer, Tabs, Spin, Tag } from 'antd';
import TaskInfo from './components/TaskInfo';
import ExecuteRecord from './components/ExecuteRecord';
// import Statistic from './components/Statistic';
import TaskServiceApi from '../../../services';
import type { TaskDetailType } from '@/types/taskcenter/interface';
import { TabTypeEnum, TaskPlanTypeEnum, TaskPlanTypeEnumMap } from '@/types/taskcenter/type';
import { CopyIcon } from 'main/MyIcon';

import styles from './index.less';

const { TabPane } = Tabs;

const TabTypeMap = {
  [TabTypeEnum.TASKINFO]: '计划信息',
  [TabTypeEnum.RUNRECORD]: '任务列表',
  [TabTypeEnum.STATISTICS]: '运行统计',
};

interface Props {
  jobUuid: string | null;
  visible: boolean;
  isGroup?: boolean;
  onClose: () => void;
}

const TaskDetail = (props: Props) => {
  const { jobUuid = null, visible, isGroup = false, onClose } = props;
  const [activeTab, setActiveTab] = useState<TabTypeEnum>(TabTypeEnum.TASKINFO);
  const [taskDetail, setTaskDetail] = useState<TaskDetailType>({
    jobUuid: '',
  } as TaskDetailType);
  /** 获取任务（组）详情信息 */
  const getTaskDetail = async (id: string) => {
    const data: TaskDetailType = await TaskServiceApi.getTaskmanageDetail(id);
    if (data?.jobUuid === id) {
      setTaskDetail(data);
    }
  };

  useEffect(() => {
    return () => {
      setActiveTab(TabTypeEnum.TASKINFO);
    };
  }, []);

  useEffect(() => {
    if (visible && jobUuid) {
      // 通过taskId查询任务详情信息
      getTaskDetail(jobUuid);
    }
  }, [jobUuid, visible]);

  // 设置当前tab
  const callback = (key: string) => {
    setActiveTab(key as TabTypeEnum);
  };
  return (
    <Drawer
      // @ts-ignore
      title={`${TaskPlanTypeEnumMap[taskDetail?.jobType]}详情`}
      width={650}
      onClose={() => {
        setActiveTab(TabTypeEnum.TASKINFO);
        onClose();
        // setTaskDetail({ jobUuid: '' } as TaskDetailType);
      }}
      open={visible}
      className={styles['task-drawer']}
      destroyOnClose>
      <Spin spinning={!taskDetail?.jobUuid}>
        <div className={styles['detail-part1']}>
          <div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={styles['flex-2']}>计划名称:</div>
              <div className={styles['flex-8']}>
                {taskDetail?.humanRobotInteractiveType && <Tag color="processing">人机</Tag>}
                {taskDetail?.jobName}
              </div>
            </div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={styles['flex-2']}>计划ID:</div>
              <div className={styles['flex-8']}>
                {taskDetail?.jobUuid}
                {taskDetail?.jobUuid && <CopyIcon copyStr={taskDetail.jobUuid} />}
              </div>
            </div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={styles['flex-2']}>计划类型:</div>
              <div className={styles['flex-8']}>{TaskPlanTypeEnumMap[taskDetail?.jobType]}</div>
            </div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={styles['flex-2']}>{'所属部门:'}</div>
              <div className={styles['flex-8']}>{taskDetail?.departmentName}</div>
            </div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={styles['flex-2']}>{'创建者：'}</div>
              <div className={styles['flex-8']}>{taskDetail?.creatorName}</div>
            </div>
            <div className={`${styles['d-flex']} ${styles['item-info']}`}>
              <div className={styles['flex-2']}>{'创建时间：'}</div>
              <div className={styles['flex-8']}>{taskDetail?.createTime}</div>
            </div>
            {/* {
              <div className={`${styles['d-flex']} ${styles['item-info']}`}>
                <div className={styles['flex-2']}>{'标签：'}</div>
                <div className={styles['flex-8']}>
                  {taskDetail?.tags?.map((item: any) => {
                    return (
                      <Tag key={item.tagId} color="magenta">
                        {item.tagValue}
                      </Tag>
                    );
                  }) || '--'}
                </div>
              </div>
            } */}
            {taskDetail.humanRobotInteractiveType && (
              <div className={`${styles['d-flex']} ${styles['item-info']}`}>
                <div className={styles['flex-2']}>{'处理人:'}</div>
                <div className={styles['flex-8']}>
                  {taskDetail?.humanRobotInteractiveHandlers?.map((item) => item.handlerNames).join(' / ')}
                </div>
              </div>
            )}
            {taskDetail.humanRobotInteractiveType && (
              <div className={`${styles['d-flex']} ${styles['item-info']}`}>
                <div className={styles['flex-2']}>{'处理人部门:'}</div>
                <div className={styles['flex-8']}>{taskDetail?.humanRobotInteractiveHandlerDeptInfos}</div>
              </div>
            )}
          </div>
        </div>
        <div className="detail-part2">
          <Tabs
            activeKey={activeTab}
            onChange={callback}
            className={activeTab === TabTypeEnum.STATISTICS ? 'tab-statistics' : ''}>
            <TabPane tab={TabTypeMap[TabTypeEnum.TASKINFO]} key={TabTypeEnum.TASKINFO}>
              {activeTab === TabTypeEnum.TASKINFO && (
                <TaskInfo
                  detail={{ ...taskDetail }}
                  onUpdate={() => {
                    if (jobUuid) {
                      getTaskDetail(jobUuid);
                    }
                  }}
                />
              )}
            </TabPane>
            {/* <TabPane
              tab={TabTypeMap[TabTypeEnum.STATISTICS]}
              key={TabTypeEnum.STATISTICS}
              className="tab-statistics"
            >
              {activeTab === TabTypeEnum.STATISTICS && (
                <Statistic jobId={taskDetail.jobId} />
              )}
            </TabPane> */}
            <TabPane tab={TabTypeMap[TabTypeEnum.RUNRECORD]} key={TabTypeEnum.RUNRECORD}>
              {taskDetail?.jobUuid && <ExecuteRecord detailInfo={taskDetail as any} />}
            </TabPane>
          </Tabs>
        </div>
      </Spin>
    </Drawer>
  );
};

export default TaskDetail;

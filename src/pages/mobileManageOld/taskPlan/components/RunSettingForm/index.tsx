import { Checkbox, Form, Switch } from 'antd';
import { FC, useEffect, useState } from 'react';
import styles from './index.less';
import { TASKCENTER_TEXT } from '../../const';
import { formValidateDataApi } from '@/utils/utils';
import { RenderTipsIcon } from 'main/HelpToolTip';
interface RunSettingFormProps {
  handleFns: ((handle: any) => void) | undefined;
  curAppDetail: EditFormInfo;
}

export interface EditFormInfo {
  screenRecordingFlag?: boolean;
  silentOperationFlag?: boolean;
  uploadControllerFlag?: boolean;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const RunSettingForm: FC<RunSettingFormProps> = (props) => {
  const { curAppDetail, handleFns } = props;
  const [form] = Form.useForm();
  const [screenRecord, setScreenRecord] = useState<boolean>(true);

  useEffect(() => {
    setScreenRecord(
      curAppDetail.screenRecordingFlag === undefined
        ? true
        : curAppDetail.screenRecordingFlag,
    );
    form.setFieldsValue({
      screenRecordingFlag: curAppDetail?.screenRecordingFlag,
      silentOperationFlag: curAppDetail?.silentOperationFlag,
      uploadControllerFlag: curAppDetail?.uploadControllerFlag,
    });
  }, [curAppDetail]);

  const handleData = {
    /** 校验form表单数据 */
    validateData: () => formValidateDataApi(form),

    /**
     * 组装要提交的数据: formValues
     */
    setupData: async () => {
      const saveData = form.getFieldsValue();

      return saveData;
    },

    /** 清除数据 */
    clearData: () => {},
  };

  // 绑定数据通信
  const bindHandle = () => {
    if (handleFns) handleFns(handleData);
  };

  useEffect(() => {
    bindHandle();
  }, []);

  return (
    <>
      <Form
        {...formItemLayout}
        form={form}
        layout="horizontal"
        scrollToFirstError={true}
      >
        <div className={styles['container']}>
          <Form.Item
            label={TASKCENTER_TEXT.TASK_SCREEN_RECORD}
            name="screenRecordingFlag"
            className={styles['record']}
            valuePropName="checked"
            initialValue={true}
            tooltip={RenderTipsIcon(
              '对任务设置运行过程录屏。是，表示录屏并上传运营平台。否，表示不录屏。按机器人设置，表示遵循机器人端的录屏设置规则。',
            )}
          >
            <Switch // 上传控制器选项受此选项影响
              onChange={(checked) => {
                setScreenRecord(checked);
                if (checked) {
                  form.setFieldsValue({ uploadControllerFlag: true });
                  form.setFieldsValue({ silentOperationFlag: false });
                } else {
                  form.setFieldsValue({ uploadControllerFlag: false });
                }
              }}
            />
          </Form.Item>
          <Form.Item
            label={''}
            name="uploadControllerFlag"
            valuePropName="checked"
            className={styles['uploadController']}
            colon={false}
            initialValue={true}
            hidden={!screenRecord}
          >
            <Checkbox>{TASKCENTER_TEXT.TASK_UPLOAD_COMMANDER}</Checkbox>
          </Form.Item>
        </div>
        <Form.Item
          label={TASKCENTER_TEXT.TASK_RUN_SILENTLY}
          name="silentOperationFlag"
          valuePropName="checked"
          initialValue={false}
        >
          <Switch disabled={screenRecord} />
        </Form.Item>
      </Form>
    </>
  );
};

export default RunSettingForm;

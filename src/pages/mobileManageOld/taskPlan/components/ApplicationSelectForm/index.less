/** 公共 */
.select-option-item {
  display: flex;
  justify-content: space-between;

  .select-option-item-label {
    width: 68%;
  }

  .select-option-item-extra {
    width: 28%;
    color: rgb(51 67 85 / 30%);
    text-align: right;
  }
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}

/** 录屏 */
.container {
  position: relative;
  width: 100%;
  height: 32px;
  margin-bottom: 24px;

  .record {
    position: absolute;
    width: 100%;
  }

  .uploadController {
    position: absolute;
    left: calc(33.33% + 150px);
    width: 270px;
    transform: translateX(-33.33%);

    :global {
      .ant-checkbox-inner {
        width: 18px;
        height: 18px;
      }
    }
  }
}

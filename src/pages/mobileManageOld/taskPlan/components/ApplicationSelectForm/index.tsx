import { useFrameWorkContext } from '@/exposes/deopCmdEntry/context';
import { AppChannelEnum } from '@/types/application/type';
import type { CommonListType } from '@/types/interface';
import type { AppDetailType } from '@/types/taskcenter/interface';
import { TaskPlanTypeEnum } from '@/types/taskcenter/type';
import type { StatusLableTypeEnum } from '@/types/type';
import { getBizUnitPathCodeInTrees } from '@/utils/bizUtils';
import { formValidateDataApi } from '@/utils/utils';
import { Form, Radio, Spin, Switch, TreeSelect } from 'antd';
import { RenderTipsIcon } from 'main/HelpToolTip';
import { SzTreeSelect } from 'main/SzComponents';
import { useEffect, useState } from 'react';
import { history } from 'umi';
import { GetDepartmentTreeRes } from '../../../services';
import { TASKCENTER_TEXT } from '../../const';
import { useAppStore } from '../store/index';
import styles from './index.less';
import { ApplicationFormTypeEnum } from './types';

const store = useAppStore;

interface Props {
  /** 编辑-编辑时候的流程详细信息 */
  detail?: AppDetailType;
  /** 展示类型-有无参数 */
  formType: ApplicationFormTypeEnum;
  /** 由父组件传递，是否需要返回流程相关信息，默认不返回 */
  isBackOptionLabel?: boolean;
  taskPlanType: TaskPlanTypeEnum;
  /** 数据处理对象 */
  handleFns?: (handle: any) => void;
  appDetail?: AppDetailType;
  /** 流程相关的store数据 */
  appList?: CommonListType[];
  appVersionsInApp?: Record<string, unknown>;
  appVersionList?: CommonListType[];
  selectAppInfo?: any;
  defaultChannel?: AppChannelEnum;
  processDefault?: string[];
  setHumanRobotInteractiveType?: (val: boolean) => void;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const generateTree = (treeNodes: GetDepartmentTreeRes = []) => {
  return treeNodes.map((item: { name: string; structId: string; children: any }) => (
    <TreeSelect.TreeNode title={item.name} value={String(item.structId)} key={item.structId}>
      {item.children.length > 0 && generateTree(item.children)}
    </TreeSelect.TreeNode>
  ));
};

const ApplicationForm = (props: Props) => {
  const {
    detail,
    isBackOptionLabel = false,
    taskPlanType,
    formType,
    handleFns,
    defaultChannel = AppChannelEnum.APPMANAGEMENT,
    processDefault = [],
    setHumanRobotInteractiveType,
  } = props;
  const {
    resetData,
    resetAppData,
    save,
    queryAppAndAppVersionListByProcessChannel,
    queryAppManageDetail,
    queryAppMarketDetail,
    selectAppInfo = {},
    appList = [],
    appVersionsInApp = {},
    appVersionList = [],
    appDetail = {},
  } = useAppStore();
  const [form] = Form.useForm();

  const frameWorkContextInfo = useFrameWorkContext();
  const { treeMenu } = frameWorkContextInfo;
  /** 渠道 */
  const [processChannel, setProcessChannel] = useState<AppChannelEnum>(defaultChannel);

  /** 流程列表及版本数据加载状态 */
  const [processUuidLoading, setProcessUuidLoading] = useState<boolean>(false);
  /** 所选择流程详情加载状态 */
  const [processDetailLoading, setProcessDetailLoading] = useState<boolean>(false);
  /* 引用参数数据 */
  const [paramsInfo, setParamsInfo] = useState<any>({});
  /* 是否开启录屏，用于判断是否展示上传控制器选项 */
  const [screenRecord, setScreenRecord] = useState<boolean>(true);

  /** 渲染表单数据 */
  const setFormFieldsValue = async (valueObj = {}) => {
    await form.setFieldsValue({ ...valueObj });
  };

  /** 获取App列表及版本信息 */
  const getAppAndAppVersionList = async (params: {
    processChannels: AppChannelEnum[];
    /** 流程上架状态：true-已上架，false-已下架，不传则表示全部，目前新建任务时只需要已上架的，编辑时候为全部 */
    isPublish?: boolean;
  }) => {
    setProcessUuidLoading(true);
    return await queryAppAndAppVersionListByProcessChannel({
      ...params,
      taskPlanType,
    }).finally(() => {
      setProcessUuidLoading(false);
    });
  };

  const findDetailById = (id: string, data?: any) => {
    let parent = {};
    let isRobert = false;
    const dataSource = data || store.getState().appList;
    dataSource?.forEach((item: any) => {
      item.children.forEach((child: any) => {
        if (id === child.value) {
          parent = item;
          isRobert = child.humanRobotInteractiveType;
        }
      });
    });
    return { parent, isRobert };
  };

  /** 修改 - 根据版本id 查询参数信息 */
  useEffect(() => {
    if (!detail?.uuid || !detail?.processUuid) {
      return;
    }
    setScreenRecord(detail.screenRecordingFlag === undefined ? true : detail.screenRecordingFlag);
    setFormFieldsValue({
      uploadControllerFlag: detail.uploadControllerFlag,
      screenRecordingFlag: detail.screenRecordingFlag,
      silentOperationFlag: detail.silentOperationFlag,
    });
    const channel = detail.processChannel!;
    setProcessChannel(channel);
    getAppAndAppVersionList({ processChannels: [channel] }).then(() => {
      save({
        selectAppInfo: {
          appId: detail?.processUuid,
          appVersionId: detail?.processDetailUuid,
        },
      });
    });
  }, [detail]);

  useEffect(() => {
    getAppAndAppVersionList({ processChannels: [processChannel] }).then(({ appList }) => {
      if (processDefault && processDefault.length) {
        if (processDefault.at(-1) && setHumanRobotInteractiveType) {
          const { isRobert } = findDetailById(processDefault.at(-1) as string, appList) as any;

          setHumanRobotInteractiveType(isRobert);
        }
        form.setFieldValue('processDetailUuid', processDefault.at(-1));
      }
    });
  }, [processChannel, defaultChannel]);

  /** 选择渠道切换时查询对应流程列表数据 */
  useEffect(() => {
    // 清除与流程相关的数据：流程、流程版本、预估人工时间、引用参数等
    setFormFieldsValue({
      processChannel,
      processUuid: null,
      processDetailUuid: null,
    }).then(() => {
      resetAppData();
    });
  }, [processChannel]);

  /** 选择流程发生切换时查询对应版本列表 */
  useEffect(() => {
    setFormFieldsValue({
      processUuid: selectAppInfo?.appId,
      processDetailUuid: null,
    }).then(() => {
      const appVersions: any = selectAppInfo?.appId ? appVersionsInApp[selectAppInfo?.appId] : [];
      save({
        appVersionList: appVersions,
        appDetail: {},
        selectAppInfo: {
          ...selectAppInfo,
          appVersionId: selectAppInfo?.appVersionId || (appVersions.length > 0 ? appVersions?.[0].key : null),
        },
      });
    });
  }, [selectAppInfo?.appId, selectAppInfo?.updateAppIdTime]);

  /**
   * 选择版本发生切换时查询对应流程详情信息
   * 编辑初始次不需要查询，只有手动改变了流程id或者版本id，主动改变了流程id的时候才查询
   * */
  useEffect(() => {
    setFormFieldsValue({
      processDetailUuid: selectAppInfo?.appVersionId,
    }).then(() => {
      if (detail?.processDetailUuid === selectAppInfo?.appVersionId) {
        save({
          appDetail: detail,
        });
        return;
      }
      save({
        appDetail: {},
      });
      if (selectAppInfo?.appVersionId) {
        setProcessDetailLoading(true);
        // 重新获取流程详情信息
        if (processChannel === AppChannelEnum.APPMARKET) {
          queryAppMarketDetail(selectAppInfo.appVersionId).finally(() => {
            setProcessDetailLoading(false);
          });
        } else {
          if (taskPlanType !== TaskPlanTypeEnum.ARRANGE) {
            queryAppManageDetail(selectAppInfo.appVersionId).finally(() => {
              setProcessDetailLoading(false);
            });
          } else {
            setProcessDetailLoading(false);
          }
        }
      }
    });
  }, [selectAppInfo?.appVersionId]);

  /** 流程列表数据返回则更新form数据 */
  useEffect(() => {
    if (selectAppInfo?.appId && appList.length > 0) {
      setFormFieldsValue({
        processUuid: selectAppInfo?.appId,
      });
    }
  }, [appList]);

  // /** 流程版本列表数据返回则更新form数据 */
  useEffect(() => {
    if (selectAppInfo?.appVersionId && appVersionList.length > 0) {
      setFormFieldsValue({
        processDetailUuid: selectAppInfo?.appVersionId,
      });
    }
  }, [appVersionList]);

  const handleData = {
    /** 校验form表单数据 */
    validateData: () => formValidateDataApi(form),

    /**
     * 组装父组件需要的数据
     */
    setupData: async () => {
      const formData = await form.getFieldsValue();
      const appNameAndVersion: {
        processName?: string;
        processVersion?: string;
        processVersionLabel?: StatusLableTypeEnum;
      } = {};
      let inputParam = null;
      const jobProcessUuid = appDetail?.uuid;

      // eslint-disable-next-line no-extra-boolean-cast
      if (!!jobProcessUuid) {
        formData.jobProcessUuid = jobProcessUuid;
      }

      // 需要向父组件返回流程相关的信息
      if (isBackOptionLabel) {
        const appIndex = (appList || []).findIndex((item: CommonListType) => item.key === formData.processUuid);
        const appVersionIndex = (appVersionList || []).findIndex(
          (item: CommonListType) => item.key === formData.processDetailUuid,
        );
        if (appIndex > -1) {
          appNameAndVersion.processName = appList[appIndex].value;
        }
        if (appVersionIndex > -1) {
          appNameAndVersion.processVersion = appVersionList[appVersionIndex].value;
          appNameAndVersion.processVersionLabel = appVersionList[appVersionIndex].processVersionLabel;
        }
      }

      // 有引用参数
      if (formType === ApplicationFormTypeEnum.NORMAL && appDetail?.isReference) {
        inputParam = await paramsInfo?.setupData();
      }
      if (formType === ApplicationFormTypeEnum.NOJSONFORM) {
        // 在不展示引用参数的地方把是否存在引用参数正常返回给父组件
        const inputParamsObj = {
          isReference: appDetail?.isReference,
          isNewVersion: appDetail?.isNewVersion,
          inputParam: appDetail?.inputParam,
          template: appDetail?.template,
        };
        const data = {
          ...appDetail,
          ...formData,
          ...inputParamsObj,
          ...appNameAndVersion,
        };

        return data;
      }
      let arrangeParam = {};
      if (taskPlanType === TaskPlanTypeEnum.ARRANGE) {
        const { arrangeVersionId } = formData;
        if (arrangeVersionId) {
          const { parent } = findDetailById(arrangeVersionId) as any;
          arrangeParam = {
            arrangeId: parent.mainUUID,
            arrangeVersionId: arrangeVersionId,
          };
        }
      }

      const saveData = {
        ...appDetail,
        ...formData,
        arrangeParam: arrangeParam,
        inputParam,
        ...appNameAndVersion,
      };
      return saveData;
    },

    /** 清除数据 */
    clearData: () => {
      setProcessChannel(AppChannelEnum.APPMANAGEMENT);
      resetData();
    },
  };

  // 绑定数据通信
  const bindHandle = () => {
    if (handleFns) handleFns(handleData);
  };

  useEffect(() => {
    bindHandle();
    if (processDefault.at(-1)) {
      save({
        selectAppInfo: { appId: null, appVersionId: processDefault.at(-1) },
      });
    }

    return () => {
      resetData();
    };
  }, [processDefault.at(-1)]);

  useEffect(() => {
    bindHandle();
  }, [appDetail, selectAppInfo, processChannel, paramsInfo]);

  useEffect(() => {
    if (taskPlanType === TaskPlanTypeEnum.ARRANGE) {
      form.setFieldValue('arrangeParam', {
        arrangeId: processDefault[0],
        arrangeVersionId: processDefault[1],
      });
    }
  }, [processDefault]);

  const createFlowArrange = () => {
    const linkUrl = getBizUnitPathCodeInTrees(treeMenu, '/flowArrange');
    history.push(`${linkUrl}&type=create`);
  };

  const filterTreeNode = (searchValue: string, node: any) => {
    return node.name.toLowerCase().includes(searchValue.toLowerCase());
  };

  return (
    <Form {...formItemLayout} form={form} layout="horizontal" scrollToFirstError={true}>
      <div>
        <Spin spinning={!!detail?.uuid && processUuidLoading}>
          <Form.Item
            label={'选择渠道'}
            name="processChannel"
            tooltip={RenderTipsIcon(TASKCENTER_TEXT.APP_CHANNEL_HELP_TIP)}
            style={{
              display: taskPlanType === TaskPlanTypeEnum.ARRANGE ? 'none' : 'block',
            }}>
            <Radio.Group
              disabled={processDefault.length > 0}
              onChange={(e) => {
                const curVal = e.target.value;
                if (curVal !== processChannel) {
                  setProcessChannel(curVal);
                  save({
                    selectAppInfo: { appId: null, appVersionId: null },
                  });
                }
              }}>
              {Object.entries({ 0: '流程列表' }).map(([key, val]: any) => {
                return (
                  <Radio key={key} value={Number(key)}>
                    {val}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>

          {<Form.Item label={'选择流程'} name="processUuid" style={{ display: 'none' }} />}
          {<Form.Item name="humanRobotInteractiveType" style={{ display: 'none' }} />}

          {<Form.Item name="arrangeParam" style={{ display: 'none' }} />}

          {taskPlanType !== TaskPlanTypeEnum.ARRANGE && (
            <Form.Item
              label={'选择流程'}
              name="processDetailUuid"
              rules={[{ required: true, message: '请选择流程' }]}
              initialValue={processDefault.at(-1)}>
              <SzTreeSelect
                defaultValue={processDefault.at(-1)}
                disabled={processDefault.length > 0}
                showSearch
                style={{ width: '100%' }}
                loading={processUuidLoading}
                placeholder={'请选择流程'}
                filterTreeNode={filterTreeNode}
                dropdownRender={(options: any) => {
                  return (
                    <>
                      {processUuidLoading ? (
                        <Spin spinning={true}>
                          <div className={'select-no-data'}>{'暂无数据'}</div>
                        </Spin>
                      ) : (
                        options
                      )}
                    </>
                  );
                }}
                onChange={(value: any) => {
                  const { parent, isRobert } = findDetailById(value) as any;
                  form.setFieldValue('processUuid', parent.mainUUID);
                  if (value !== selectAppInfo.appVersionId) {
                    save({
                      selectAppInfo: {
                        ...selectAppInfo,
                        appVersionId: value,
                        appId: parent.mainUUID,
                      },
                      appDetail: {},
                    });
                    form.setFieldValue('humanRobotInteractiveType', isRobert);
                    if (setHumanRobotInteractiveType) setHumanRobotInteractiveType(isRobert);
                  }
                }}
                treeDefaultExpandAll
                treeData={appList}
              />
            </Form.Item>
          )}

          {/* <div className={styles['container']}>
                {taskPlanType !== TaskPlanTypeEnum.ARRANGE && (
                  <Form.Item
                    label={TASKCENTER_TEXT.TASK_SCREEN_RECORD}
                    name="screenRecordingFlag"
                    className={styles['record']}
                    valuePropName="checked"
                    initialValue={true}
                    tooltip={RenderTipsIcon(
                      '对任务设置运行过程录屏。是，表示录屏并上传运营平台。否，表示不录屏。按机器人设置，表示遵循机器人端的录屏设置规则。',
                    )}>
                    <Switch
                      // 上传控制器选项受此选项影响
                      onChange={(checked) => {
                        setScreenRecord(checked);

                        if (checked) {
                          form.setFieldsValue({ uploadControllerFlag: true });
                          form.setFieldsValue({ silentOperationFlag: false });
                        } else {
                          form.setFieldsValue({ uploadControllerFlag: false });
                        }
                      }}
                    />
                  </Form.Item>
                )}
              </div> */}

          {/* {formType === ApplicationFormTypeEnum.NORMAL ? (
                <>
                  {appDetail?.isReference ? (
                    <Form.Item label={'引用参数'} name="inputParam">
                      <JsonForm
                        formId="application-form"
                        jsonformInfo={{
                          isNewVersion: appDetail?.isNewVersion,
                          isReference: appDetail?.isReference,
                          template: appDetail?.template,
                          inputParam: appDetail?.inputParam,
                        }}
                        handleFns={(v: any) => setParamsInfo(v)}
                        updateType={ReferanceParameterUpdateTypeEnum.EDITANDCOLOR}
                      />
                    </Form.Item>
                  ) : null}
                </>
              ) : null} */}
        </Spin>
      </div>
    </Form>
  );
};

export default ApplicationForm;

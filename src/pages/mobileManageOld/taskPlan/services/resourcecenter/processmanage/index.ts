import request from '@/utils/request';
import type { statisticParamsType } from '@/types/taskcenter/interface';
import type { RoiTypeEnum } from '@/types/application/type';

const getProcess = async (params: any) => {
  return request('/process', {
    method: 'get',
    params,
  });
};
export const getProcessDetail = async (id: string) => {
  return request(`/process/${id}`, {
    method: 'get',
  });
};
const processPackageUpload = async (params: any) => {
  return request('/process/package-upload', {
    method: 'post',
    data: params,
  });
};
const processSave = async (id: any, params: any) => {
  return request(`/process/${id}`, {
    method: 'post',
    data: params,
  });
};

// 【manager】流程删除
const curProcessDelete = async (params: { processUuid: string }) => {
  return request(`/process/delete`, {
    method: 'post',
    data: params,
  });
};

// 流程列表-删除流程版本
export const processDelete = async (params: { processDetailUUID: string }) => {
  return request(`/process/detail/del`, {
    method: 'post',
    data: params,
  });
};

// 流程列表-新增流程-流程包删除
const processPkgDelete = async (params: { packageUUID: string }) => {
  return request(`/process/package-delete`, {
    method: 'post',
    data: params,
  });
};

// 流程列表-发布流程版本
export const processIsPublish = async (params: {
  processDetailUUID: string;
  isPublished: boolean;
}) => {
  return request(`/process/detail/published`, {
    method: 'post',
    data: params,
  });
};
export const processDownload = async (id: any) => {
  return request(`/process/detail/downLoad/${id}`, {
    method: 'get',
    responseType: 'blob',
  });
};

export interface processActionParams {
  processDetailUuid: string;
  processHumanMinute?: string;
  versionLabel: number;
  roiType?: RoiTypeEnum;
  roiPercent?: number;
}

// 流程列表-编辑人工预计时间
const updateTime = async (params: processActionParams) => {
  return request(`/process/detail/data/human-minute`, {
    method: 'post',
    data: params,
  });
};

// 新版-流程列表-编辑人工预计时间，编辑标签
export const updateProcess = async (params: processActionParams) => {
  return request(`/process/detail/update`, {
    method: 'post',
    data: params,
  });
};

// 流程列表-选择流程列表下拉
export async function getAppManageOptions() {
  return request('/process/select', {
    method: 'GET',
  });
}

// 流程列表-选择单个流程版本列表下拉
export async function getAppManageVersionOptions(processUuid: string) {
  return request(`/process/detail/select/${processUuid}`, {
    method: 'GET',
  });
}

// 流程列表-通过流程版本查询流程详情
export async function getAppManageDetail(processDetailUuid: string) {
  return request(`/opt/oldMobile/1.0.0/web/process/detail/select/data`, {
    method: 'POST',
    data: {
      processDetailUuid: processDetailUuid,
    },
  });
}

/** 流程列表-使用统计 */
// 概况分析
export async function getGeneralStatisticData(params: statisticParamsType) {
  return request(`/process/execute/statistics/survey-analysis`, {
    method: 'post',
    data: params,
  });
}

// 使用趋势
export async function getStatisticsUseTrendData(params: statisticParamsType) {
  return request(`/process/execute/statistics/trend`, {
    method: 'post',
    data: params,
  });
}

// 失败原因
export async function getStatisticFailedResonData(params: statisticParamsType) {
  return request(`/process/execute/statistics/fail-chart`, {
    method: 'post',
    data: params,
  });
}

// 【manager】查询流程所属版本集合
export async function getProcessVersionListByUuid(params: any) {
  return request(`/process/select-process-belong-version-list`, {
    method: 'post',
    data: params,
  });
}

/**
 * 【manager】流程图详情
 *  根据流程版本uuid查询流程图数据
 */
export async function getProcessFlowData(params: { detailUuid: string }) {
  return request(`/process/flow-info`, {
    method: 'post',
    data: params,
  });
}

/**
 * 【manager】流程块图详情
 *  根据流程版本uuid及流程块id查询组件树数据
 */
export async function getProcessTaskBlockData(params: {
  detailUuid: string;
  taskId: string;
}) {
  return request(`/process/task-info`, {
    method: 'post',
    data: params,
  });
}

/**
 *流程列表编辑部门
 */
export async function changeBuMen(params: { uuid: string; deptIds: string[] }) {
  return request(`/process/dept-update`, {
    method: 'post',
    data: params,
  });
}

export default {
  getProcess,
  processPackageUpload,
  processSave,
  getProcessDetail,
  curProcessDelete,
  processDelete,
  processPkgDelete,
  processIsPublish,
  processDownload,
  updateTime,
  updateProcess,
  getAppManageOptions,
  getAppManageVersionOptions,
  getAppManageDetail,
  getGeneralStatisticData,
  getStatisticsUseTrendData,
  getStatisticFailedResonData,
  getProcessVersionListByUuid,
  getProcessFlowData,
  getProcessTaskBlockData,
};

import request from '@/utils/request';

export type CalendarListRes = {
  id: number | string;
  name: string;
}[];

export const getCalendarList = (data: { name: string }) => {
  return request<CalendarListRes>('/calendar/list', {
    data,
  });
};

export const checkArrangeUpgrade = (data: {
  arrangeId: string;
  arrangeVersionId: string;
}) => {
  return request<{
    arrangeId: string;
    needUpgrade: boolean;
    upgradeToMaxDetailId: string;
    upgradeToMaxDetailVersion: string;
  }>('/opt/oldMobile/1.0.0/web/flow-arrange/check-upgrade', {
    method: 'POST',
    data,
  });
};

/** 更新任务编排到最大版本 */
export const arrangeUpgradeMax = (data: {
  jobUuid: string;
  arrangeId: string;
}) => {
  return request<{
    arrangeId: string;
    needUpgrade: boolean;
    upgradeToMaxDetailId: string;
    upgradeToMaxDetailVersion: string;
  }>('/opt/oldMobile/1.0.0/web/job/arrange-upgrade-max', {
    method: 'POST',
    data,
  });
};

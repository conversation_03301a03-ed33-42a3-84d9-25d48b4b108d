/**
 * 流程块管理接口文档
 * https://yapi.ii-ai.tech/project/368/interface/api/cat_11395
 */
import request from '@/utils/request';

/** 企业流程块分页列表 */
export async function getProcessBlockList(params: any) {
  return request('/process-block/publish/page', {
    method: 'POST',
    data: params,
  });
}

/** 流程块下载最新版本 */
export async function processBlockLastedDownload(params: any) {
  return request('/process-block/publish/lasted-download', {
    method: 'POST',
    responseType: 'blob',
    data: params,
  });
}

/** 流程块下载具体版本 */
export async function processBlockDownload(params: any) {
  return request('/process-block/publish/detail/download', {
    method: 'POST',
    responseType: 'blob',
    data: params,
  });
}

/** 删除企业流程块 */
export async function processBlockDelete(params: any) {
  return request('/process-block/publish/delete', {
    method: 'POST',
    data: params,
  });
}

/** 企业流程块 详情 */
export async function getProcessBlockDetail(params: any) {
  return request('/process-block/publish/info', {
    method: 'POST',
    data: params,
  });
}

/** 企业流程块 历史版本 */
export async function getProcessBlockDetailList(params: any) {
  return request('/process-block/publish/detail/page', {
    method: 'POST',
    data: params,
  });
}

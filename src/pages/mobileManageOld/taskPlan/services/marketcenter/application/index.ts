/**
 * 任务管理接口文档
 * https://yapi.ii-ai.tech/project/368/interface/api/cat_10135
 */
import request from '@/utils/request';

/** 流程列表 */
export async function getApplicationList(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/list', {
    method: 'POST',
    data: params,
  });
}

/** 流程详情 */
export async function getApplicationDetail(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/detail', {
    method: 'POST',
    data: params,
  });
}

/** 流程下架 */
export async function stateOffApplication(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/state-off', {
    method: 'POST',
    data: params,
  });
}

/** 流程上架 */
export async function stateOnApplication(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/state-on', {
    method: 'POST',
    data: params,
  });
}

/** 流程详情下架 */
export async function detailStateOffApplication(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/detail-state-off', {
    method: 'POST',
    data: params,
  });
}

/** 流程详情上架 */
export async function detailStateOnApplication(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/detail-state-on', {
    method: 'POST',
    data: params,
  });
}

/** 流程详情删除 */
export async function detailDelete(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/detail-delete', {
    method: 'POST',
    data: params,
  });
}

/** 编辑流程人共花费时间  已废弃 */
export async function detailHumanMinute(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/detail-human-minute', {
    method: 'POST',
    data: params,
  });
}

/** 编辑流程人共花费时间  新版 */
export async function applicationUpdate(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/update', {
    method: 'POST',
    data: params,
  });
}

/** 流程详情下载 */
export async function detailDownload(params: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/detail-download', {
    method: 'POST',
    responseType: 'blob',
    data: params,
  });
}

/** 视频下载 */
export async function videoDownload(UUid: any) {
  return request('/opt/oldMobile/1.0.0/web/application/data/video-download', {
    method: 'POST',
    responseType: 'blob',
    data: {
      applicationUUID: UUid,
    },
  });
}

/** 流程共享-选择流程列表下拉 */
export async function getAppMarketOptions() {
  return request('/opt/oldMobile/1.0.0/web/application/data/main-pull-down', {
    method: 'POST',
  });
}

/** 流程共享-选择单个流程版本列表下拉 */
export async function getAppMarketVersionOptions(applicationUUID: string) {
  return request(`/opt/oldMobile/1.0.0/web/application/data/detail-pull-down`, {
    method: 'POST',
    data: {
      applicationUUID,
    },
  });
}

/** 流程共享-通过流程版本查询流程详情 */
export async function getAppMarketDetail(applicationDetailUUID: string) {
  return request(`/opt/oldMobile/1.0.0/web/application/data/version`, {
    method: 'POST',
    data: {
      applicationDetailUUID,
    },
  });
}

/**
 * 【manager】流程图详情
 *  根据流程版本uuid查询流程图数据
 */
export async function getAppMarketFlowData(params: { detailUuid: string }) {
  return request(`/opt/oldMobile/1.0.0/web/application/data/flow-info`, {
    method: 'post',
    data: params,
  });
}

/**
 * 【manager】流程块图详情
 *  根据流程版本uuid及流程块id查询组件树数据
 */
export async function getAppMarketTaskBlockData(params: {
  detailUuid: string;
  taskId: string;
}) {
  return request(`/opt/oldMobile/1.0.0/web/application/data/task-info`, {
    method: 'post',
    data: params,
  });
}

/**
 *企业市场编辑部门
 */
export async function changeShiChangBuMen(params: {
  uuid: string;
  deptId: string;
}) {
  return request(`/opt/oldMobile/1.0.0/web/application/data/dept-update`, {
    method: 'post',
    data: params,
  });
}
export default {
  getApplicationList,
  getApplicationDetail,
  stateOffApplication,
  stateOnApplication,
  detailStateOffApplication,
  detailStateOnApplication,
  detailDelete,
  detailHumanMinute,
  detailDownload,
  videoDownload,
  getAppMarketOptions,
  getAppMarketVersionOptions,
  getAppMarketDetail,
  getAppMarketFlowData,
  getAppMarketTaskBlockData,
};

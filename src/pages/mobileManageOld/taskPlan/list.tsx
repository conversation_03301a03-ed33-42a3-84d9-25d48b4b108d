import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'umi';
import { Space, Table, Input, Select, Button, message, Spin, Typography, Switch, Tag, Popover } from 'antd';
import _ from 'lodash';
import { PlusOutlined, SyncOutlined } from '@ant-design/icons';
import { IconFont } from 'main/MyIcon';
import { useTaskPlanStore } from './store';
import { HelpToolTip } from 'main/HelpToolTip';
import moment from 'moment';
import AddTaskEntry from './components/update/add';
import EditTaskEntry from './components/update/edit';
import TaskDetail from './components/detail';
import TaskManageServiceApi from '../services';
import AppApi from '../services2';
import { NO_DATA_IN_TABLE_COLUMN_DISPLY, AuthListApiFailedTip } from '@/utils/constants/const';
import {
  ExecuteTypeEnum,
  ExecuteTypeMap,
  ExecuteTypeArr,
  TaskPlanTypeEnum,
  TaskPlanTypeEnumMap,
  EnableTypeEnum,
  EnableTypeEnumList,
  TaskStatusArr,
  TaskStatusEnum,
} from '@/types/taskcenter/type';
import { AppChannelEnum, AppChannelMap } from '@/types/application/type';
import type { TaskListRecordType, PublicProcessDetailsOnListType } from '@/types/taskcenter/interface';
import { MenuProps } from 'antd/es/menu';
import StyleText from '@/components/StyleText';
import { useFrameWorkContext } from '@/exposes/deopCmdEntry/context';
import type { CommonListType } from '@/types/interface';
import type { AppType } from '@/types/application/interface';
import { SzSelect, SzDropdown, SzDatePicker, SZDatePickerType, SzTooltip, SzConfirm } from 'main/SzComponents';
import { GetPopupContainerLayerType } from './const';
import styles from './index.less';
import TaskApi from '../services';
import { useRequest } from 'ahooks';

const { Search } = Input;
const { Option } = Select;

const PCTaskManage = (props: { jobStatus: Array<number> }) => {
  const { jobStatus } = props;
  const frameWorkContextInfo = useFrameWorkContext();
  const [pageSearchParams] = useSearchParams();
  const pageCode = pageSearchParams.get('code');
  const { current, taskListReload, searchForm, size, total, queryTaskList, save, resetData, loading } =
    useTaskPlanStore();

  const {} = useRequest(TaskApi.getMobileTaskmanageDetail);

  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [records, setRecord] = useState([]);
  const [curJobUuid, setCurJobUuid] = useState<string | null>(null);
  const [isGroup, setIsGroup] = useState<boolean>(false);
  const [taskPlanType, setTaskPlanType] = useState<TaskPlanTypeEnum>(TaskPlanTypeEnum.SINGLE);
  const [addTaskVisiable, setAddTaskVisiable] = useState<boolean>(false);
  const [appList, setAppList] = useState<CommonListType[]>([]);
  const [appListLoading, setAppListLoading] = useState<boolean>(false);
  const [editTaskDetailVisible, setEditTaskDetailVisible] = useState<boolean>(false);
  /** 查询列表数据 */
  const queryList = async (params?: any, isShowLoading = true) => {
    const searchParams = { ...params };
    if (params) {
      /** 主动更新store的信息 */
      save(searchParams);
    }
    const payload = {
      ...searchForm,
      ...searchParams?.searchForm,
      processChannel:
        searchForm?.processChannel === 'ALL' || searchParams?.searchForm?.processChannel === 'ALL'
          ? ''
          : searchForm?.processChannel,
      jobStatus: jobStatus,
      pageNo: searchParams?.current || current,
      pageSize: Number(searchParams?.size) || size,
    };
    const res = await queryTaskList(payload);
    setRecord(res.records);
  };
  /** 按钮权限 */
  const { pageTabsAndbtnsPremission } = frameWorkContextInfo;
  const Auth = useMemo(() => {
    if (!pageCode) return {};
    const pageTabAndbuttonsPremission = pageTabsAndbtnsPremission?.[pageCode] || [];
    return {
      // add: pageTabAndbuttonsPremission.includes('add'),
      // detail: pageTabAndbuttonsPremission.includes('detail'),
      // delete: pageTabAndbuttonsPremission.includes('delete'),
      // edit: pageTabAndbuttonsPremission.includes('edit'),
      // list: pageTabAndbuttonsPremission.includes('list'),
      // control: pageTabAndbuttonsPremission.includes('control'),
      add: true,
      detail: true,
      delete: true,
      edit: true,
      list: true,
      control: true,
    };
  }, [pageTabsAndbtnsPremission]);

  const defaultCreatorArr = [{ key: '', name: '全部创建人' }];
  const [creatorArr, setCreatorArr] = useState<{ key: string; name: string }[]>(defaultCreatorArr);

  // 首页初始化
  const initPage = () => {
    if (!Auth.list) {
      AuthListApiFailedTip();
      return;
    }
    queryList();
    if (!Auth.detail) {
      message.error('暂无详情权限，请联系管理员开通');
      // eslint-disable-next-line no-useless-return
      return;
    }
  };

  /** 强制停止手动计划 */
  const forcedStopTask = (record: any) => {
    const run = async () => {
      await TaskManageServiceApi.getForcedStop({
        jobUuid: record.jobUuid,
      }).then(() => {
        queryList({ current: 1 }, false);
      });
    };
    SzConfirm({
      title: '此操作将影响所有正在触发本计划的机器人，确定强制停止？',
      content: '',
      onOk: () => {
        run();
      },
    });
  };

  /** 强制停止非手动的计划 */
  const stopTask = (record: any) => {
    const run = async () => {
      await TaskManageServiceApi.getTaskmanageStop({
        jobUuid: record.jobUuid,
      }).then(() => {
        queryList({ current: 1 }, false);
      });
    };
    SzConfirm({
      title: '停止后机器人上该计划将终止任务，非暂停计划，确定停止？',
      content: '',
      onOk: () => {
        run();
      },
    });
  };

  /** 顶部搜索数据变更接口, 除日期接口以外的 */
  const searchBarUpdate = (key: string, val?: any) => {
    queryList({
      searchForm: {
        ...searchForm,
        [key]: val,
      },
      current: 1,
    });
  };

  /** 删除计划 */
  const delTask = (record: TaskListRecordType) => {
    const run = async () => {
      await TaskManageServiceApi.getTaskmanageDelete({
        jobUuid: record.jobUuid,
      }).then(() => {
        queryList({ current: 1 });
      });
    };
    SzConfirm({
      title: '确认删除？',
      content: '',
      onOk: () => {
        run();
      },
    });
  };

  const runTask = (record: any) => {
    const run = async () => {
      await TaskManageServiceApi.getTaskManageRun(record.jobUuid)
        .then(() => {
          queryList({}, false);
        })
        .catch((err) => {
          console.log(err);
        });
    };
    SzConfirm({
      title: '确认新建一次任务？',
      content: '',
      onOk: () => {
        run();
      },
    });
  };

  // 开启停止定时计划
  const handleScheduleStatus = (record: any) => {
    const run = async () => {
      if (record.isSchedule) {
        await TaskManageServiceApi.stopSchedule({
          jobUuid: record.jobUuid,
        }).then(() => {
          queryList({ current: 1 }, false);
        });
      } else {
        await TaskManageServiceApi.startSchedule({
          jobUuid: record.jobUuid,
        }).then(() => {
          queryList({ current: 1 }, false);
        });
      }
    };
    SzConfirm({
      title: record.isSchedule ? '停止' : '开启',
      content: '',
      onOk: () => {
        run();
      },
    });
  };

  const generateMenu = (record: any): MenuProps['items'] => {
    const itemArr = [];
    if (record.executeType !== ExecuteTypeEnum.MANUAL) {
      itemArr.push({
        key: '1',
        label: (
          <StyleText
            type="link"
            onClick={() => {
              runTask(record);
            }}>
            {'新建一次任务'}
          </StyleText>
        ),
      });
    }

    if (record.status === TaskStatusEnum.RUNNING) {
      itemArr.push({
        key: '2',
        label: (
          <StyleText
            type="link"
            onClick={
              record.executeType === ExecuteTypeEnum.MANUAL
                ? () => {
                    forcedStopTask(record);
                  }
                : () => {
                    stopTask(record);
                  }
            }>
            {'停止所有任务'}
          </StyleText>
        ),
      });
    }

    if (record.executeType === ExecuteTypeEnum.TIMING) {
      itemArr.push({
        key: '3',
        label: (
          <StyleText
            type="link"
            onClick={() => {
              handleScheduleStatus(record);
            }}>
            {record.isSchedule ? '停止' : '启动'}
            {'定时器'}
          </StyleText>
        ),
      });
    }
    // 计划状态-启用 触发方式-立即触发，定时触发

    return itemArr;
  };

  const changeFormStatus = (record: TaskListRecordType) => {
    const { jobUuid, enabled } = record;
    const title: string = enabled ? '确认禁用吗？' : '确认启用吗？';
    const descs: string[] = enabled
      ? ['禁用任务计划的影响如下：', '禁用前生成的任务不受影响，禁用后将不再生成任务。']
      : ['启用后，将按触发方式生成任务。'];
    SzConfirm({
      title,
      content: (
        <div>
          {descs.map((desc) => (
            <div key={desc}>{desc}</div>
          ))}
        </div>
      ),
      okText: '确认',
      onOk() {
        if (enabled) {
          TaskApi.disable({ jobUuid }).then(() => {
            message.success('修改成功');
            queryList();
          });
        } else {
          TaskApi.enable({ jobUuid }).then(() => {
            message.success('修改成功');
            queryList();
          });
        }
      },
    });
  };

  const commonColumns = [
    {
      title: '计划名称',
      dataIndex: 'jobName',
      key: 'jobName',
      width: 150,
      ellipsis: true,
      render: (text: string, record: TaskListRecordType) => {
        return (
          <SzTooltip title={text} placement="topLeft" popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}>
            <div className="flex gap-[4px]">
              <span className="flex-1 truncate">{text}</span>
              {record?.humanRobotInteractiveType && <Tag color="processing">人机</Tag>}
            </div>
          </SzTooltip>
        );
      },
    },
    {
      title: '关联流程',
      dataIndex: 'processDetails',
      key: 'processDetails',
      width: 250,
      render: (val: PublicProcessDetailsOnListType[], record: TaskListRecordType) => {
        if (record.jobType === TaskPlanTypeEnum.SINGLE) {
          return (
            <SzTooltip
              title={val[0]?.processName}
              placement="topLeft"
              popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}>
              {val[0]?.processName || NO_DATA_IN_TABLE_COLUMN_DISPLY}
            </SzTooltip>
          );
        } else if (record?.jobType === TaskPlanTypeEnum.GROUP) {
          const text = (
            <>
              流程计划组
              <span className="text-note">
                {'(包含'}
                {record?.processDetails?.length || 0}
                {'个流程)'}
              </span>
            </>
          );
          const processNames = record?.processDetails?.map((item) => item.processName);
          const tooltipTile = processNames?.map((item, index) => {
            return (
              <>
                <label>
                  {index + 1}. {item}
                </label>
                {index !== processNames.length - 1 && <br />}
              </>
            );
          });
          return (
            <SzTooltip
              title={
                <>
                  {/* {I18N.botmanage.PCBot.baoHanLiuCheng}
                    <br /> */}
                  {tooltipTile}
                </>
              }
              placement="topLeft"
              popupContainerLayer={GetPopupContainerLayerType.FOURPARENT}>
              {text}
            </SzTooltip>
          );
        } else if (record.jobType === TaskPlanTypeEnum.ARRANGE) {
          return (
            <Popover
              placement="bottomLeft"
              title={() => <header style={{ fontWeight: 500, fontSize: '14px' }}>包含流程</header>}
              content={
                <>
                  <div className="max-h-40 overflow-y-scroll">
                    {record.processDetails ? (
                      record.processDetails.map((item) => {
                        return (
                          <p
                            style={{
                              paddingRight: '80px',
                              fontWeight: 400,
                              fontSize: '12px',
                            }}
                            className="flex items-center gap-1 truncate"
                            key={item.processName}>
                            <IconFont className="text-[20px]" type="iconopqiantailiucheng" />
                            {item.processName}
                          </p>
                        );
                      })
                    ) : (
                      <p
                        style={{
                          width: '250px',
                          paddingRight: '80px',
                          fontWeight: 400,
                          fontSize: '12px',
                        }}>
                        暂无更多
                      </p>
                    )}
                  </div>
                </>
              }>
              <span className="flex items-center gap-[4px]">
                <IconFont className="text-[20px]" type="iconopqiantailiucheng" />
                <span className="flex-1 truncate">{record?.arrangeName}</span>
                包含
                <span style={{ color: '#0085FF' }}>{record?.processDetails ? record?.processDetails.length : '0'}</span>
                个流程
              </span>
            </Popover>
          );
        } else {
          return NO_DATA_IN_TABLE_COLUMN_DISPLY;
        }
      },
    },
    // {
    //   title: '计划类型',
    //   dataIndex: 'jobType',
    //   key: 'jobType',
    //   width: 100,
    //   render: (value: 0 | 1 | 2) => TaskPlanTypeEnumMap[value],
    // },
    {
      title: '触发方式',
      dataIndex: 'executeType',
      key: 'executeType',
      width: 100,
      render: (text: ExecuteTypeEnum, record: TaskListRecordType) => {
        if (text === ExecuteTypeEnum.TIMING) {
          return (
            <Space size={[3, 0]}>
              <>{ExecuteTypeMap[text]}</>
              <HelpToolTip
                title={record?.enabled ? '已开启' : '已关闭'}
                type="icondingshi"
                fontSize="14px"
                color={record?.enabled ? '#2F9AFF' : '#334355'}
                getPopupContainer={(triggerNode: any) => {
                  return triggerNode.parentElement.parentElement.parentElement;
                }}
              />
            </Space>
          );
        }
        return ExecuteTypeMap[text] || NO_DATA_IN_TABLE_COLUMN_DISPLY;
      },
    },
    {
      title: '定时规则',
      dataIndex: 'jobDescription',
      key: 'jobDescription',
      ellipsis: true,
      width: 150,
      render: (value: string) => value || NO_DATA_IN_TABLE_COLUMN_DISPLY,
    },
    // {
    //   title: '状态',
    //   dataIndex: 'enabled',
    //   key: 'enabled',
    //   width: 80,
    //   render: (_: any, record: TaskListRecordType) => {
    //     return (
    //       <Switch
    //         unCheckedChildren="禁用"
    //         checkedChildren="启用"
    //         checked={Boolean(record.enabled)}
    //         onChange={() => {
    //           changeFormStatus(record);
    //         }}
    //       />
    //     );
    //   },
    // },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 150,
      ellipsis: true,
      render: (value: any) => value || NO_DATA_IN_TABLE_COLUMN_DISPLY,
    },
    // {
    //   title: '处理人',
    //   dataIndex: 'humanRobotInteractiveHandlers',
    //   key: 'humanRobotInteractiveHandlers',
    //   width: 150,
    //   ellipsis: true,
    //   render: (value: any) =>
    //     (
    //       (value?.length > 0 && value.map((item: any) => item.handlerNames)) ||
    //       []
    //     ).join(',') || NO_DATA_IN_TABLE_COLUMN_DISPLY,
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 170,
      ellipsis: true,
    },
  ];
  let columns: any[] = [...commonColumns];
  if (Auth.list) {
    columns = [
      ...columns,
      {
        title: '操作',
        dataIndex: 'ypmc',
        key: 'ypmc',
        fixed: 'right',
        width: 120,
        render: (text: any, record: TaskListRecordType) => {
          return (
            <Space>
              {/* executeType===9 定时触发的 executeType === 1手动触发 isSchedule定时是否开启 true-是,false否 */}
              {Auth.control && (
                <>
                  {record.enabled === EnableTypeEnum.DISABLE || record.executeType === ExecuteTypeEnum.MANUAL ? (
                    <SzTooltip title={'暂无可控制操作'} popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}>
                      <Typography.Text disabled>{'控制'}</Typography.Text>
                    </SzTooltip>
                  ) : (
                    <SzDropdown
                      popupContainerLayer={GetPopupContainerLayerType.FIVEPARENT}
                      trigger={['click']}
                      menu={{
                        items: generateMenu(record),
                      }}
                      placement="top"
                      arrow>
                      <Typography.Link shizai-key="control">{'控制'}</Typography.Link>
                    </SzDropdown>
                  )}
                </>
              )}

              {Auth.detail ? (
                <Typography.Link
                  shizai-key="detail"
                  onClick={() => {
                    setCurJobUuid(record.jobUuid);
                    setTaskDetailVisible(true);
                    setIsGroup(record.isGroup || false);
                  }}>
                  {'详情'}
                </Typography.Link>
              ) : null}
              {Auth.edit && (
                <a
                  shizai-key="edit"
                  onClick={() => {
                    setIsGroup(record.isGroup!);
                    setCurJobUuid(record.jobUuid);
                    setEditTaskDetailVisible(true);
                  }}>
                  {'编辑'}
                </a>
              )}
              {Auth.delete ? (
                <Typography.Link type="danger" shizai-key="delete" onClick={() => delTask(record)}>
                  {'删除'}
                </Typography.Link>
              ) : null}
            </Space>
          );
        },
      },
    ];
  }

  useEffect(() => {
    initPage();

    /** 获取全部创建人列表 */
    TaskManageServiceApi.getTaskManageUserSelectList()
      .then((res) => {
        const newData: any[] = [...defaultCreatorArr];
        (res || []).map((item: any) => {
          newData.push({
            key: item.userId,
            name: item.account,
          });
          return res;
        });
        setCreatorArr(newData);
      })
      .catch((err) => {
        console.log(err);
      });

    return () => {
      resetData();
    };
  }, []);

  const formatAppList = (result: AppType[]) => {
    const newAppList: CommonListType[] = [];
    result.map((item: AppType) => {
      newAppList.push({
        key: item.mainUUID,
        value: item.name,
        extra: item?.author,
      });
      return item;
    });
    return newAppList;
  };
  useEffect(() => {
    setAppListLoading(true);
    AppApi.getAppAndAppVersionList({
      processChannels: searchForm?.processChannel === 'ALL' ? [0, 1] : [searchForm?.processChannel],
    })
      .then((res: any) => {
        let list: CommonListType[] = [];

        if (Number(searchForm?.processChannel) === AppChannelEnum.APPMANAGEMENT) {
          list = formatAppList(res?.processMainList || []);
        } else if (Number(searchForm?.processChannel) === AppChannelEnum.APPMARKET) {
          list = formatAppList(res?.applicationMainList || []);
        } else {
          list = [...formatAppList(res?.applicationMainList || []), ...formatAppList(res?.processMainList || [])];
        }
        setAppList(list);
      })
      .finally(() => {
        setAppListLoading(false);
      });
  }, [searchForm?.processChannel]);

  useEffect(() => {
    queryList({ current: 1 });
  }, [taskListReload]);
  return (
    <>
      {/** 列表页 */}
      <div>
        {Auth.list && (
          <div className={`${styles['header-filter-part']} ${styles['flex']}`}>
            <Space wrap align="start">
              <Search
                onSearch={(val: string) => {
                  searchBarUpdate('jobName', val);
                }}
                placeholder={'请输入计划名称'}
                allowClear
                style={{ width: 180 }}
              />
              <Input.Group compact>
                <SzSelect
                  // <AppChannelEnum | ''>
                  popupContainerLayer={GetPopupContainerLayerType.PROGRIDCONTENT}
                  style={{ width: 100 }}
                  defaultValue={'ALL'}
                  onChange={(val: AppChannelEnum | '') => {
                    setAppList([]);

                    queryList({
                      searchForm: {
                        processChannel: val,
                        processUuid: undefined,
                        arrangeId: undefined,
                      },
                      current: 1,
                    });
                  }}>
                  <Option key={'ALL'} value={'ALL'}>
                    {'全部流程'}
                  </Option>
                  {Object.entries(AppChannelMap).map(([key, val]: any) => {
                    if (Number(key) !== AppChannelEnum.APPMANAGEMENT) return null;
                    return (
                      <Option key={key} value={key}>
                        {val}
                      </Option>
                    );
                  })}
                </SzSelect>
                <SzSelect
                  showSearch
                  placeholder={'请选择流程'}
                  optionFilterProp="label"
                  optionLabelProp="label"
                  disabled={searchForm?.processChannel === 'ALL'}
                  value={searchForm?.processUuid}
                  dropdownRender={(options: any) => {
                    return (
                      <>
                        {appListLoading ? (
                          <Spin spinning={true}>
                            <div className={'select-no-data'}>{'暂无数据'}</div>
                          </Spin>
                        ) : (
                          options
                        )}
                      </>
                    );
                  }}
                  onSelect={(val: any) => {
                    if (Number(searchForm?.processChannel) === AppChannelEnum.ARRANGE) {
                      searchBarUpdate('arrangeId', val);
                    } else {
                      searchBarUpdate('processUuid', val);
                    }
                  }}
                  allowClear
                  onClear={() => {
                    queryList({
                      searchForm: {
                        ...searchForm,
                        processUuid: undefined,
                        arrangeId: undefined,
                      },
                      current: 1,
                    });
                  }}
                  style={{ width: 160 }}>
                  {(appList || []).map((appItem: CommonListType) => {
                    if (appItem.key) {
                      return (
                        <Option value={appItem.key} key={appItem.key} label={appItem.value}>
                          {appItem.value}
                        </Option>
                      );
                    }
                    return '';
                  })}
                </SzSelect>
              </Input.Group>
              <SzSelect
                placeholder={'选择执行方式'}
                popupContainerLayer={GetPopupContainerLayerType.PROGRIDCONTENT}
                defaultValue={ExecuteTypeEnum.ALL}
                onChange={(val: ExecuteTypeEnum) => {
                  searchBarUpdate('executeType', val);
                }}
                style={{ width: 128 }}>
                {ExecuteTypeArr.map((item: { key: ExecuteTypeEnum; name: string }) => {
                  return (
                    <Option value={item.key} key={item.key}>
                      {item.name}
                    </Option>
                  );
                })}
              </SzSelect>
              <SzSelect
                placeholder={'选择状态'}
                popupContainerLayer={GetPopupContainerLayerType.PROGRIDCONTENT}
                defaultValue={EnableTypeEnum.ALL}
                onChange={(val: EnableTypeEnum) => {
                  searchBarUpdate('enabled', val);
                }}
                style={{ width: 100 }}>
                {EnableTypeEnumList.map((item: { key: EnableTypeEnum; name: string }) => {
                  return (
                    <Option value={item.key} key={item.key}>
                      {item.name}
                    </Option>
                  );
                })}
              </SzSelect>
              <SzSelect
                placeholder={'请选择创建人'}
                popupContainerLayer={GetPopupContainerLayerType.PROGRIDCONTENT}
                defaultValue={defaultCreatorArr[0].key}
                onChange={(val: string) => {
                  searchBarUpdate('creator', val);
                }}
                style={{ width: 125 }}
                showSearch
                optionFilterProp={'children'}>
                {creatorArr.map((item: { key: string; name: string }) => {
                  return (
                    <Option value={item.key} key={item.key}>
                      {item.name}
                    </Option>
                  );
                })}
              </SzSelect>
              <SzDatePicker
                dateType={SZDatePickerType.RANGEPICKER}
                popupContainerLayer={GetPopupContainerLayerType.PROGRIDCONTENT}
                format="YYYY-MM-DD"
                onChange={(val: any) => {
                  queryList({
                    searchForm: {
                      ...searchForm,
                      startTime: val && val[0] ? moment(val[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss') : '',
                      endTime: val && val[1] ? moment(val[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss') : '',
                    },
                    current: 1,
                  });
                }}
                style={{ width: 240 }}
              />
            </Space>
            <Space>
              <Button
                style={{ width: 110 }}
                shizai-key="refresh"
                onClick={() => {
                  queryList();
                }}>
                <SyncOutlined rev={1} />
                {'刷新'}
              </Button>
              <Button
                shizai-key="add"
                hidden={!Auth.add}
                type="primary"
                onClick={() => {
                  setIsGroup(false);
                  setTaskPlanType(TaskPlanTypeEnum.SINGLE);
                  setAddTaskVisiable(true);
                }}
                icon={<PlusOutlined rev={1} />}
                style={{ width: 110 }}>
                添加
              </Button>
            </Space>
          </div>
        )}
        <Table
          loading={loading}
          key="jobUuid"
          rowKey={(record: TaskListRecordType) => String(record?.jobId)}
          dataSource={records}
          columns={columns}
          scroll={{ x: 1420 }}
          pagination={{
            size: 'small',
            current: Number(current),
            pageSize: Number(size),
            total: Number(total),
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (val: number) => `共${val}条`,
            onChange: (pageNum?: number, pageSize?: number) => {
              queryList({
                current: pageNum || current,
                size: pageSize || size,
              });
            },
          }}
        />
      </div>

      {/** 弹窗-新建计划 */}
      <AddTaskEntry
        visible={addTaskVisiable}
        taskPlanType={taskPlanType}
        isGroup={isGroup}
        onClose={() => {
          queryList();
          setAddTaskVisiable(false);
        }}
      />

      {/** 弹窗-计划详情 */}
      <TaskDetail
        jobUuid={curJobUuid}
        visible={taskDetailVisible}
        isGroup={isGroup}
        onClose={() => {
          setCurJobUuid(null);
          setTaskDetailVisible(false);
          queryList(undefined, false);
        }}
      />

      {/** 弹窗-编辑计划 */}
      <EditTaskEntry
        jobUuid={curJobUuid}
        visible={editTaskDetailVisible}
        isGroup={isGroup}
        onClose={() => {
          setCurJobUuid(null);
          setEditTaskDetailVisible(false);
        }}
      />
    </>
  );
};

export default PCTaskManage;

/**
 * Antd内存在getPopupContainer属性的返回层级
 * 涉及组件： DatePicker, Select, TreeSelect, DropDown, Tooltip
 */
export enum GetPopupContainerLayerType {
  // 原始：document.body
  ORIGIN = 'origin',
  // 基于右侧content的最外层父类（.global-content），目前主要是队列子页面比较特殊
  GLOBALCONTENT = 'global-content',
  // 基于右侧content的最外层父类（.ant-pro-grid-content)，目前大多都是
  PROGRIDCONTENT = 'ant-pro-grid-content',
  // 向上一层，默认
  ONEPARENT = 'oneParent',
  // 向上两层
  TWOPARENT = 'twoParent',
  // 向上三层，正常表格内
  THREEPARENT = 'threeParent',
  // 向上四层，在表格基础上单独包裹了一层
  FOURPARENT = 'fourParent',
  // 向上五层
  FIVEPARENT = 'fiveParent',
  // 向上六层
  SIXPARENT = 'sixParent',
  // 向上7层
  SEVENPARENT = 'sevenParent',
}

export interface PopupContainerLayerProps {
  popupContainerLayer?: GetPopupContainerLayerType;
}

/**
 * 自定义组件标签的getPopupContainer方法
 * @param triggerNode 当前html元素
 * @param defaultType 默认的GetPopupContainerLayerType类型
 * @param type        当前的GetPopupContainerLayerType类型
 * @returns
 */
export const szGetPopupContainer = (
  triggerNode: any,
  defaultType?: GetPopupContainerLayerType,
  type?: GetPopupContainerLayerType,
) => {
  if (!triggerNode) {
    return document.body;
  }
  const newType = type || defaultType;
  if (newType === GetPopupContainerLayerType.GLOBALCONTENT) {
    if (document.getElementsByClassName('global-content').length > 0) {
      return document.getElementsByClassName('global-content')[0];
    }
    return document.body;
  }
  if (newType === GetPopupContainerLayerType.PROGRIDCONTENT) {
    if (document.getElementsByClassName('ant-pro-grid-content').length > 0) {
      return document.getElementsByClassName('ant-pro-grid-content')[0];
    }
    return document.body;
  }

  switch (newType) {
    case GetPopupContainerLayerType.ORIGIN:
      return document.body;
    case GetPopupContainerLayerType.ONEPARENT:
    default:
      return triggerNode.parentElement;
    case GetPopupContainerLayerType.TWOPARENT:
      return triggerNode.parentElement.parentElement;
    case GetPopupContainerLayerType.THREEPARENT:
      return triggerNode.parentElement.parentElement.parentElement;
    case GetPopupContainerLayerType.FOURPARENT:
      return triggerNode.parentElement.parentElement.parentElement
        .parentElement;
    case GetPopupContainerLayerType.FIVEPARENT:
      return triggerNode.parentElement.parentElement.parentElement.parentElement
        .parentElement;
    case GetPopupContainerLayerType.SIXPARENT:
      return triggerNode.parentElement.parentElement.parentElement.parentElement
        .parentElement.parentElement;
    case GetPopupContainerLayerType.SEVENPARENT:
      return triggerNode.parentElement.parentElement.parentElement.parentElement
        .parentElement.parentElement.parentElement;
  }
};

export const TASKCENTER_TEXT = {
  TASK_TIP_ENTER_POSITIVE_INTEGER: '请输入正整数',
  APP_CHANNEL_HELP_TIP:
    '流程列表为【流程管理-流程列表】下的流程；流程共享为【共享管理-流程共享】下的流程。',
  TASK_NMAE_HELP_TIP:
    '支持中文、大小写字母、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头，不超过100个字符',
  TASK_APPHUMANTIME_HELP_TIP:
    '预估人工时间：预估人工时间来源于上传流程包时所填写的时间，如需要修改请至【资源中心-流程管理】/【市场管理-流程共享】，找到对应流程和版本，进行数据修改',
  TASK_SOURCE_NOTICE:
    '注：此流程来源设计器，由设计器发布到控制器的流程暂时还不支持引用参数的编辑',
  TASK_TIMING_LIMITATION_LABEL: '定时时效：',
  TASK_TIMING_LIMITATION:
    '定时时效：若设置为指定有效时间，则定时任务只会在有效时间内才会执行，适用于某一段时间需要规律执行的场景。设置为长期有效，则该任务会一直按规律执行，永不停止。',
  TASK_NUMBER_OF_TIMES_PER_EXECUTION_LABEL: '每次执行次数',
  TASK_NUMBER_OF_TIMES_PER_EXECUTION:
    '每次执行次数：每次执行任务时将按设置条数执行多遍。例，设置为每日10点执行，每次执行次数为3，则每天上午10点将运行该任务3遍；设置立即执行，每次执行次数为3，则立即执行该任务3遍。',
  TASK_MAX_WAITING_TIMEOUT_LABEL: '等待超时时间(分钟)',
  TASK_MAX_WAITING_TIMEOUT:
    '等待超时时间：当需要执行的任务很多，但是机器人资源有限时，会存在需要排队的情况。开启等待超时销毁后则排队等待超过设定的时间，则该次执行等待超时后会自行清除，继续执行后面排队的任务，避免大量任务长时间等待造成的任务堵塞',
  TASK_MAX_QUEUE_NUMBER_LABEL: '最大排队数量',
  TASK_MAX_PALING_OVER_TIME: '运行超时警告',
  TASK_MAX_PALING_OVER_TIME_Plo:
    '流程最长运行时间，超过设定时间将发生告警。需先在消息设置处开启任务运行超时告警。0表示不设置超时告警。',
  TASK_MAX_QUEUE_NUMBER:
    '最大排队数量：当需要执行的任务很多，但是机器人资源有限时，会存在需要排队的情况。一旦此任务排队中的待执行记录数量达到设置的最大值，将不再产生新的待执行记录',
  TASK_TIP_PRIORITY:
    '优先级影响任务执行的先后顺序，执行顺序依次为1->2->...>9->10。当一个bot上同时存在多个任务时，需要等待所有高优先级的任务执行完成后才会执行下一个优先级的任务',
  TASK_SCREEN_RECORD: '录屏',
  TASK_UPLOAD_COMMANDER: '上传运营平台',
  TASK_RUN_SILENTLY: '静默运行Beta',
};

export const TaskModalType = {
  GROUP: 'group',
  QUICK: 'quickCreate',
  EDIT: 'edit',
  CREATE: 'create',
};

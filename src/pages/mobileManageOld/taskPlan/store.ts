import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import TaskApi from '../services';
import { immer } from 'zustand/middleware/immer';
interface State {
  current: number;
  size: number;
  records: any[];
  total: string;

  searchForm: any;
  loading: boolean;
}
const initialState: State = {
  current: 1,
  size: 20,
  records: [],
  total: '0',
  searchForm: {
    processChannel: 'ALL',
  },
  loading: true,
};

interface Actions {
  reset: () => void;
  queryTaskList: (payload: any) => any;
  taskListReload: () => void;
  resetData: () => void;
  save: (payload: any) => any;
}

export const useTaskPlanStore = create<State & Actions>()(
  devtools(
    immer<State & Actions>((set) => ({
      ...initialState,
      taskListReload() {},
      async queryTaskList(payload: any) {
        set((state) => ({ ...state, loading: true }));
        const result = await TaskApi.getTaskManage(payload);
        set((state) => ({ ...state, ...result, loading: false }));
        return result;
      },

      reset() {
        set(initialState);
      },

      saveStepFormData() {
        set(initialState);
      },
      save(payload: any) {
        set((state) => ({ ...state, ...payload }));
      },
      resetTaskDetailData() {
        set(initialState);
      },
      resetData() {
        set(initialState);
      },

      resetPaginationData() {
        set(initialState);
      },
      resetManageData() {
        return { ...initialState };
      },
    })),
    {
      name: 'useTaskPlanStore',
    },
  ),
);

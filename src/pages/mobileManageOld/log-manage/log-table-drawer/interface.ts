import { PaginationParamsType } from '@/interface';
import { LogSourceTypeEnum } from './types';

/** 查询日志级别列表请求参数信息 */
export interface QueryLogLevelParamsType {
  requestUuid?: string;
}

/** 查询日志级别列表请求参数信息 */
export interface QueryLogLevelBackType {
  key: string;
  value: string;
}

/** 查询任务列表下存在日志的作业列表-请求参数类型 */
export interface QueryProcessListForLogType {
  // 任务列表id
  workUuid: string;
}

/** 查询任务列表下存在日志的作业列表-返回数据单个类型 */
export interface QueryProcessListForLogBackType {
  key: string;
  value: string;
  logSource?: LogSourceTypeEnum;
}

/** 查询日志请求参数信息 */
export interface QueryLogParamsType extends PaginationParamsType {
  workUuid?: string;
  workExecuteUuid?: string;
  message?: string;
  level?: string;
}

/** 日志单条返回数据 */
export interface LogBackType {
  logId?: number;
  level?: number;
  message?: string;
  executeTime?: string;
}

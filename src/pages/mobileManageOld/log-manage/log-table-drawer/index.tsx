import { useState, useEffect, useCallback } from 'react';
import { Modal, Form } from 'antd';
import { QueryProcessListForLogBackType, QueryLogLevelBackType, LogBackType, QueryLogParamsType } from './interface';
import { SzSearchForm, SzTable } from 'main/SzComponents';
import { ListBackType } from '@/interface';
import moment from 'moment';
import Highlighter from 'react-highlight-words';
import * as LogModalServiceApi from './services';
import { generateOneUuid } from '@/utils/utils';
// import { PaginationInitData } from '@/constants';

import styles from './index.less';
import { useModal } from '@ebay/nice-modal-react';

interface Props {
  visible: boolean;
  workDetail?: any;
  onClose: () => void;
}

const PaginationInitData = {
  pageSize: 10,
  pageNo: 1,
  total: 0,
};

/** 日志来源: 1 旧日志, 2 新日志 */
export enum LogSourceTypeEnum {
  OLD = 1,
  NEW = 2,
}

// 旧日志map
export const StatusMap = {
  0: '失败',
  1: '成功',
};

export enum IsSuccessEnum {
  SUCCESS = 0,
  FAILED = 1,
}

const LogTableDrawer = (props: Props) => {
  const modal = useModal();
  const { visible = false, workDetail, onClose } = props;
  // 搜索框
  const [modalSearchForm] = Form.useForm();
  /** 当前日志的来源 */
  const [curLogSource, setCurLogSource] = useState<LogSourceTypeEnum>();
  /** 日志级别列表 */
  const [logLevelList, setLogLevelList] = useState<QueryLogLevelBackType[]>([]);
  // /** 当前选择的日志级别 */
  // const [curLogLevel, setCurLogLevel] = useState<string>();
  /** 可选择的流程列表 */
  const [processSelectList, setProcessSelectList] = useState<QueryProcessListForLogBackType[]>([]);
  /** 展示的日志列表 */
  const [logListData, setLogListData] = useState<ListBackType<LogBackType>>({
    total: PaginationInitData.total,
    size: PaginationInitData.pageSize,
    current: PaginationInitData.pageNo,
    records: [],
  });
  /** 查询日志列表状态 */
  const [logListLoading, setLogListLoading] = useState<boolean>(false);
  /** 用来记录当前搜索数据是否处于变化未查询过，如果是的话默认从第1页开始查询 */
  const [isSearchValueChange, setIsSearchValueChange] = useState<boolean>(false);

  /** 通过日志级别id获取日志级别名称 */
  const getLogLevelNameByKey = useCallback(
    (key: number) => {
      const findItem = logLevelList.find(
        (item: QueryLogLevelBackType) => item?.key !== '' && Number(item?.key) === key,
      );

      if (findItem) {
        return findItem.value;
      }

      return '未知';
    },
    [logLevelList],
  );

  // 日志表格
  const columns = [
    {
      title: '时间',
      dataIndex: 'executeTime',
      className: styles['log-execute-time'],
      key: 'executeTime',
      width: '25%',
      render: (value: number) => `${moment(value).format('YYYY-MM-DD HH:mm:ss')}`,
    },
    {
      title: '日志内容',
      dataIndex: 'message',
      key: 'message',
      width: '60%',
      render: (value: string) => {
        return (
          <Highlighter
            highlightClassName={styles['highlight-text']}
            searchWords={[modalSearchForm.getFieldValue('message') || '']}
            autoEscape={true}
            textToHighlight={value}
          />
        );
      },
    },
    {
      title: '日志级别',
      dataIndex: 'level',
      key: 'level',
      width: '15%',
      render: (value: number) => {
        return getLogLevelNameByKey(value);
      },
    },
  ];

  // 查询日志列表
  const queryLogList = async (params?: QueryLogParamsType) => {
    const values = modalSearchForm.getFieldsValue();
    const newParams = params || {};

    setLogListLoading(true);

    if (isSearchValueChange) {
      newParams.pageNo = PaginationInitData.pageNo;
    }
    newParams.pageSize = newParams?.pageSize || Number(logListData.size || 0) || PaginationInitData.pageSize;

    await LogModalServiceApi.getWorkLogInfo({
      workUuid: workDetail.workUuid,
      ...values,
      ...newParams,
    })
      .then(({ bizData }: { bizData: ListBackType<LogBackType> }) => {
        if (!!bizData) {
          setLogListData(bizData);
        }
      })
      .finally(() => {
        setLogListLoading(false);
        setIsSearchValueChange(false);
      });
  };

  const resetPageNumAndQueryList = (params?: QueryLogParamsType) => {
    queryLogList({ ...params, pageNo: 1 });
  };

  // 搜索项
  const searchForm = {
    formData: [
      {
        name: 'message',
        component: {
          name: 'Search',
          allowClear: true,
          style: { width: 200 },
          placeholder: '请输入日志内容',
          onSearch(value: string) {
            resetPageNumAndQueryList({ message: value });
          },
        },
      },
      {
        name: 'level',
        component: {
          name: 'Select',
          style: { width: 180 },
          placeholder: '请选择日志等级',
          allowClear: true,
          options: {
            data: logLevelList,
            keyStr: 'key',
            valStr: 'value',
          },
          onChange(value: string) {
            resetPageNumAndQueryList({ level: value });
          },
        },
      },
      {
        name: 'workExecuteUuid',
        component: {
          name: 'Select',
          style: { width: 240 },
          placeholder: '请选择流程',
          // allowClear: true,
          disabled: processSelectList?.length > 1 ? false : true,
          options: {
            data: processSelectList,
            keyStr: 'key',
            valStr: 'value',
          },
          onChange(value: string, option: any) {
            if (option.optionData.logSource === LogSourceTypeEnum.NEW) {
              resetPageNumAndQueryList({ workExecuteUuid: value });
            }
          },
        },
      },
    ],
    form: modalSearchForm,
    initialValues: {},
    onFormChange: resetPageNumAndQueryList,
  };

  // 查询有日志的流程列表
  const queryProcessList = async () => {
    // setProcessSelectListLoading(true);
    await LogModalServiceApi.getProcessListForLog({
      workUuid: workDetail.workUuid,
    })
      .then(({ bizData }: { bizData: QueryProcessListForLogBackType[] }) => {
        if (!!bizData) {
          setProcessSelectList(bizData || []);
          if (!!bizData?.length) {
            setCurLogSource(bizData[0].logSource);
            modalSearchForm.setFieldValue('workExecuteUuid', bizData[0].key);
            if (bizData[0].logSource === LogSourceTypeEnum.NEW) {
              resetPageNumAndQueryList({ workExecuteUuid: bizData[0].key });
            }
          }
        }
      })
      .finally(() => {
        // setProcessSelectListLoading(false);
      });
  };

  // 查询有日志的流程列表
  const queryLogLevelList = async () => {
    await LogModalServiceApi.getWorkLogLevelList({
      requestUuid: generateOneUuid(),
    }).then(({ bizData }: { bizData: QueryLogLevelBackType[] }) => {
      if (!!bizData) {
        setLogLevelList([{ key: '', value: '全部级别' }, ...bizData]);
      }
    });
  };

  useEffect(() => {
    if (visible && !!workDetail?.workUuid) {
      queryProcessList();
      queryLogLevelList();
    }
  }, [visible, workDetail]);

  return (
    <Modal
      className={`${styles['worklog-modal']}`}
      title="日志查看"
      open={modal.visible}
      width={800}
      style={{ height: 600 }}
      onCancel={modal.remove}
      footer={null}
      destroyOnClose>
      <SzSearchForm {...searchForm} />
      <SzTable
        size={'small'}
        columns={columns}
        loading={logListLoading}
        tableData={logListData || {}}
        bordered={true}
        onPageChange={(currentPage: number, pageSize: number) => {
          queryLogList({ pageNo: currentPage, pageSize: pageSize });
        }}
        scroll={{ y: 500 }}
        tableLayout={'fixed'}
      />
    </Modal>
  );
};

export default LogTableDrawer;

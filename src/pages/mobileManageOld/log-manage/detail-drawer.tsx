import { FormatSecToDate } from '@/bizComponents/WorkRecord/constants';
import { TaskExcuteRecordEnum } from '@/types/taskcenter/type';
import InfoCard from '@deopCmd/components/InfoCard';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useRequest } from 'ahooks';
import { Drawer } from 'antd';
import { FC } from 'react';
import FlowDetailTable from './flow-detail-table';
import * as API from './services';
import { TaskExcuteRecordMap } from './status-select';
import TaskStepCommon from './task-step-common';

const MobileLogDetailDrawer: FC<{
  workId: string;
}> = ({ workId }) => {
  const modal = useModal();
  const { data: response } = useRequest(() => API.getLogDetail({ workId: '12' }), {});
  const data = response?.bizData;
  return (
    <Drawer
      open={modal.visible}
      onClose={modal.remove}
      title={'详情'}
      width={810}
      footer={null}
      maskClosable={true}
      destroyOnClose>
      <div className="space-y-4">
        <InfoCard
          items={[
            {
              label: '运行记录ID',
              key: 'workUuid',
              value: data?.workId,
              hide: false,
              allowCopy: true,
            },
            {
              label: '运行状态',
              key: 'status',
              // @ts-ignore
              value: TaskExcuteRecordMap[data?.status],
              hide: false,
            },
            {
              label: '失败原因',
              key: 'failDescription',
              value: data?.failReason,
              hide: data?.status !== TaskExcuteRecordEnum.RUNFAILED,
            },
            { label: '运行优先级', key: 'priority', value: data?.priority, hide: !data?.priority },
            {
              label: '运行时长',
              key: 'runTime',
              value: FormatSecToDate(Number(data?.runTime) || 0),
              hide: !data?.runTime || !Number(data?.runTime),
            },
            {
              label: '运行用户',
              key: 'accountName',
              value: data?.accountName,
            },
            {
              label: '用户姓名',
              key: 'realName',
              value: data?.realName,
              hide: !data?.realName,
            },
            {
              label: '用户姓名',
              key: 'jobName',
              value: data?.jobName,
              hide: !data?.jobName,
            },
          ]}
        />

        <div className="space-y-4">
          <p className="font-semibold">流程信息</p>
          <FlowDetailTable dataSource={data?.processDetails} />
        </div>

        <div className="space-y-4">
          <p className="font-semibold">进度信息</p>
          <TaskStepCommon recordInfo={data} />
        </div>
      </div>
    </Drawer>
  );
};

export default NiceModal.create(MobileLogDetailDrawer);

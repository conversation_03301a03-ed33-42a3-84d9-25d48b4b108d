import { NO_DATA_IN_TABLE_COLUMN_DISPLY } from '@deopCmd/utils/constants/const';
import { Space, Table } from 'antd';
import React, { FC } from 'react';

/** 流程渠道：0- 流程管理，1-流程市场 */
export enum AppChannelEnum {
  APPMANAGEMENT = 0,
  APPMARKET = 1,
}

export const AppChannelMap = {
  [AppChannelEnum.APPMANAGEMENT]: '流程管理',
  [AppChannelEnum.APPMARKET]: '流程市场',
};

const columns = [
  {
    title: '排序',
    dataIndex: 'uuid',
    width: 50,
    render: (_: any, __: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '流程命名',
    dataIndex: 'processName',
    with: '30%',
  },
  {
    title: '版本',
    dataIndex: 'processVersion',
    with: '20%',
  },
  {
    title: (
      <Space>
        <label>渠道</label>
      </Space>
    ),
    dataIndex: 'processChannel',
    render: (value: AppChannelEnum) => {
      return AppChannelMap[value] || NO_DATA_IN_TABLE_COLUMN_DISPLY;
    },
  },
];

const FlowDetailTable: FC<{ dataSource: any }> = ({ dataSource }) => {
  return <Table size={'small'} columns={columns} pagination={false} dataSource={dataSource} bordered />;
};

export default FlowDetailTable;

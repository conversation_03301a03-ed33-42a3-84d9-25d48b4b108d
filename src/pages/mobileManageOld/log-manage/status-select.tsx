import { Select, SelectProps } from 'antd';
import React, { FC } from 'react';

/** 任务运行状态：1-运行中，2-待运行，3-运行失败，4-已停止，5-运行成功，6-停止中，-2-丢失结果，8-已终止， 8-已终止废弃， -5-等待超时 */
export enum TaskExcuteRecordEnum {
  ALL = '',
  RUNNING = 1,
  RUNNABLE = 2,
  RUNFAILED = 3,
  STOPPED = 4,
  RUNSUCCESS = 5,
  STOPPING = 6,
  SHUTDOWN = 8,
  LOADING = -1,
  LOSTRESULT = -2,
  TIMEOUT = -5,
}

export const TaskExcuteRecordMap: Record<TaskExcuteRecordEnum, string> = {
  [TaskExcuteRecordEnum.ALL]: '全部',
  [TaskExcuteRecordEnum.RUNNING]: '运行中',
  [TaskExcuteRecordEnum.RUNNABLE]: '待运行',
  [TaskExcuteRecordEnum.RUNFAILED]: '运行失败',
  [TaskExcuteRecordEnum.STOPPED]: '已停止',
  [TaskExcuteRecordEnum.RUNSUCCESS]: '运行成功',
  [TaskExcuteRecordEnum.STOPPING]: '停止中',
  [TaskExcuteRecordEnum.SHUTDOWN]: '已终止',
  [TaskExcuteRecordEnum.LOADING]: '加载中',
  [TaskExcuteRecordEnum.LOSTRESULT]: '丢失结果',
  [TaskExcuteRecordEnum.TIMEOUT]: '等待超时',
};
export const TaskExcuteRecordArr = [
  { key: TaskExcuteRecordEnum.ALL, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.ALL] },
  { key: TaskExcuteRecordEnum.RUNNING, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.RUNNING] },
  { key: TaskExcuteRecordEnum.RUNNABLE, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.RUNNABLE] },
  {
    key: TaskExcuteRecordEnum.RUNFAILED,
    name: TaskExcuteRecordMap[TaskExcuteRecordEnum.RUNFAILED],
  },
  { key: TaskExcuteRecordEnum.STOPPED, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.STOPPED] },
  {
    key: TaskExcuteRecordEnum.RUNSUCCESS,
    name: TaskExcuteRecordMap[TaskExcuteRecordEnum.RUNSUCCESS],
  },
  { key: TaskExcuteRecordEnum.STOPPING, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.STOPPING] },
  // { key: TaskExcuteRecordEnum.SHUTDOWN, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.SHUTDOWN] },
  { key: TaskExcuteRecordEnum.LOADING, name: TaskExcuteRecordMap[TaskExcuteRecordEnum.LOADING] },
  {
    key: TaskExcuteRecordEnum.LOSTRESULT,
    name: TaskExcuteRecordMap[TaskExcuteRecordEnum.LOSTRESULT],
  },
  {
    key: TaskExcuteRecordEnum.TIMEOUT,
    name: TaskExcuteRecordMap[TaskExcuteRecordEnum.TIMEOUT],
  },
];

export const MobleTaskStatusColor: Record<TaskExcuteRecordEnum, string> = {
  [TaskExcuteRecordEnum.ALL]: '',
  [TaskExcuteRecordEnum.RUNNING]: 'green',
  [TaskExcuteRecordEnum.RUNNABLE]: 'gray',
  [TaskExcuteRecordEnum.RUNFAILED]: 'error',
  [TaskExcuteRecordEnum.STOPPED]: 'orange',
  [TaskExcuteRecordEnum.RUNSUCCESS]: 'gray',
  [TaskExcuteRecordEnum.STOPPING]: 'orange',
  [TaskExcuteRecordEnum.SHUTDOWN]: 'orange',
  [TaskExcuteRecordEnum.LOADING]: 'gray',
  [TaskExcuteRecordEnum.LOSTRESULT]: 'error',
  [TaskExcuteRecordEnum.TIMEOUT]: 'gray',
};

const StatusSelect: FC<SelectProps> = (props) => {
  return (
    <Select {...props}>
      {TaskExcuteRecordArr.map((item) => {
        return (
          <Select.Option value={item.key} key={item.key}>
            {item.name}
          </Select.Option>
        );
      })}
    </Select>
  );
};

export default StatusSelect;

import ComboxSelectSearch from '@/components/ui/combox-select/combox-select-search';
import NiceModal from '@ebay/nice-modal-react';
import { useAntdTable } from 'ahooks';
import { Badge, Form, FormInstance, Select, Space, TableColumnType, Tooltip, Typography } from 'antd';
import { PageContainer } from 'main/PageContainer';
import { PageListContent } from 'main/PageListContent';
import type { FC } from 'react';
import * as logAPI from './services';
import { NO_DATA_IN_TABLE_COLUMN_DISPLY } from '@deopCmd/utils/constants/const';
import { ColumnsType } from 'antd/lib/table';
import { GetProps } from 'main/types';
import StatusSelect, { MobleTaskStatusColor, TaskExcuteRecordEnum } from './status-select';
import { Table } from '@deopCmd/components/ui/Table';
import { TaskExcuteRecordMap } from '@deopCmd/types/taskcenter/type';
import detailDrawer from './detail-drawer';

type RecordType = Awaited<NonNullable<ReturnType<typeof logAPI.getLogList>>>['bizData']['records'][number];

const columns: ColumnsType<RecordType> = [
  {
    title: '作业ID',
    dataIndex: 'workId',
    ellipsis: true,
    width: '10%',
    render: (text: string) => {
      return (
        <Tooltip
          placement="topLeft"
          title={text}>{`${text.substring(0, 4)}...${text.substring(text.length - 4, text.length)}`}</Tooltip>
      );
    },
  },
  {
    title: '任务名称',
    dataIndex: 'jobName',
    width: '15%',
    render: (text: string) => {
      return text ? (
        <Tooltip title={text} placement="topLeft">
          {text}
        </Tooltip>
      ) : (
        NO_DATA_IN_TABLE_COLUMN_DISPLY
      );
    },
  },
  {
    title: '运行用户',
    dataIndex: 'accountName',
    width: '10%',

    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => <span>{text || NO_DATA_IN_TABLE_COLUMN_DISPLY}</span>,
  },
  {
    title: '用户姓名',
    dataIndex: 'realName',
    width: '10%',
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => {
      return text ? <Tooltip title={text}>{text}</Tooltip> : NO_DATA_IN_TABLE_COLUMN_DISPLY;
    },
  },
  {
    title: '运行时长',
    dataIndex: 'runTime',
    ellipsis: true,
    width: '10%',
    render: (text: any) => formatSecToDate(text),
  },
  {
    title: '状态',
    dataIndex: 'status',
    ellipsis: true,
    width: '10%',
    render: (text, record) => {
      const status = record.status as TaskExcuteRecordEnum;
      return <Badge color={MobleTaskStatusColor[status]} text={TaskExcuteRecordMap[status]} />;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreated',
    ellipsis: true,
    width: '15%',
  },
  {
    title: '操作',
    width: '20%',
    ellipsis: true,
    render: (text: any, record: RecordType) => {
      return (
        <Space>
          <Typography.Link
            onClick={() => {
              NiceModal.show(detailDrawer, { workId: 'sdfsdf' });
            }}>
            详情
          </Typography.Link>
          <Typography.Link>日志</Typography.Link>
          <Typography.Link>删除</Typography.Link>
        </Space>
      );
    },
  },
];

const MobileWorkManage = () => {
  const [form] = Form.useForm<{ realName: string; userName: string }>();
  const { tableProps, search } = useAntdTable(
    async (p, formValue) => {
      const { SHOULD_SPREAD, ...rest } = formValue;

      const res = await logAPI.getLogList({ ...SHOULD_SPREAD, ...rest });
      return {
        list: res.bizData.records,
        total: res.bizData.total,
      };
    },
    {
      defaultPageSize: 10,
      defaultCurrent: 1,
      form,
    },
  );
  return (
    <NiceModal.Provider>
      <PageContainer>
        <PageListContent>
          <div className="space-y-4">
            <SeachHeader form={form} onSubmit={search.submit} />
            <Table
              {...tableProps}
              scroll={window.innerWidth < 1300 ? { x: 1300 } : {}}
              rowKey={(r) => r.workId}
              columns={columns}
            />
          </div>
        </PageListContent>
      </PageContainer>
    </NiceModal.Provider>
  );
};

const SeachHeader: FC<{ form: FormInstance; onSubmit: () => void }> = ({ form, onSubmit }) => {
  const selectOptions: GetProps<typeof ComboxSelectSearch>['options'] = [
    {
      value: 'jobName',
      label: '任务名',
      child: { placeholder: '请输入任务名' },
    },
    {
      value: 'workId',
      label: '作业ID',
      child: { placeholder: '请输入作业ID' },
    },
    {
      value: 'accountName',
      label: '运行用户',
      child: { placeholder: '请输入运行用户' },
    },
    {
      value: 'realName',
      label: '用户姓名',
      child: { placeholder: '请输入用户姓名' },
    },
  ];
  return (
    <Form form={form} initialValues={{ SHOULD_SPREAD: ['jobName', ''] }}>
      <div className="flex gap-4">
        {/* 应该展开 */}
        <Form.Item noStyle shouldUpdate={() => true}>
          {({ setFieldValue }) => {
            return (
              <>
                <ComboxSelectSearch
                  onSearch={(val) => {
                    setFieldValue('SHOULD_SPREAD', { [val[0]]: val[1] });
                    onSubmit();
                  }}
                  defaultValue={['jobName', '']}
                  options={selectOptions}
                />
                <Form.Item noStyle name={['SHOULD_SPREAD']} />
              </>
            );
          }}
        </Form.Item>
        <Form.Item noStyle name={['status']}>
          <StatusSelect onChange={onSubmit} className="min-w-[100px]" />
        </Form.Item>
      </div>
    </Form>
  );
};

const formatSecToDate = (sec: number) => {
  if (!sec) {
    return NO_DATA_IN_TABLE_COLUMN_DISPLY;
  }
  const paddingZero = (val: number) => {
    if (val < 10) {
      return `0${val}`;
    }
    return val;
  };

  const min = Math.floor(sec % 3600);
  return `${paddingZero(Math.floor(sec / 3600))}:${paddingZero(Math.floor(min / 60))}:${paddingZero(sec % 60)}`;
};

export default MobileWorkManage;

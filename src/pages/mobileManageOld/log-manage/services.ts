import { IList, IPageParams, IRes } from 'main/types';

export const getLogList = async (
  params: IPageParams<{ userName: string; status: string }>,
): Promise<
  IRes<
    IList<{
      workId: string;
      jobName: string;
      botId: any;
      accountName: any;
      runTime: any;
      status: number;
      statusDesc: string;
      failReasonCode: any;
      failReason: string;
      gmtCreated: string;
      hasLog: boolean;
      hasRecord: any;
      hasReference: any;
      realName: string;
    }>
  >
> => {
  console.log('🚀 -> params:', params);
  const mockData: IList<{
    workId: string;
    jobName: string;
    botId: any;
    accountName: any;
    runTime: any;
    status: number;
    statusDesc: string;
    failReasonCode: any;
    failReason: string;
    gmtCreated: string;
    hasLog: boolean;
    hasRecord: any;
    hasReference: any;
    realName: string;
  }> = {
    total: 3,
    pages: 1,
    size: 20,
    current: 1,
    records: [
      {
        workId: '1854041350586077185',
        jobName: '1212',
        botId: null,
        accountName: null,
        runTime: null,
        status: 2,
        statusDesc: '待运行',
        failReasonCode: null,
        failReason: '',
        gmtCreated: '2024-11-06 14:01:19',
        hasLog: false,
        hasRecord: null,
        hasReference: null,
        realName: '',
      },
      {
        workId: '1850827876154314753',
        jobName: 'test',
        botId: null,
        accountName: null,
        runTime: null,
        status: 2,
        statusDesc: '待运行',
        failReasonCode: null,
        failReason: '',
        gmtCreated: '2024-10-28 17:12:07',
        hasLog: false,
        hasRecord: null,
        hasReference: null,
        realName: '',
      },
      {
        workId: '1850827567784890370',
        jobName: '121212',
        botId: null,
        accountName: null,
        runTime: null,
        status: 2,
        statusDesc: '待运行',
        failReasonCode: null,
        failReason: '',
        gmtCreated: '2024-10-28 17:10:54',
        hasLog: false,
        hasRecord: null,
        hasReference: null,
        realName: '',
      },
      {
        workId: '1850826379014279170',
        jobName: 'wefwef',
        botId: null,
        accountName: null,
        runTime: null,
        status: 2,
        statusDesc: '待运行',
        failReasonCode: null,
        failReason: '',
        gmtCreated: '2024-10-28 17:06:10',
        hasLog: false,
        hasRecord: null,
        hasReference: null,
        realName: '',
      },
      {
        workId: '1802992660679933954',
        jobName: 'zyj-test',
        botId: null,
        accountName: null,
        runTime: null,
        status: 2,
        statusDesc: '待运行',
        failReasonCode: null,
        failReason: '',
        gmtCreated: '2024-06-18 17:12:03',
        hasLog: false,
        hasRecord: null,
        hasReference: null,
        realName: '',
      },
    ],
  };
  return {
    bizData: mockData,
    code: '0',
    msg: 'success',
    subCode: '0',
    subMsg: 'success',
  };
};

export const getLogDetail = async (params: {
  workId: string;
}): Promise<
  IRes<{
    workId: string;
    status: number;
    statusDesc: string;
    priority: number;
    jobName: string;
    accountName: any;
    processDetails: {
      id: string;
      processOrder: number;
      processChannel: number;
      processId: string;
      processDetailId: string;
      processName: string;
      processVersion: string;
      processHumanMinute: number;
      processHumanHourCost: any;
      processSource: number;
      isReference: boolean;
      inputParam: any;
      template: any;
    }[];
    workExecuteDetails: {
      workExecuteId: string;
      status: number;
      processName: string;
      processVersion: string;
      processChannel: number;
      machineName: any;
      machineIp: any;
      createTime: string;
      startTime: any;
      endTime: any;
      statusDesc: string;
      runTime: string;
      failReasonCode: any;
      failReason: string;
    }[];
    failReasonCode: any;
    failReason: any;
    createTime: string;
    startTime: any;
    endTime: any;
    runTime: any;
    isGroup: boolean;
    realName: string;
  }>
> => {
  return {
    bizData: {
      workId: '1876939206029815809',
      status: 3,
      statusDesc: '运行失败',
      priority: 5,
      jobName: '绯世任务 006',
      accountName: 'bycyyds',
      processDetails: [
        {
          id: '1876939206029815810',
          processOrder: 1,
          processChannel: 0,
          processId: '1807666243197599745',
          processDetailId: '1808772107771125762',
          processName: '元素点击',
          processVersion: '1.0.30',
          processHumanMinute: 60,
          processHumanHourCost: null,
          processSource: 0,
          isReference: false,
          inputParam: null,
          template: null,
        },
      ],
      workExecuteDetails: [
        {
          workExecuteId: '1876939206029815810',
          status: 3,
          processName: '元素点击',
          processVersion: '1.0.30',
          processChannel: 0,
          machineName: 'MAR-TL00',
          machineIp: '***********',
          createTime: '2025-01-08 18:29:13',
          startTime: '2025-01-08 18:30:01',
          endTime: '2025-01-08 18:30:01',
          statusDesc: '运行失败',
          runTime: '0',
          failReasonCode: '6000000001',
          failReason: '流程执行异常',
        },
      ],
      failReasonCode: '6000000001',
      failReason: '流程执行异常',
      createTime: '2025-01-08 18:29:13',
      startTime: '2025-01-08 18:30:01',
      endTime: '2025-01-08 18:30:01',
      runTime: '0',
      isGroup: false,
      realName: 'bycyyds',
    },
    code: '0',
    msg: 'success',
    subCode: '0',
    subMsg: 'success',
  };
};

/**
 * 任务管理-PC任务 接口文档
 * https://yapi.ii-ai.tech/project/368/interface/api/cat_4771
 */

import request from '@/utils/request';
import { PaginationInitData } from '@/constants';
import type {
  JobActionParamsTest,
  JobActionParams,
  CheckJobCreateFirstParams,
  CheckJobCreateSecondParams,
  QueryTaskListParamsType,
  JobCreateParams,
  UpdateInputParamType,
  QueryWorkListType,
  WorkSingleTypeParamsType,
  WorkLogParamsType,
  QueryWorkRecordDetailParamsType,
  statisticParamsType,
  WorkRecordListParamsType,
  DeleteWorkRecordParamsType,
} from '@/types/taskcenter/interface';

// const CancelToken = Request.CancelToken;
// const SZCOMMANDER: any = ((window as any)['SZ_COMMANDER'] = {});

/** 【manager】任务列表 */
export async function getTaskManage(params: QueryTaskListParamsType) {
  return request('/opt/oldMobile/1.0.0/web/job/page', {
    method: 'POST',
    data: params,
  });
}

/** 【manager】查询当前租户的用户列表 */
export async function getTaskManageUserSelectList() {
  return request('/uc/user/1.0.0/user-pull-down', {
    method: 'POST',
    data: {},
  });
}

/** 【manager】任务执行 */
export async function getTaskManageRun(jobUuid: string) {
  return request(`/opt/oldMobile/1.0.0/web/jobExecute`, {
    method: 'POST',
    data: {
      jobUuid,
    },
  });
}

/** 【manager】创建任务校验第一步 */
export async function getCheckJobCreateFirst(params: any) {
  return request(`/opt/oldMobile/1.0.0/web/job/validate/create`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】创建任务校验第二步, 设置每次执行次数 */
export async function getCheckJobCreateSecond(params: CheckJobCreateSecondParams) {
  return request(`/opt/oldMobile/1.0.0/web/job/validate/create/second`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】创建任务 */
export async function getTaskmanageSetup(params: JobCreateParams) {
  return request(`/opt/oldMobile/1.0.0/web/job/add`, {
    method: 'POST',
    data: params,
  });
}

// 任务管理-停止任务
export async function getTaskmanageStop(params: JobActionParams) {
  return request(`/opt/oldMobile/1.0.0/web/job/stop`, {
    method: 'POST',
    data: params,
  });
}

// 任务管理-删除任务
export async function getTaskmanageDelete(params: JobActionParamsTest) {
  return request(`/opt/oldMobile/1.0.0/web/job/delete`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】任务详情 */
export async function getTaskmanageDetail(jobUuid: string) {
  // TODO
  return request(`/opt/oldMobile/1.0.0/web/jobInfo`, {
    method: 'POST',
    data: { jobUuid },
  });
}

/** 【manager】查询任务与流程引用参数信息 */
export async function queryReferenceParamterByTaskAndProcessUuid(jobProcessUuid: string) {
  return request(`/opt/oldMobile/1.0.0/web/job/select-job-process-by-uuid`, {
    method: 'POST',
    data: { jobProcessUuid },
  });
}

/** 【manager】获取编辑任务详情 */
export async function getEditTaskmanageDetail(jobUuid: string) {
  return request(`/opt/oldMobile/1.0.0/web/job/update/info/${jobUuid}`, {
    method: 'POST',
  });
}

export async function getManageWorkVideo(workUuid: any) {
  return request(`/opt/oldMobile/1.0.0/web/work/downLoad/${workUuid}`, {
    method: 'POST',
    responseType: 'blob',
  });
}

export async function getManageWorkLog(workUuid: any) {
  return request(`/opt/oldMobile/1.0.0/web/work/log/${workUuid}`, {
    method: 'POST',
  });
}

/**  任务日志级别下拉 */
export async function getLogLevelList() {
  return request(`/opt/oldMobile/1.0.0/web/work/engine/levelSelect`, {
    method: 'POST',
  });
}

/**
 *
 * @since 2.2.0 departmentId
 * @param departmentId 部门
 *
 */
export async function getBotList(params: Record<string, any> & { departmentId?: string }) {
  return request(`/opt/deop/1.0.0/web/bot/assign`, {
    method: 'POST',
    data: params,
  });
}

export async function getCron(params: any) {
  return request(`/opt/deop/1.0.0/web/text/parse/data/cron/parse`, {
    method: 'POST',
    data: params,
  });
}

// 强制停止手动任务
export async function getForcedStop(params: JobActionParams) {
  return request(`/job/manual/stop`, {
    method: 'post',
    data: params,
  });
}

export async function deleteTaskList(workUuid: any) {
  return request(`/opt/oldMobile/1.0.0/web/work/delete/waitWork`, {
    method: 'POST',
    data: {
      workUuid,
    },
  });
}

/** 【manager】查询优先级列表 1~10 数值越小优先级越高，数值越大优先级越低 */
export async function getSelectPriorityList() {
  return request(`/opt/deop/1.0.0/web/job/select-priority-list`, {
    method: 'POST',
    data: {},
  });
}

/** 【manager】任务入参，获取密码类型加密key */
export async function getSelectJobEncryptKey() {
  return request(`/opt/oldMobile/1.0.0/web/job/select-job-encrypt-key`, {
    method: 'POST',
  });
}
/** 详情页【manager】更新job引用参数 */
export async function updateInputParam(params: UpdateInputParamType) {
  return request(`/opt/oldMobile/1.0.0/web/job/update-input-param`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】查询系统设置信息
 *
 * 包括：等待超时时间、最大排队数量
 */
export const getTaskConfig = () => {
  return request('/opt/oldMobile/1.0.0/web/tenant/setting/info', {
    method: 'post',
    data: {},
  });
};

// 任务管理-停止定时任务
export async function stopSchedule(params: any) {
  return request(`/opt/oldMobile/1.0.0/web/job/schedule/stop`, {
    method: 'POST',
    data: params,
  });
}

// 任务管理-开启定时任务
export async function startSchedule(params: any) {
  return request(`/opt/oldMobile/1.0.0/web/job/schedule/start`, {
    method: 'POST',
    data: params,
  });
}
// 任务管理修改保存
export async function saveEditTask(params: any) {
  return request(`/opt/oldMobile/1.0.0/web/job/update`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】作业列表 */
export async function getWorkList(params: QueryWorkListType) {
  return request(`/opt/oldMobile/1.0.0/web/work/list`, {
    method: 'POST',
    data: params,
    // cancelToken: new CancelToken((cancel: any) => {
    //   console.log(SZCOMMANDER['taskWorkList'])
    //   SZCOMMANDER['taskWorkList'] = cancel;
    // }),
  });
}

/** 任务列表-右侧action接口 */
/** 【manager】作业录像下载 */
export async function getWorkVideo(params: WorkSingleTypeParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/work/downLoad`, {
    method: 'POST',
    params,
    responseType: 'blob',
  });
}

/** 【manager】作业录屏下拉(录屏) */
export async function getWorkVideoSelect(params: QueryWorkRecordDetailParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/work/execute/select/screen-record`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】作业日志 */
export async function getWorkLog(params: WorkLogParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/work/engine/log`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】作业执行下拉(日志) */
export async function getWorkLogSelect(params: QueryWorkRecordDetailParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/work/execute/select/log`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】作业引用参数 */
export async function getWorkInputParam(params: WorkSingleTypeParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/work/inputParam`, {
    method: 'POST',
    data: params,
  });
}

/** 【manager】作业执行下拉(引用参数) */
export async function getWorkInputParamSelect(params: QueryWorkRecordDetailParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/work/execute/select/inputParam`, {
    method: 'POST',
    data: params,
  });
}

/** 任务管理-运行统计 */
// 概况分析
export async function getGeneralStatisticData(params: statisticParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/job/execute/statistics/survey-analysis`, {
    method: 'post',
    data: params,
  });
}

// 使用趋势
export async function getStatisticsUseTrendData(params: statisticParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/job/execute/statistics/trend`, {
    method: 'post',
    data: params,
  });
}

// 失败原因
export async function getStatisticFailedResonData(params: statisticParamsType) {
  return request(`/opt/oldMobile/1.0.0/web/job/execute/statistics/fail-chart`, {
    method: 'post',
    data: params,
  });
}

/** ************************* 计划详情-任务列表 ************************* */
// 查询任务列表列表
export async function getWorkRecordListForTask(params: WorkRecordListParamsType) {
  return await request('/opt/oldMobile/1.0.0/web/work/within-execute', {
    method: 'POST',
    data: {
      pageSize: PaginationInitData.pageSize,
      pageNo: PaginationInitData.pageNo,
      ...params,
    },
  });
}

// 删除待任务列表RUNNABLE
export async function deleteRunnableWorkRecord(params: DeleteWorkRecordParamsType) {
  return await request('/opt/oldMobile/1.0.0/web/work/delete-wait', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

interface DepartmentInfo {
  structId: string;
  uniqCode: string;
  name: string;
  parentIdWithRoot: string;
  parentIdWithoutRoot: string;
  users: any[];
  children: DepartmentInfo[] | [];
}

export type GetDepartmentTreeRes = DepartmentInfo[];

/**
 * 获取有权限的部门列表
 */
export const getDepartmentTree = async () => {
  return request<GetDepartmentTreeRes>('/opt/oldMobile/1.0.0/web/organization/structure/data/format/list', {
    method: 'POST',
    data: {},
  });
};

/**
 * 启用
 */
export const enable = async (params: any) => {
  return request('/opt/oldMobile/1.0.0/web/job/enable', {
    method: 'POST',
    data: { ...params },
  });
};

/**
 * 禁用
 */
export const disable = async (params: any) => {
  return request('/opt/oldMobile/1.0.0/web/job/disable', {
    method: 'POST',
    data: { ...params },
  });
};
// mock模拟停止任务
export async function fakeTaskmanageStop(params: JobActionParams) {
  return request(`/api/taskmanageStop`, {
    method: 'POST',
    data: params,
  });
}

export default {
  getTaskManage,
  getTaskManageRun,
  getTaskmanageSetup,
  getTaskmanageStop,
  getTaskmanageDelete,
  getTaskmanageDetail,
  getWorkRecordListForTask,
  deleteRunnableWorkRecord,
  queryReferenceParamterByTaskAndProcessUuid,
  getEditTaskmanageDetail,
  getTaskManageUserSelectList,
  getManageWorkVideo,
  getManageWorkLog,
  getLogLevelList,
  getBotList,
  getCheckJobCreateFirst,
  getCron,
  getForcedStop,
  deleteTaskList,
  getSelectPriorityList,
  getSelectJobEncryptKey,
  updateInputParam,
  getTaskConfig,
  stopSchedule,
  startSchedule,
  saveEditTask,
  getWorkList,
  getWorkVideo,
  getWorkVideoSelect,
  getWorkLog,
  getWorkLogSelect,
  getWorkInputParam,
  getWorkInputParamSelect,
  getGeneralStatisticData,
  getStatisticsUseTrendData,
  getStatisticFailedResonData,
  getDepartmentTree,
  enable,
  disable,
  fakeTaskmanageStop,
};

import request from '@/utils/request';
// import type { AppChannelEnum } from '@/types/application/type';

/**
 * 获取流程列表及流程共享的流程及版本列表，可按processChannel来独立查询
 * 接口地址：https://yapi.ii-ai.tech/project/368/interface/api/19143
 */

/**
  ** 渠道
  processChannels?: AppChannelEnum[];
   上架状态：不传则表示全部状态，true-已上架，false-已下架
  isPublish: boolean;
 *
 */
export async function getAppAndAppVersionList(params?: any) {
  return request('/opt/oldMobile/1.0.0/web/process/aggregation/pull-down', {
    method: 'POST',
    data: params || {},
  });
}

/** 流程编排列表*/

export async function getFlowArrangeList() {
  return request('/opt/oldMobile/1.0.0/web/flow-arrange/pull-down', {
    method: 'POST',
    data: {},
  });
}

export default {
  getAppAndAppVersionList,
  getFlowArrangeList,
};

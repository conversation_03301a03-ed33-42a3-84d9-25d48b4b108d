import { Row, Col, Typography } from 'antd';
import { colBigSpan } from '../../chartComponent/const';
import StatusRunningCard from './StatusRunningCard';
import {
  BotStatusAndTitleColorMap,
  BotStatusDesMap,
  commonParams,
  setIntervalTime,
  TitleAndTipsTypeEnum,
} from '../constants';
import { useRequest } from 'ahooks';
import * as API from '../services';
import DataSummaryTbColumnsConfig from '@/pages/monitoringCenter/dataCockpit/components/tbCloumns';
import React, { useState } from 'react';
import ListCard from './ListCard';
import NiceModal from '@ebay/nice-modal-react';
import DetailDrawer from './DetailDrawer/detail-drawer';
import DetailConfig from './DetailDrawer/detailConfig';
import { WorkErrorLastedErrorBackType } from '@/types/monitoringcenter/interface';
import { formatDate } from '@/utils/utils';
// @ts-ignore
import { SzTooltip, GetPopupContainerLayerType } from 'main/SzComponents';

interface IProps {
  pageTitlesAndTipsMap: { [key: string]: { title: string; tip: string } }
  handleHistoryPush: (url: string, extend?: string) => void
}

const StatusAndBreakdown = (props: IProps) => {
  const { pageTitlesAndTipsMap, handleHistoryPush } = props;
  const [requestCount, setRequestCount] = useState<number>(0);

  const {
    data: botStatusStatisticsData,
    loading: botStatusStatisticsDataLoading,
  } = useRequest(() => API.getBotStatusStatistics({...commonParams}), {
    pollingInterval: setIntervalTime,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 3,
    onBefore: () => setRequestCount(count => count + 1)
  });

  const {
    data: botStatusLastedErrorList,
    loading: botStatusLastedErrorListLoading,
  } = useRequest(() => API.getBotStatusLastedErrorList({...commonParams}), {
    pollingInterval: setIntervalTime,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 3
  });

  const {
    data: workErrorLastedErrorList,
    loading: workErrorLastedErrorListLoading,
  } = useRequest(() => API.getWorkErrorLastedErrorList({...commonParams}), {
    pollingInterval: setIntervalTime,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 3
  });

  const workErrorLastedErrorColumn = [
      {
        title: '故障任务',
        dataIndex: 'jobName',
        ellipsis: true,
        render: (text: string, record: WorkErrorLastedErrorBackType) => {
          return (
            <Typography.Link
              onClick={() => handleHistoryPush('/taskList', `&workUuid=${record.workUuid}`)}
            >
              <SzTooltip
                title={text}
                placement="topLeft"
                popupContainerLayer={GetPopupContainerLayerType.FOURPARENT}
              >
                {text}
              </SzTooltip>
            </Typography.Link>
          );
        },
      },
      {
        title: '运行用户',
        dataIndex: 'userName',
        ellipsis: true,
        render: (text: string) => {
          return (
            <SzTooltip
              title={text}
              placement="topLeft"
              popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}
            >
              {text}
            </SzTooltip>
          );
        },
      },
      {
        title: '故障原因',
        dataIndex: 'failDescription',
        ellipsis: true,
        width: '40%',
        render: (text: string) => {
          return text ? (
            <SzTooltip
              title={text}
              placement="topLeft"
              popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}
            >
              {text}
            </SzTooltip>
          ) : (
            '未知错误'
          );
        },
      },
      {
        title: '故障时间',
        dataIndex: 'endTime',
        ellipsis: true,
        render: (text: string) => {
          return (
            <SzTooltip
              title={text}
              placement="topLeft"
              popupContainerLayer={GetPopupContainerLayerType.THREEPARENT}
            >
              {formatDate(text)}
            </SzTooltip>
          );
        },
      },
    ]

  return (
    <Row gutter={[12, 12]} style={{ marginBottom: 12 }}>
      <Col {...colBigSpan}>
        <StatusRunningCard
          key={'botStatusList'}
          cardHeaderData={{
            ...pageTitlesAndTipsMap?.[TitleAndTipsTypeEnum.BOTSTATUS],
          }}
          statusCountTableData={{
            ...botStatusStatisticsData,
            statusMap: BotStatusDesMap,
            statusColorMap: BotStatusAndTitleColorMap,
            loading: requestCount === 0 && botStatusStatisticsDataLoading,
          }}
          subCardHeaderData={{
            isDivider: false,
            title: '最近异常',
            titleStyles: { fontSize: '13px' },
            isShowDetail: true,
            onShowDetail: () => {
              NiceModal.show(DetailDrawer, {
                headerTitle: '机器人异常',
                ...DetailConfig.BotStatusLastedErrorList,
              })
            },
          }}
          dataSource={botStatusLastedErrorList}
          columns={DataSummaryTbColumnsConfig.botErrorColumn}
          tbLoading={requestCount === 0 && botStatusLastedErrorListLoading}
          rowKey={'idx'}
        />
      </Col>
      <Col {...colBigSpan}>
        <ListCard
          cardHeaderData={{
            title: pageTitlesAndTipsMap?.[TitleAndTipsTypeEnum.RECENTFAILED]?.title,
            tip: pageTitlesAndTipsMap?.[TitleAndTipsTypeEnum.RECENTFAILED]?.tip,
            isShowDetail: true,
            onShowDetail: () => {
              handleHistoryPush('/taskList', `&status=3`);
            },
          }}
          dataSource={workErrorLastedErrorList}
          columns={workErrorLastedErrorColumn}
          tbLoading={ requestCount === 0 && workErrorLastedErrorListLoading }
          rowKey="idx"
        />
      </Col>
    </Row>
  )
}

export default StatusAndBreakdown;
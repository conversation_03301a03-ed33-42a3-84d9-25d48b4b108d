import React, { useEffect } from 'react';
import { Form, Radio, Select, Tag, Row, Col, InputNumber, Input } from 'antd';
import useAddAndEditDrawerStore from '../../store';
import { DistributionModeEnum, TaskPlanTypeEnum } from '@/types/taskcenter/type';
import { useRequest } from 'ahooks';
import { getAutoBotNum, getBotList } from '../../services';
import {
  BotStatusEnum,
  BotStatusMap,
  BotStatusColorMap,
  BotStatusBgColorMap,
  ExecuteTypeEnum,
  ScheduleTypeEnum,
  TimingTimeTypeEnum,
} from '../../type';
import isNil from 'lodash/isNil';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import { ModalTiming } from './components/modalTiming';
import { FileTrigger } from './components/fileTrigger';
import { EmailTrigger } from './components/emailTrigger';
import * as SystemAPI from '@deopCmd/pages/systemSetting/services';

const { Option } = Select;
interface Props {
  form: any;
}

// 触发方式设置初始值
const triggerTypeinitialValues = {
  scheduleTriggerParam: {
    scheduleType: ScheduleTypeEnum.TIMING,
    cronExpression: '',
    timingTimeType: TimingTimeTypeEnum.LONGTERM,
  },
  emailTriggerParam: {
    emailTriggerConfigId: undefined,
    recipientInclude: undefined,
    senderInclude: undefined,
    subjectInclude: undefined,
    bodyInclude: undefined,
    dateRange: null,
  },
  fileManagerTriggerParam: {
    includeLeaf: true,
    monitorType: 1,
    eventTypes: ['createEvent', 'deleteEvent'],
  },
};

// 高级设置初始值
const advancedConfiginitialValues = {
  executeTimes: 1,
  maxQueueNumber: 10000,
  priority: 5,
  timeout: 3000,
  executeTimeout: undefined,
  retryTimes: undefined,
};

const CarryOutSetForm: React.FC<Props> = (props: Props) => {
  const { form } = props;
  const { visible, setVisible, id, setId, taskPlanType, setTaskPlanType, resetAddAndEditDrawerState, mode } =
    useAddAndEditDrawerStore();
  const distributionType = Form.useWatch('distributionType', form);
  const bots = Form.useWatch('bots', form);
  const executeType = Form.useWatch('executeType', form);
  const isEdit = !!id;

  // 获取动态分配机器概况
  const { data: botTypeInfoNum } = useRequest(
    async () => {
      const { bizData } = await getAutoBotNum({ runSystem: mode });
      return bizData;
    },
    {
      refreshDeps: [distributionType, mode],
      ready: Boolean(distributionType),
    },
  );

  // 从系统设置获取最大队列数和超时等待时间
  useRequest(SystemAPI.getDetail, {
    onSuccess: (data) => {
      form.setFieldsValue({
        maxQueueNumber: data.cmdTenantSettingInfo.maxQueueNumber,
        timeout: data.cmdTenantSettingInfo.timeoutWaitTime,
      });
    },
  });

  // 获取机器人下拉列表
  const { data: botLists } = useRequest(
    async () => {
      const { bizData } = await getBotList({ runSystems: [mode] });
      const order = [1, 2, -1, 4, 3];
      bizData.sort((a: any, b: any) => {
        const indexA = order.indexOf(a.status);
        const indexB = order.indexOf(b.status);

        return indexA - indexB;
      });

      return bizData;
    },
    {
      refreshDeps: [distributionType, mode],
      ready: Boolean(distributionType) && Boolean(mode),
    },
  );

  const tagRender = (props: CustomTagProps) => {
    const { value, closable, onClose } = props;
    // debugger;
    const selectTag = botLists?.find((item: any) => item.uuid === value);
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };
    return (
      <Tag onMouseDown={onPreventMouseDown} closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
        {selectTag?.userName}
      </Tag>
    );
  };

  useEffect(() => {
    form.setFieldsValue({
      accounts: botLists
        ?.filter((item: any) => bots?.includes(item.uuid))
        ?.map((item: any) => {
          const { userId, uuid, userName, realName } = item;
          return {
            accountId: userId,
            accountUuid: uuid,
            accountName: userName,
            realName,
          };
        }),
    });
  }, [bots]);

  return (
    <div>
      <Form
        name="carryOutSetform"
        form={form}
        layout="vertical"
        initialValues={{
          distributionType: 1,
          executeType: ExecuteTypeEnum.IMMEDIATE,
          ...triggerTypeinitialValues,
          ...advancedConfiginitialValues,
        }}>
        {taskPlanType !== TaskPlanTypeEnum.ARRANGE && (
          <Form.Item
            label={
              <div className="flex items-center">
                <div style={{ marginRight: 20 }}>分配方式</div>
                <div className="flex items-center">
                  <div>
                    {distributionType === 1 ? '可分配机器人：' : '已选择机器人：'}
                    <span style={{ marginRight: 20, color: '#3B3D3D' }}>
                      {distributionType === 1 ? botTypeInfoNum?.assignable || 0 : bots?.length || 0}
                    </span>
                  </div>
                  <div>
                    空闲机器人：
                    <span style={{ color: '#3B3D3D' }}>
                      {distributionType === 1
                        ? botTypeInfoNum?.idle || 0
                        : botLists
                            ?.filter((item: any) => bots?.includes(item.uuid))
                            ?.filter((item: any) => item.status === BotStatusEnum.Free).length || 0}
                    </span>
                  </div>
                </div>
              </div>
            }
            name={'distributionType'}
            rules={[{ required: true }]}>
            <Radio.Group
              onChange={() => {
                // form.resetFields(['handlers']);
                form.setFieldValue('bots', undefined);
              }}
              options={[
                { label: '动态分配', value: 1, disabled: executeType === ExecuteTypeEnum.MANUAL },
                { label: '指定机器人', value: 2 },
              ]}
            />
          </Form.Item>
        )}
        <Form.Item noStyle shouldUpdate={() => true}>
          {/* @ts-ignore */}
          {() => {
            if (isNil(distributionType)) return null;
            switch (distributionType) {
              case 1:
                return null;
              case 2: {
                return (
                  <>
                    <Form.Item name={'bots'} rules={[{ required: true, message: '请选择分配机器人' }]}>
                      <Select
                        showSearch
                        filterOption={(input: string, option) => {
                          return String(option?.key?.split('-')?.[1] ?? '')
                            ?.toLowerCase()
                            .includes(input.toLowerCase());
                        }}
                        placeholder="选择分配机器人"
                        mode="multiple"
                        tagRender={tagRender}
                        maxTagCount="responsive">
                        {botLists?.map((item: any) => (
                          <Option value={item.uuid} key={`${item.uuid}-${item.userName}`}>
                            <div className="flex justify-between items-center">
                              <div className="flex-1 truncate">{item.userName}</div>
                              <div
                                className="mr-[16px] px-[4px] py-[2px] rounded-[4px]"
                                style={{
                                  background: BotStatusBgColorMap[item.status],
                                  color: BotStatusColorMap[item.status],
                                }}>
                                {BotStatusMap[item.status]}
                              </div>
                            </div>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item label="机器人详情" name={'accounts'} style={{ display: 'none' }}>
                      <Input type="hidden" />
                    </Form.Item>
                  </>
                );
              }
            }
          }}
        </Form.Item>
        <Form.Item
          label={'触发方式'}
          name={'executeType'}
          rules={[{ required: true }]}
          tooltip="立即触发：任务创建后会立即下发给机器人执行。定时触发：任务创建后会按设置的定时规则下发给机器人执行。手动触发：任务创建后需要在机器人手动点击运行按钮运行任务。邮件触发：任务创建后会监听配置好的邮箱，满足邮件触发规则时会下发给机器人执行。文件触发：任务创建后会监听到文件管理中指定文件的事件后下发给机器人执行。">
          <Radio.Group
            disabled={isEdit}
            onChange={(e) => {
              form.setFieldsValue({
                ...triggerTypeinitialValues,
                ...advancedConfiginitialValues,
              });
              const isManual = e.target.value === ExecuteTypeEnum.MANUAL;
              if (isManual) {
                form.setFieldsValue({
                  distributionType: DistributionModeEnum.ASSASSIGN,
                });
              }
            }}
            options={[
              { label: '立即触发', value: ExecuteTypeEnum.IMMEDIATE },
              { label: '定时触发', value: ExecuteTypeEnum.TIMING },
              {
                label: '手动触发',
                value: ExecuteTypeEnum.MANUAL,
                disabled: taskPlanType === TaskPlanTypeEnum.ARRANGE,
              },
              { label: '邮件触发', value: ExecuteTypeEnum.EMAIL },
              { label: '文件触发', value: ExecuteTypeEnum.FILE },
            ]}
          />
        </Form.Item>
        <Form.Item noStyle shouldUpdate={() => true}>
          {/* @ts-ignore */}
          {() => {
            if (isNil(executeType)) return null;
            switch (executeType) {
              case ExecuteTypeEnum.IMMEDIATE:
                return null;
              case ExecuteTypeEnum.MANUAL:
                return null;
              case ExecuteTypeEnum.TIMING: {
                return (
                  <Form.Item name="scheduleTriggerParam" noStyle>
                    <ModalTiming />
                  </Form.Item>
                );
              }
              case ExecuteTypeEnum.FILE: {
                return (
                  <Form.Item name="fileManagerTriggerParam" noStyle>
                    <FileTrigger />
                  </Form.Item>
                );
              }
              case ExecuteTypeEnum.EMAIL: {
                return (
                  <Form.Item name="emailTriggerParam" noStyle>
                    <EmailTrigger />
                  </Form.Item>
                );
              }
            }
          }}
        </Form.Item>
        <Row gutter={20}>
          <Col span={12}>
            <Form.Item
              label={'单次触发次数'}
              name={'executeTimes'}
              rules={[{ required: true }]}
              tooltip="每次触发任务时将按设置条数触发多遍。例：设置为每日10点触发，每次触发次数为3，则每天上午10点将运行该任务3遍；设置立即触发，每次触发次数为3，则立即触发该任务3遍。">
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                precision={0}
                disabled={executeType === ExecuteTypeEnum.MANUAL}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={'运行超时时间(分钟)'}
              name={'executeTimeout'}
              // rules={[{ required: taskPlanType !== TaskPlanTypeEnum.ARRANGE }]}
              tooltip="流程最长运行时间，超过设定时间将结束运行，为空表示不设置运行超时。">
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                precision={0}
                disabled={true}
                placeholder={taskPlanType === TaskPlanTypeEnum.ARRANGE ? '' : '为空表示不设置运行超时'}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={20}>
          <Col span={12}>
            <Form.Item
              label={'任务重试'}
              name={'retryTimes'}
              // rules={[{ required: taskPlanType !== TaskPlanTypeEnum.ARRANGE }]}
              tooltip="任务触发失败时立即在当前机器人重新触发，为空表示失败时不重试。">
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                precision={0}
                disabled={true}
                placeholder={taskPlanType === TaskPlanTypeEnum.ARRANGE ? '' : '为空表示不设置任务重试'}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={'等待超时清除（分钟）'}
              name={'timeout'}
              rules={[{ required: true }]}
              tooltip="当需要触发的任务很多，但是机器人资源有限时，会存在需要排队的情况。开启等待超时销毁后则排队等待超过设定的时间，则该次触发等待超时后会自行清除，继续触发后面排队的任务，避免大量任务长时间等待造成的任务堵塞。">
              <InputNumber style={{ width: '100%' }} min={1} precision={0} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={20}>
          <Col span={12}>
            <Form.Item
              label={'最大排队数(条)'}
              name={'maxQueueNumber'}
              rules={[{ required: true }]}
              tooltip="当需要触发的任务很多，但是机器人资源有限时，会存在需要排队的情况。一旦此任务排队中的待触发记录数量达到设置的最大值，将不再产生新的待触发记录。">
              <InputNumber style={{ width: '100%' }} min={1} precision={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={'优先级'}
              name={'priority'}
              rules={[{ required: true }]}
              tooltip="选择数字越小优先级越高，分配机器人时将优先分配。">
              <InputNumber style={{ width: '100%' }} min={1} max={10} step={1} precision={0} disabled={true} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default CarryOutSetForm;

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { useRequest } from 'ahooks';
import { Button, Card, Drawer, message, Space, Upload } from 'antd';
// @ts-ignore
import { IconFont } from 'main/MyIcon';
import React from 'react';
import * as CompetitorApi from '../services';
import DragUpload from '@deopCmd/bizComponents/DragUpload';

interface Props {
  /** 友商ID */
  firmId: string;
  onCancel: () => void;
  onOk?: (processName: string) => void;
}

const UploadDrawer: React.FC<Props> = (props) => {
  const { onCancel, onOk, firmId } = props;
  const modal = useModal();

  const {
    data: packageInfo,
    runAsync: runUploadProcessPackage
  } = useRequest(CompetitorApi.uploadProcessPackage, {
    manual: true,
  });
  const handleOk = async () => {
    if (!packageInfo) return;

    try {
      await CompetitorApi.updateProcessPackage({
        departmentIds: packageInfo.deptIds,
        // 现在只有uipath
        firmId,
        packageUuid: packageInfo.packageUuid,
      });
      message.success('保存成功');
      onOk?.(packageInfo.processName);
    } catch {

    }
  };

  return (
    <Drawer
      destroyOnClose
      width={600}
      title="配置操控流程包"
      onClose={onCancel}
      footer={
        <div>
          <Space>
            <Button
              onClick={() => {
                modal.remove();
                onOk?.('new Name');
              }}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                await handleOk();
                modal.remove();
              }}
              disabled={!packageInfo}>
              保存
            </Button>
          </Space>
        </div>
      }
      open={modal.visible}
      className="[&_.ant-drawer-body]:!p-0">
      <div className="flex w-full rounded bg-[#ecfcfd] leading-8">
        <IconFont type="iconxinxishuoming2x" className="mr-[5px] pt-[8px] text-[16px]" />
        <h3 className="m-0 text-[14px]">在下方上传需使用的流程包，点击确定后该友商的任务将使用此流程包进行操控</h3>
      </div>
      <div className={'px-[20px]'}>
        <div className="mb-[20px] mt-[10px] h-[10px] text-[14px] font-medium leading-[20px] text-[#334355]">
          上传流程包
        </div>
        <div className="h-[180px]">
          <DragUpload
            accept=".zip"
            multiple={false}
            maxCount={1}
            hint="支持zip格式文件"
            onUploadSuccess={async ({ fileId, ...fileInfo }, options) => {
              try {
                const data = { fileId };
                await runUploadProcessPackage(data);
                // @ts-ignore
                options.onSuccess?.();
              } catch (error) {
                // @ts-ignore
                options.onError?.();
              }
            }}
          />
        </div>
        <div className="mt-[50px]" style={{ display: packageInfo?.processName ? '' : 'none' }}>
          <Card>
            <div className="mb-[8px] flex text-[14px] leading-5">
              <span className="mr-[10px] w-[60px] shrink-0 text-[14px] text-[#334355]">流程名称: </span>
              <span>{packageInfo?.processName}</span>
            </div>
            <div className="mb-[8px] flex text-[14px] leading-5">
              <span className="mr-[10px] w-[60px] shrink-0 text-[14px] text-[#334355]">版本号: </span>
              <span>{packageInfo?.processVersion}</span>
            </div>
            <div className="mb-[8px] flex text-[14px] leading-5">
              <span className="mr-[10px] w-[60px] shrink-0 text-[14px] text-[#334355]">流程简介: </span>
              <span>{packageInfo?.processDescription}</span>
            </div>
          </Card>
        </div>
      </div>
    </Drawer>
  );
};

export const NiceUploadDrawer = NiceModal.create(UploadDrawer);

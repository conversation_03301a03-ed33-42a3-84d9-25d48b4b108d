import { createPromise } from '@/utils/utils';
import { EditOutlined } from '@ant-design/icons';
import NiceModal from '@ebay/nice-modal-react';
import { useRequest } from 'ahooks';
import { Button, Empty, Form, Input, List, message, Spin, Switch } from 'antd';
import type { FC } from 'react';
import { useState } from 'react';
import * as API from '../services';
import { NiceUploadDrawer } from './upload-drawer';
import { BaseFormInstance } from '@deopCmd/types/form';
import { tryit } from '@deopCmd/utils/tryit';
import Ternary from '@deopCmd/components/Ternary';

type FormType = Record<
  string,
  {
    id: string;
    paramConfig: Record<string, string>;
    isControl: number;
    name: string;
    defaultProcessName: string;
  }
>;

const CompetitorConfiguration: FC = () => {
  const [form] = Form.useForm<FormType>() as [BaseFormInstance<FormType>];

  const {
    data: competitorData,
    refresh,
    loading,
  } = useRequest(API.getFriendConfig, {
    onSuccess: (data) => {
      //需要把后端数据转换
      const initialFormValues = data.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {} as FormType);

      form.setFieldsValue(initialFormValues);
    },
  });

  const handleFinish = async () => {
    const values = await form.validateFields();

    const formatValue = Object.entries(values).map(([id, values]) => {
      return {
        id,
        isControl: values.isControl,
        paramConfig: JSON.stringify(values.paramConfig),
      };
    });

    // 现在只有一个 UIPATH
    const UIPATH_VALUE = formatValue[0]!;

    await API.updateFriendConfig(UIPATH_VALUE);
    refresh();

    message.success('保存成功');
  };

  return (
    <Spin spinning={loading}>
      <Ternary condition={Boolean(competitorData && competitorData?.length > 0)} falseElement={<Empty />}>
        <div>
          <Form form={form}>
            {competitorData?.map((item) => {
              return <CardItem item={item} />;
            })}
          </Form>

          <Button className="h-[36px] w-[92px] rounded bg-[#0085ff]" onClick={handleFinish} type="primary">
            保存
          </Button>
        </div>
      </Ternary>
    </Spin>
  );
};

const CardItem = ({ item }: { item: FormType[keyof FormType] }) => {
  return (
    <List className="mb-[20px]" size="large" bordered={true}>
      <div className="mx-[24px] my-0 flex h-[50px] items-center justify-between border-0 border-b border-solid border-b-[#f0f0f0] px-[10px] leading-5 text-[#334355]">
        <Form.Item noStyle>
          <span>{item.name}</span>
        </Form.Item>
        <span className="flex gap-[10px] text-[12px] text-[#8c9aab]">
          开启调度
          <Form.Item name={[item.id, 'isControl']} noStyle>
            <NumberSwitchItem id={item.id} value={item.isControl} />
          </Form.Item>
        </span>
      </div>

      <Form.Item noStyle shouldUpdate={() => true}>
        {({ getFieldValue }) => {
          const isControl = getFieldValue([item.id, 'isControl']);
          if (isControl !== 1) return null;

          return (
            <>
              <div className="pt-[20px]">
                {Object.entries(item.paramConfig)?.map(([label, value]) => {
                  return (
                    <>
                      <Form.Item
                        initialValue={value}
                        key={label}
                        label={label}
                        labelAlign="left"
                        name={[item.id, 'paramConfig', label]}
                        required
                        rules={[{ required: true, message: `请输入${label}!` }]}
                        labelCol={{ span: 3, offset: 2 }}
                        wrapperCol={{ span: 8 }}>
                        <Input />
                      </Form.Item>
                    </>
                  );
                })}
                {
                  <Form.Item
                    labelCol={{ span: 3, offset: 2 }}
                    wrapperCol={{ span: 8 }}
                    label="操控流程包"
                    labelAlign="left"
                  >
                    <ProcessPackageItem id={item.id} initialValue={item.defaultProcessName} />
                  </Form.Item>
                }
              </div>
            </>
          );
        }}
      </Form.Item>
    </List>
  );
};

const NumberSwitchItem = ({ id, value, onChange }: { id: string; value?: number; onChange?: (val: number) => void }) => {
  const defaultChecked = value === 1;

  const handleChange = async (checked: boolean) => {
    const isControl = checked ? 1 : 0;

    await API.updateFriendConfig({ id, isControl });

    message.success('保存成功');

    onChange?.(isControl);
  };

  return <Switch defaultChecked={defaultChecked} onChange={handleChange} />;
};

const ProcessPackageItem = ({ id, initialValue }: { id: string; initialValue: string }) => {
  const [processName, setProcessName] = useState(initialValue);
  return (
    <>
      <span>{processName}</span>
      <span
        className="ml-[20px] cursor-pointer text-[#2f9aff]"
        onClick={async () => {
          const { promise, resolve, reject } = createPromise<string>();
          NiceModal.show(NiceUploadDrawer, {
            firmId: id,
            onOk: (processName) => {
              resolve(processName);
            },
            onCancel: reject,
          });

          const [err, newName] = await tryit(() => promise)();
          if (err) return;

          setProcessName(newName);
        }}>
        <EditOutlined rev="" />
        更改
      </span>
    </>
  );
};

export default CompetitorConfiguration;

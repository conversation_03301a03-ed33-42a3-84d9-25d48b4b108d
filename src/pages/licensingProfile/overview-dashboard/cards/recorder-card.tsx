import React from 'react';
import { EChartsPieOption } from '../../components/pie-chart';
import Card from './card';
import NiceModal from '@ebay/nice-modal-react';
import { AssignAuthDrawer } from '../../common-dashboard/assign-auth-drawer';
import StyleText from '@deopCmd/components/StyleText';
import processRecorderImg from '@/assets/images/tenantsAuthorization/process-recorder.png';
import { IOverviewDashboardCard, TabsEnum } from '@/pages/licensingProfile/types';
import { getChartsData } from '@/pages/licensingProfile/utils';
import { useStoreSelector } from '@/pages/licensingProfile/store';

const ProcessRecorderCard: React.FC<IOverviewDashboardCard> = (props) => {
  const { onUpdate, data, mode, dateStr } = props
  const viewType = useStoreSelector((s) => s.viewerMode);

  // 饼图配置项
  const option: EChartsPieOption = {
    tooltip: {
      trigger: 'item',
      extraCssText: 'width:fit-content',
      formatter: `<div>
    <div style="color:gray;margin-bottom:6px;font-size:12px;">流程记录器授权数据</div>
    {b}:<span style="font-weight:bold"> {c} </span></div>`,
    },

    series: [
      {
        name: '流程记录器授权数据',
        type: 'pie',
        radius: ['50%', '80%'],
        left: 0,
        center: ['44%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 24,
            fontWeight: 'bold',
            formatter: `{c}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: [getChartsData('已分配', '#56D995', data?.assignedCount as number), getChartsData('可分配', '#46A5FD', data?.assignableCount as number)],
      },
    ],
  };

  return (
    <Card
      mode={mode}
      chartOptions={option}
      title={
        <span className="flex items-center gap-2">
          <img src={processRecorderImg} alt="" style={{ width: '32px' }} />
          流程记录器授权
        </span>
      }
      onClick={() => {
        NiceModal.show(AssignAuthDrawer, {
          type: TabsEnum.processRecorder,
          viewType,
          dateStr,
          onClose: onUpdate,
        });
      }}>
      <div className="flex flex-col flex-shrink-0 justify-center items-start gap-3 w-[58%] h-full">
        <div className="flex justify-between items-center w-[100%]">
          <StyleText dot dotColor="#56D995" textColor="#505962">
            已分配
          </StyleText>
          <span className="font-bold">{data?.assignedCount}</span>
        </div>
        <div className="flex justify-between items-center w-[100%]">
          <StyleText dot dotColor="#46A5FD" textColor="#505962">
            可分配
          </StyleText>
          <span className="font-bold">{data?.assignableCount}</span>
        </div>
      </div>
    </Card>
  );
};

export default ProcessRecorderCard;

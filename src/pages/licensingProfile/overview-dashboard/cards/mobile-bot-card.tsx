import React from 'react';
import { EChartsPieOption } from '../../components/pie-chart';
import Card from './card';
import NiceModal from '@ebay/nice-modal-react';
import { AssignAuthDrawer } from '../../common-dashboard/assign-auth-drawer';
import StyleText from '@deopCmd/components/StyleText';
import botImg from '@deopCmd/assets/images/tenantsAuthorization/mobile-bot.png';
import { IOverviewDashboardCardBot, TabsEnum } from '@/pages/licensingProfile/types';
import { getChartsData } from '@/pages/licensingProfile/utils';

/**
 * @since 2025-05-13 手机端机器人授权卡片
 */
const MobileBotCard: React.FC<IOverviewDashboardCardBot> = (props) => {
  const { onUpdate, data, mode, dateStr } = props;

  // 饼图配置项
  const option: EChartsPieOption = {
    tooltip: {
      trigger: 'item',
      extraCssText: 'width:fit-content',
      formatter: `<div>
    <div style="color:gray;margin-bottom:6px;font-size:12px;">机器人授权数据</div>
    {b}:<span style="font-weight:bold"> {c} </span></div>`,
    },

    series: [
      {
        name: '手机机器人授权数据',
        type: 'pie',
        radius: ['40%', '70%'],
        // left: 0,
        // center: [62, '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 24,
            fontWeight: 'bold',
            formatter: `{c}`,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          getChartsData('已分配(固定)', '#56D995', data?.assignedFixedCount as number),
          getChartsData('已分配(动态)', '#46A5FD', data?.assignedAutoCount as number),
          getChartsData('可分配', '#9079FF', data?.assignableCount as number),
        ],
      },
    ],
  };

  return (
    <Card
      type={TabsEnum.bot}
      mode={mode}
      chartOptions={option}
      title={
        <span className="flex items-center gap-2">
          <img src={botImg} alt="" style={{ width: '32px' }} />
          手机机器人授权
        </span>
      }>
      <div className="flex flex-col flex-shrink-0 justify-center items-start gap-3 h-full">
        <div className="flex justify-between items-center w-[160px]">
          <StyleText dot dotColor="#56D995" textColor="#505962">
            已分配(固定)
          </StyleText>
          <span className="font-bold">{data?.assignedFixedCount}</span>
        </div>
        <div className="flex justify-between items-center w-[160px]">
          <StyleText dot dotColor="#46A5FD" textColor="#505962">
            已分配(动态)
          </StyleText>
          <span className="font-bold">{data?.assignedAutoCount}</span>
        </div>
        <div className="flex justify-between items-center w-[160px]">
          <StyleText dot dotColor="#9079FF" textColor="#505962">
            可分配
          </StyleText>
          <span className="font-bold">{data?.assignableCount}</span>
        </div>
      </div>
    </Card>
  );
};

export default MobileBotCard;

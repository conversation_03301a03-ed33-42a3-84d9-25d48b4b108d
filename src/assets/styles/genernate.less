//

/**
//  * @description: 通用less函数批量生成常用样式
//  * <AUTHOR> ba
//  * @date 2021 01 18
//  */

// // 字体大小 从@font-size-start到@font-size-end 相隔1像素
// .generate-font-sizes(@font-size-end);
// .generate-font-sizes(@n, @i: @font-size-start) when (@i =< @n) {
//   .fs-@{i}px {
//     font-size: 0px + @i !important;
//   }
//   .generate-font-sizes(@n, (@i+1));
// }
// .generate-margins(@margin-padding-end);
// .generate-margins(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .m-@{i}px {
//     margin: 0px + @i !important;
//   }
//   .generate-margins(@n, (@i+1));
// }
// .generate-margin-tops(@margin-padding-end);
// .generate-margin-tops(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mt-@{i}px {
//     margin-top: 0px + @i !important;
//   }
//   .generate-margin-tops(@n, (@i+1));
// }
// .generate-margin-bottoms(@margin-padding-end);
// .generate-margin-bottoms(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mb-@{i}px {
//     margin-bottom: 0px + @i !important;
//   }
//   .generate-margin-bottoms(@n, (@i+1));
// }
// .generate-margin-tops-bottoms(@margin-padding-end);
// .generate-margin-tops-bottoms(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mtb-@{i}px {
//     margin-top: 0px + @i !important;
//     margin-bottom: 0px + @i !important;
//   }
//   .generate-margin-tops-bottoms(@n, (@i+1));
// }
// .generate-margin-negative-tops(@margin-padding-end);
// .generate-margin-negative-tops(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mtn-@{i}px {
//     margin-top: 0px - @i !important;
//   }
//   .generate-margin-negative-tops(@n, (@i+1));
// }
// .generate-margin-negative-bottoms(@margin-padding-end);
// .generate-margin-negative-bottoms(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mbn-@{i}px {
//     margin-bottom: 0px - @i !important;
//   }
//   .generate-margin-negative-bottoms(@n, (@i+1));
// }
// .generate-margin-lefts(@margin-padding-end);
// .generate-margin-lefts(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .ml-@{i}px {
//     margin-left: 0px + @i !important;
//   }
//   .generate-margin-lefts(@n, (@i+1));
// }
// .generate-margin-rights(@margin-padding-end);
// .generate-margin-rights(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mr-@{i}px {
//     margin-right: 0px + @i !important;
//   }
//   .generate-margin-rights(@n, (@i+1));
// }
// .generate-margin-lefts-rights(@margin-padding-end);
// .generate-margin-lefts-rights(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mlr-@{i}px {
//     margin-left: 0px + @i !important;
//     margin-right: 0px + @i !important;
//   }
//   .generate-margin-lefts-rights(@n, (@i+1));
// }
// .generate-margin-negative-lefts(@margin-padding-end);
// .generate-margin-negative-lefts(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mln-@{i}px {
//     margin-left: 0px - @i !important;
//   }
//   .generate-margin-negative-lefts(@n, (@i+1));
// }
// .generate-margin-negative-rights(@margin-padding-end);
// .generate-margin-negative-rights(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .mrn-@{i}px {
//     margin-right: 0px - @i !important;
//   }
//   .generate-margin-negative-rights(@n, (@i+1));
// }
// .generate-paddings(@margin-padding-end);
// .generate-paddings(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .p-@{i}px {
//     padding: 0px + @i !important;
//   }
//   .generate-paddings(@n, (@i+1));
// }
// .generate-padding-tops(@margin-padding-end);
// .generate-padding-tops(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .pt-@{i}px {
//     padding-top: 0px + @i !important;
//   }
//   .generate-padding-tops(@n, (@i+1));
// }
// .generate-padding-bottoms(@margin-padding-end);
// .generate-padding-bottoms(@n, @i: @margin-padding-start) when (@i =< @n) {
//   .pb-@{i}px {
//     padding-bottom: 0px + @i !important;
//   }
//   .generate-padding-bottoms(@n, (@i+1));
// }
// // .generate-widths(@width-end);
// // .generate-widths(@n, @i: @width-start) when (@i =< @n) {
// //   .w-@{i}px {
// //     width: 0px + @i !important;
// //   }
// //   .generate-widths(@n, (@i+1));
// // }
// // .generate-heights(@height-end);
// // .generate-heights(@n, @i: @height-start) when (@i =< @n) {
// //   .h-@{i}px {
// //     height: 0px + @i !important;
// //   }
// //   .generate-heights(@n, (@i+1));
// // }
// .generate-line-hegiths(@line-height-end);
// .generate-line-hegiths(@n, @i: @line-height-start) when (@i =< @n) {
//   .lh-@{i}px {
//     line-height: 0px + @i !important;
//   }
//   .generate-line-hegiths(@n, (@i+1));
// }
// .generate-font-weights(@font-weight-end);
// .generate-font-weights(@n, @i: @font-weight-start) when (@i =< @n) {
//   .fw-@{i} {
//     font-weight:  @i !important;
//   }
//   .generate-font-weights(@n, (@i+100));
// }
// .generate-absolute-flexs(@flex-end);
// .generate-absolute-flexs(@n, @i: @flex-start) when (@i =< @n) {
//   .flex-@{i} {
//     flex:  @i !important;
//     overflow: hidden !important;
//   }
//   .generate-absolute-flexs(@n, (@i+1));
// }

@import '~antd/es/style/variable.less';

//  ====================== base主题色 ======================
//  ====================== login page ======================
// 登录页背景色
@login-page-bg-color: var(--login-page-bg-color);
// 系统名称-主题色
@login-systemname-color: var(--login-systemname-color);
// 登录模块名称-一级文本
@login-title-color: var(--first-font-color);
// 登录按钮背景-主题色
@login-btn-bg-color: var(--login-btn-bg-color);
// 登录按钮阴影
@login-btn-shadow-color: var(--login-btn-shadow-color);
// 登录按钮禁用背景
@login-btn-disabled-bg-color: var(--login-btn-disabled-bg-color);

//  ====================== main page ======================
// 系统名称
@mainpage-systemname-color: var(--mainpage-systemname-color);

// 主题色
@main-color: var(--main-color);
// 辅色
@assist-color: var(--assist-color);
// 提示色成功
@success-color: var(--success-color);
// 提示色错误
@error-color: var(--error-color);
// 提示色帮助
@help-color: var(--help-color);
// 提示色提示
@info-color: var(--info-color);
// 提示色标注
@warning-color: var(--warning-color);
// 一级文本标题
@first-font-color: var(--first-font-color);
// 二级文本标题
@second-font-color: var(--second-font-color);
// 三级文本标题
@third-font-color: var(--third-font-color);
// 掩码文本
@masktext-font-color: var(--masktext-font-color);
// 表格操作文本
@table-operate-font-color: var(--table-operate-font-color);

//  ====================== disabled相关-开始  ======================
// 禁用按钮
@disabled-btn-bg-color: var(--light-icon-color);
@disabled-btn-font-color: var(--disabled-btn-font-color);
@disabled-btn-border-color: var(--ant-border-color-base);
// 禁用form表单里的元素
@disabled-formitem-bg-color: var(--piece-bg-color);
@disabled-formitem-font-color: var(--masktext-font-color);
@disabled-formitem-border-color: var(--form-border-color);
//  ====================== disabled相关-结束  ======================

// 原来的颜色定义开始
// 标注
@label-color: var(--label-color);
// 弹窗-删除，按钮色
@delete-btn-color: var(--delete-btn-color);
// 原来的颜色定义开始

//  ======================icon ======================
// 浅色icon
@light-icon-color: var(--light-icon-color);
// 深色icon
@dark-icon-color: var(--dark-icon-color);
// 深色icon hover
@dark-hover-icon-color: var(--dark-hover-icon-color);
// 详情页-可超链接的icon
@detail-entry-icon-color: var(--detail-entry-icon-color);

//  ======================icon ======================
// 表单边框
@form-border-color: var(--form-border-color);
// 分割线
@divider-border-color: var(--divider-border-color);
// 块状背景
@piece-bg-color: var(--piece-bg-color);
// 页面背景
@page-bg-color: var(--page-bg-color);
// 主题操作区背景
@page-operate-bg-color: var(--page-operate-bg-color);

// 内容区边框
@border-line: 1px solid @border-color-base;

//  ======================表格 ======================
@tb-th-bg-color: var(--piece-bg-color);
@tb-th-color: var(--first-font-color);
@tb-td-hover-bg-color: var(--tb-td-hover-bg-color);
@tb-td-color: var(--second-font-color);

//  ====================== 顶部菜单 ======================
@topmenu-collapsed-icon-bg-color: var(--topmenu-collapsed-icon-bg-color);
@layout-main-bg: var(--layout-main-bg);
@layout-main-font-color: var(--layout-main-font-color);
@layout-menu-bg: var(--layout-menu-bg);
@layout-menu-divide-color: var(--layout-menu-divide-color);
@layout-icon-color: var(--layout-icon-color);
@layout-avatar-font-color: var(--layout-avatar-font-color);
@layout-avatar-icon-color: var(--layout-avatar-icon-color);
@layout-box-shadow-color: var(--layout-box-shadow-color);
@menu-collapsed-bg: var(--menu-collapsed-bg);

/** 左侧菜单宽度 */
@menu-width: 208px;
@primary: @primary-color;
@hover-color: @primary-color;
@white: #fff;
@base: #485c77;
@secondary: #7286a2;
@thirdary: #8c9aab;
@basetitle: #334355;
@bgsecondary: #f5faff;
@bgtransparent: transparent;
@disabled-color: #8c9aab;
